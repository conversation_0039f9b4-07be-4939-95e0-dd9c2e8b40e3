<?php

namespace App\Modules\Job\Constants;

class FailedJobCommand
{
    /**
     * Check config/database.php for connection configuration
     * make sure that the value below exists in database.php
     */
    public const QUEUE_RETRY = 'queue:retry';

    public const QUEUE_FLUSH = 'queue:flush';

    public const QUEUE_FORGET = 'queue:forget';

    public const OPTION_ALL = 'all';

    public static function validCommand(string $command)
    {
        return in_array($command, [self::QUEUE_RETRY, self::QUEUE_FLUSH, self::QUEUE_FORGET]) ? true : false;
    }
}
