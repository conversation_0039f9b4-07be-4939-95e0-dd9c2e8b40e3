import React, { useRef, useState } from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import { updateFieldValue, offFilter } from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useOutsideClick from "@/Util/useOutsideClick";

export default function Filter({ routeName, userId, userEmail, onLoading }) {
    const { type, date, email } = route().params ?? {};
    const containerRef = useRef();

    const [userInput, setUserInput] = useState(email || userEmail || "");
    const [filterOrder, setFilterOrder] = useState([]);

    const config = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            type: {
                active: false,
                value: type ? (Array.isArray(type) ? type : [type]) : [],
                type: "option",
                items: [
                    "Sign in", "Register", "Profile update", "Security update",
                    "Contact update", "Category update", "Domain update",
                    "Identity verification", "Payment summary"
                ],
                name: "Last Activity",
            },
            date: {
                active: false,
                value: date ? (Array.isArray(date) ? date : [date]) : [],
                type: "option",
                items: ["today", "yesterday", "last 7 days", "last 30 days"],
                name: "Date",
            },
            user: {
                active: false,
                value: [],
                type: "text",
                name: "Email",
                readonly: false,
                placeholder: "Search by email",
                tempValue: "",
            },
        },
    };

    const [filter, setFilter] = useState(config);

    React.useEffect(() => {
        if (email || userEmail) {
            const emailToUse = email || userEmail;
            handleFieldUpdateValue("user", emailToUse, true);
        }
    }, []);

    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        const { type, date, user } = updatedFilter.field;
        let payload = {};

        if (type?.value?.length > 0) {
            payload.type = type.value.map(t => t.replace(/ /g, "_").toUpperCase());
        }
        if (date?.value?.length > 0) {
            payload.date = date.value;
        }
        if (user?.value?.length > 0) {
            payload.email = user.value[0];
        }

        if (userId && (!user?.value || user.value.length === 0)) {
            router.get(route('client.logs.security.all'), {}, {
                preserveState: false,
                onFinish: () => {
                    onLoading(false);
                },
            });
            return;
        }

        const routeParams = userId ? { user_id: userId } : {};

        router.get(route(routeName, routeParams), payload, {
            preserveState: true,
            preserveScroll: true,
            onFinish: () => {
                onLoading(false);
            },
        });
    };

    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);

        setFilter({
            ...closedFilter,
            ...newObject,
            field: {
                ...closedFilter.field,
                ...newObject.field
            }
        });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        if (key === "user") {
            setUserInput(value);
            
            if (!value || value === userInput) {
                if (value.length === 0 && userId) {
                    const updatedFilter = offFilter({
                        ...filter,
                        container: { ...filter.container, active: false },
                        field: {
                            ...filter.field,
                            user: {
                                ...filter.field.user,
                                active: false,
                                value: [],
                                tempValue: value
                            },
                        },
                    });
                    setFilter(updatedFilter);
                    setFilterOrder(prev => prev.filter(k => k !== key));
                    onLoading(true);
                    router.get(route("client.logs.security.all"), {}, {
                        preserveState: false,
                        onFinish: () => {
                            onLoading(false);
                        }
                    });
                    return;
                }

                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                if (value) {
                    setFilterOrder(prev => {
                        const newOrder = prev.filter(k => k !== key);
                        return [...newOrder, key];
                    });
                } else {
                    setFilterOrder(prev => prev.filter(k => k !== key));
                }
                onLoading(true);
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    user: {
                        ...prevFilter.field.user,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        if (newValue.value.length > 0) {
            setFilterOrder(prev => {
                const newOrder = prev.filter(k => k !== key);
                return [...newOrder, key];
            });
        } else {
            setFilterOrder(prev => prev.filter(k => k !== key));
        }
        if (reload) {
            onLoading(true);
            submit(updatedFilter);
        }
    };

    const orderedField = filterOrder.reduce((acc, key) => {
        if (field[key].value.length > 0 || (field[key].type === 'text' && field[key].tempValue)) {
            acc[key] = field[key];
        }
        return acc;
    }, {});

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={orderedField}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={field}
                />
                {field.type && (
                    <OptionFilter
                        fieldProp={field.type}
                        fieldKey="type"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                    />
                )}
                {field.date && (
                    <OptionFilter
                        fieldProp={field.date}
                        fieldKey="date"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                    />
                )}
                {field.user && (
                    <TextFilter
                        fieldProp={field.user}
                        fieldKey="user"
                        handleFieldUpdateValue={handleFieldUpdateValue}
                        offFilter={() => {
                            const currentValue = field.user.tempValue || field.user.value[0] || "";
                            handleFieldUpdateValue("user", currentValue);
                            setFilter(offFilter(filter));
                        }}
                    />
                )}
            </div>
        </div>
    );
}
