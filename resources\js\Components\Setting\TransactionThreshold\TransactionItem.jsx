//* PACKAGES
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdMode } from "react-icons/md";

//* COMPONENTS
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
// import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";
import { useRef } from 'react';
import Dropdown from '@/Components/Dropdown';
import LoaderSpinner from '@/Components/LoaderSpinner';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function TransactionItem({
    item,
    handleChange,
    handleActionChanged,
    handleReset,
    editAllTrigger,
    handleSetDefaults
}) {
    //! PACKAGE
    //...

    //! HOOKS
    // const { hasPermission } = usePermissions();

    //! VARIABLES
    const ref = useRef();
    //...

    //! STATES
    const [isEditMode, setIsEditMode] = useState(true);
    const [processing, setProcessing] = useState(false);

    //! USE EFFECTS
    useEffect(() => {
        setIsEditMode(editAllTrigger);
    }, [editAllTrigger]);
    //...

    //! FUNCTIONS
    const onHandleChange = (event) => {
        handleChange(item.name, event.target.name, getEventValue(event));
    };

    const action = () => {
        if (item.notify_subscriber && !item.allow_action)
            return "Notify & Reject";
        else if (item.notify_subscriber && item.allow_action)
            return "Notify";
        else if (!item.notify_subscriber && !item.allow_action)
            return "Reject";
        else if (!item.notify_subscriber && item.allow_action)
            return "None";
        return "";
    };

    const formatTransactionName = (str) => {
        return str.toLowerCase().replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
    }

    const toggleEditMode = () => {
        setIsEditMode(!isEditMode);
        handleReset(item.name);
    };

    // const handleUpdate = () => {
    //     router.patch(route("setting.transaction.threshold-update"), {
    //         transaction_id: item.transaction_id,
    //         name: item.name,
    //         system_limit: item.system_limit,
    //         user_limit: item.user_limit,
    //         length: item.length,
    //         notify_subscriber: item.notify_subscriber,
    //         allow_action: item.allow_action,
    //     });
    // }

    const handleUpdate = async () => {
        setProcessing(true);

        const response = await axios.patch(route("setting.transaction.threshold-update"), {
            transaction_id: item.transaction_id,
            name: item.name,
            system_limit: item.system_limit,
            user_limit: item.user_limit,
            length: item.length,
            notify_subscriber: item.notify_subscriber,
            allow_action: item.allow_action,
        });

        if (response.data.success) {
            toast.success(response.data.message);
            setProcessing(false);
            handleSetDefaults(item.name);
            setIsEditMode(false);
        }
    };

    return (
        <tr className="hover:bg-gray-100">
            <td className="w-1/5 py-4 px-3">
                <span>
                    {formatTransactionName(item.name)}
                </span>
            </td>
            <td className="w-1/5 py-2 px-3">
                {isEditMode ? (
                    <TextInput
                        type="number"
                        name="system_limit"
                        placeholder="0"
                        value={item.system_limit}
                        handleChange={onHandleChange}
                        className="w-1/2 text-sm py-1"
                    />
                ) : (
                    <span>{item.system_limit}</span>
                )}
            </td>
            <td className="w-1/5 py-2 px-3">
                {isEditMode ? (
                    <TextInput
                        type="number"
                        name="user_limit"
                        placeholder="0"
                        value={item.user_limit}
                        handleChange={onHandleChange}
                        className="w-1/2 text-sm py-1"
                    />
                ) : (
                    <span>{item.user_limit}</span>
                )}
            </td>
            <td className="w-1/5 py-2 px-3">
                {isEditMode ? (
                    <TextInput
                        type="number"
                        name="length"
                        placeholder="0"
                        value={item.length}
                        handleChange={onHandleChange}
                        className="w-1/2 text-sm py-1"
                    />
                ) : (
                    <span>{item.length}</span>
                )}
            </td>
            <td className="w-1/5 py-2 px-3">
                {isEditMode ? (
                    <div>
                        <Dropdown>
                            <Dropdown.Trigger>
                                <span className="inline-flex rounded-md">
                                    <button
                                        type="button"
                                        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 rounded-md bg-white hover:text-gray-700 focus:outline-none transition ease-in-out duration-150"
                                    >
                                        {action()}

                                        <svg
                                            className="ml-2 -mr-0.5 h-4 w-4"
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path
                                                fillRule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clipRule="evenodd"
                                            />
                                        </svg>
                                    </button>
                                </span>
                            </Dropdown.Trigger>

                            <Dropdown.Content align='left' width='w-40'>
                                {["Notify", "Reject", "Notify & Reject", "None"]
                                    .filter(label => label !== action())
                                    .map(label => (
                                        <Dropdown.Button
                                            key={label}
                                            handleClick={() => handleActionChanged(item.name, label)}
                                        >
                                            {label}
                                        </Dropdown.Button>
                                    ))}
                            </Dropdown.Content>
                        </Dropdown>
                    </div>
                ) : (
                    <span>{action()}</span>
                )}
            </td>
            <td className='py-2 px-2'>
                <span className="flex justify-center">

                    {isEditMode ? (
                        <div>
                            <span className="flex justify-end space-x-1">
                                {processing ? (
                                    <div className="flex space-x-2 justify-center items-center">
                                        <LoaderSpinner h="h-4" w="w-4" /> <span className='text-gray-700 text-xs'>Updating...</span>
                                    </div>
                                ) : (
                                    <div className="flex space-x-2">
                                        <button
                                            className={`text-xs rounded-md border bg-primary text-white border-transparent hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150 px-4 py-1`}
                                            disabled={processing}
                                            onClick={handleUpdate}
                                        >
                                            Update
                                        </button>

                                        <button
                                            className={`flex items-center text-xs rounded-md border px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150`}
                                            disabled={processing}
                                            onClick={toggleEditMode}
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                )}
                            </span>
                        </div>

                    ) : (
                        <button
                            className="inline-flex items-center space-x-1 text-gray-500 hover:text-primary"
                            onClick={toggleEditMode}
                        >
                            <MdMode />
                            <span>Edit</span>
                        </button>
                    )}

                </span>
            </td>
        </tr>
    );
}
