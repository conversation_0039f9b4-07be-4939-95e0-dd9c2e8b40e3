<?php

namespace App\Providers;

use App\Modules\SecretManager\ManualSecretManagerImplementatation;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        DB::macro('client', function () {
            return DB::connection('client');
        });

        $secretManager = new ManualSecretManagerImplementatation();
        $APP_KEY = $secretManager->getMeta('REGISTRY_API_URL');
        $REGISTRY_SECRET_KEY = $secretManager->getPayload('REGISTRY_API_URL_SECRET_KEY');

        Http::macro('registry', function () use ($APP_KEY, $REGISTRY_SECRET_KEY) {
            return Http::baseUrl($APP_KEY)->withToken($REGISTRY_SECRET_KEY);
        });

        Http::macro('client', function () {
            return Http::baseUrl(env('CLIENT_API_URL'));
        });
    }
}
