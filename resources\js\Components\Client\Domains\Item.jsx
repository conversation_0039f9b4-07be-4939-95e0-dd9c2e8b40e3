import "react-toastify/dist/ReactToastify.css";
import getRecentTime from "@/Util/getRecentTime";
import getRemainingTime from "@/Util/getRemainingTime";
import convertToDatetime from "@/Util/convertToDatetime";
import { useState, useRef } from "react";
import { MdMoreVert } from "react-icons/md";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { router } from "@inertiajs/react";

export default function Item({ item, dateTimeFormat, }) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleView = () => {
        router.get(route("domain.history.show", { id: item.domain_id }));
    };

    const formatDateTime = (date, format) => {
        switch (format) {
            case 1:
                return <span>{new Date(date).toDateString()}</span>;
            case 2:
                if (Date.now() > date)
                    return (
                        <span className="font-semibold text-red-500">
                            {getRecentTime(date, true)}
                        </span>
                    );
                return (
                    <span className="font-semibold text-green-500">
                        {getRemainingTime(date, true)}
                    </span>
                );
            case 3:
                return <span>{convertToDatetime(date)}</span>;
            case 4:
                return <span>{convertToDatetime(date, false, true)}</span>;
            default:
                return <span>{new Date(date).toDateString()}</span>;
        }
    };

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <span className=" font-semibold pl-2">{item.domain_name}</span>
            </td>
            <td>{formatDateTime(item.expiry, dateTimeFormat)}</td>
            <td>
                <span>{new Date(item.created_at + "Z").toDateString()}</span>
            </td>
            <td>
                <span>{item.domainStatus}</span>
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <button
                            className="hover:bg-gray-100 px-5 py-1"
                            onClick={handleView}
                        >
                            View Logs
                        </button>
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
