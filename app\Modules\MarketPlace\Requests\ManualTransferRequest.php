<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class ManualTransferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'auth' => 'required|string',
            'order_id' => 'required'
        ];
    }

    public function storeAuth() : void
    {
        DB::table('public.market_domains_manual')->insert(['name' => $this->name, 'auth' => $this->auth]);
        DB::table('public.market_place_domains')->where('order_id', $this->order_id)->update(['epp_error' => 'REJECTED_SUBMITTED']);
    }
}
