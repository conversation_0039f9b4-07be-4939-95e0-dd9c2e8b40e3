<?php

namespace App\Modules\Activity\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;

use Illuminate\Support\Facades\File;

class AdminActivityService
{
    private static $logPath = 'admin/admin';

    public static function get(int $day)
    {
        if ($day < 1) {
            $logFilePath = self::$logPath . '.log';
        } else {
            $logFilePath = self::$logPath . '-' . Carbon::now()->subDays($day - 1)->toDateString() . '.log';
        }
        return Storage::has($logFilePath) ? Storage::get($logFilePath) : '';
    }

    public static function getActivityLogs()
    {
        $logFilePath = self::$logPath . '.log';

        // Get contents as string
        $contents = Storage::exists($logFilePath) ? Storage::get($logFilePath) : '';

        // Convert to collection of lines
        //$lines = collect(explode(PHP_EOL, $contents));

        // Parse each line (no filters at all)
        $lines = collect(explode("\n", $contents)); // Or however you're getting your lines

        $logs = $lines
            ->map(function ($line) {
                if (empty(trim($line))) {
                    return null; // skip empty lines
                }

                // Use named capture groups
                preg_match('/\[(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] (?P<level>local\.(INFO|ERROR))\: (?P<message>.*)/', $line, $matches);

                if (empty($matches)) {
                    return null; // skip lines that don't match
                }

                $dateTime = isset($matches['timestamp']) ? Carbon::parse($matches['timestamp']) : null;
                $level = $matches['level'] ?? '';
                $message = $matches['message'] ?? '';

                // If dateTime is not valid, return null or a default value
                if (!$dateTime) {
                    return null;
                }
                // Get the formatted date
                $now = Carbon::now();
                $formattedDate = strtoupper($dateTime->format('M d, Y'));

                // Handle "X DAYS AGO"
                if (preg_match('/(\d+\.?\d*)\s+DAYS\s+AGO/', $formattedDate, $matches)) {
                    $daysAgo = (int) floatval($matches[1]);  // Cast to integer to remove decimals
                    // Calculate the actual date from 'X DAYS AGO'
                    $dateLabel = Carbon::now()->subDays($daysAgo)->format('M d, Y');
                    $dateLabel = "{$daysAgo} DAYS AGO ({$dateLabel})";
                } elseif ($dateTime->isToday()) {
                    $dateLabel = "TODAY ({$formattedDate})";
                } elseif ($dateTime->isYesterday()) {
                    $dateLabel = "YESTERDAY ({$formattedDate})";
                } elseif ($dateTime->lt($now)) {
                    $daysDiff = (int) floatval($dateTime->diffInDays($now));
                    if ($daysDiff <= 6) {
                        $dateLabel = "{$daysDiff} DAYS AGO ({$formattedDate})";
                    } else {
                        $dateLabel = $formattedDate;
                    }
                } else {
                    // For future dates
                    $dateLabel = $formattedDate;
                }

                return [
                    'dateLabel' => $dateLabel,
                    'icon' => 'clock', // You can replace this with an icon if needed
                    'title' => ucfirst(explode('.', $level)[1] ?? 'Log'),
                    'description' => $message,
                    'time' => $dateTime->format('g:i A'), // Add time to the log
                    'dateTime' => $formattedDate,
                ];
            })
            ->filter() // remove nulls
            ->sortByDesc(function ($log) {
                // Ensure that the log has a valid dateLabel for sorting
                return Carbon::parse($log['dateTime'] ?? now());
            })
            ->values();

        return response()->json($logs);
    }
}
