import React from "react";

function DomainRefund({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div
            className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500"
            style={{
                fontFamily:
                    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
            }}
        >
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">Greetings!</p>
                <p className="mt-4">
                    We are pleased to inform you that the refund process for the
                    of <br />
                    <strong>{data.domain}</strong> has been initiated. The
                    refunded amount totals{" "}
                    <strong>${data.refunded_amount}</strong>.
                </p>
                <p className="mt-2">
                    It can take approximately 10 days to appear on your
                    statement. If it takes longer, please contact your bank for
                    assistance. For other questions or concerns, please don't
                    hesitate to reach out.
                </p>

                <p className="mt-4">
                    <a href={data.redirect_url}>View Refund Details</a>
                </p>

                <p className="font-bold mt-4">SUMMARY</p>
                <div className="border-b mt-2"></div>
                <p className="mt-2">Invoice #: {data.other_data.invoice_num}</p>
                <p className="mt-2">Date: {data.other_data.date}</p>

                 <table className="w-full mt-4 border-collapse">
                    <thead>
                        <tr className="border-b">
                            <th className="text-left py-2">Domain</th>
                            <th className="text-left py-2">Type</th>
                            <th className="text-left py-2">Refunded Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td className="py-2">{data.domain}</td>
                            <td className="py-2">{data.refund_type}</td>
                            <td className="py-2">${data.refunded_amount}</td>
                        </tr>
                    </tbody>
                </table>

                <p className="mt-4">For more information, please see StrangeDomains's <a href={data.refund_policy_url}>Refund Policy</a>.</p>

                <p className="mt-4 font-bold mb-2">Sincerely,</p>
                <p className="font-bold">Strange Domains</p>
            </div>
        </div>
    );
}

export default DomainRefund;
