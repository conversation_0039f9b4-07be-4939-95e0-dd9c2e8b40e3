import { useEffect, useRef, useState } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import { MdMoreVert } from "react-icons/md";
import useOutsideClick from "@/Util/useOutsideClick";
import { Link } from "@inertiajs/react";
export default function ContactItems({ item }) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <div className="flex ">
                    <div className="truncate max-w-[8rem] " title={item.name}>
                        <span>{item.contact.name}</span>
                    </div>
                    {item.is_primary && (
                        <span className="w-2 flex-none pl-2 font-semibold text-primary border border-transparent  text-xs  ">
                            Primary
                        </span>
                    )}
                </div>
            </td>
            <td>
                <span>{item.registry.name}</span>
            </td>
            <td>
                <span>{item.contact.contact_id}</span>
            </td>

            <td>
                <div className="truncate max-w-[8rem]" title={item.email}>
                    <span>{item.contact.email}</span>
                </div>
            </td>
            <td>
                <span>{item.contact.voice_phone}</span>
            </td>
            <td>
                <span>{item.contact.state_province}</span>
            </td>
            <td>
                <span>
                    {item.contact.country_code} {item.postal_code}
                </span>
            </td>
            <td>
                <span>{item.contact.status}</span>
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <div className="hover:bg-gray-100 px-5 py-1">
                            <Link
                                href={route("contact.show", { id: item.id })}
                                method="get"
                                as="button"
                                type="button"
                            >
                                <span>Show all</span>
                            </Link>
                        </div>

                        <span
                            className={`hover:bg-gray-100 px-5 py-1 ${
                                item.is_primary && "hidden"
                            }`}
                        >
                            <Link
                                as="button"
                                href={route("contact.setPrimary", {
                                    id: item.id,
                                })}
                                method="patch"
                            >
                                Set as primary
                            </Link>
                        </span>
                        <span className="hover:bg-gray-100 px-5 py-1">
                            <Link
                                as="button"
                                href={route("contact.edit", {
                                    id: item.contact_id,
                                })}
                                method="get"
                            >
                                Edit
                            </Link>
                        </span>
                        {/* <span className="hover:bg-gray-100 px-5 py-1">
                            <Link
                                as="button"
                                href={route("contact.edit-authentication", {
                                    id: item.id,
                                })}
                                method="get"
                            >
                                Change authentication
                            </Link>
                        </span> */}
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
