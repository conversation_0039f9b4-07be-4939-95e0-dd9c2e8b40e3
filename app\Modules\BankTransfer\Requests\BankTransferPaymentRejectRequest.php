<?php

namespace App\Modules\BankTransfer\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class BankTransferPaymentRejectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'paidAmount' => ['required', 'string'],
            'note'       => ['required', 'string', 'max:1000'], 
            'purpose'    => ['required', 'string']
        ];
    }
}
