import EditWireTransferForm from "../../Components/WireTransfer/EditWireTransferForm";
import AdminLayout from "../../Layouts/AdminLayout";


export default function Edit({item, action = 'verify' }) {

    return (
        <AdminLayout hideNav={true} postRouteName={'billing.wire.transfer.verify-edit'}>
            <div className="mx-auto container max-w-[900px] mt-20 space-x-5">
                <div className=" flex flex-col space-y-12 divide-y pb-10">
                    <div className=" space-y-2">
                        <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                            <div className="pt-2">
                                {/* <span className="text-2xl font-semibold">Funding Wire Transfer</span> */}
                            </div>
                        </div>
                        <EditWireTransferForm
                        item={item}
                        action={action}/>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
