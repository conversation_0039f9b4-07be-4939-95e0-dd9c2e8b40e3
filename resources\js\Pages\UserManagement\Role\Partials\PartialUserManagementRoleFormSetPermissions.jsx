//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import InputError from '@/Components/InputError';
import UserManagementPermissionsReviewModalComponent from '@/Components/UserManagement/UserManagementPermissionsReviewModalComponent';
import UserManagementSetPermissionsTabComponent from '@/Components/UserManagement/UserManagementSetPermissionsTabComponent';

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserManagementRoleFormSetPermissions(
    {
        //! VARIABLES 
        formMode='create',
        activePermissions, 
        allPermissions, 
        categories, 
        categoryPermissions,

        //! STATES
        stateInputName,
        setStateInputName,
        stateInputSelectedPermissions,
        setStateInputSelectedPermissions,
        stateErrorMessageName,
        stateErrorMessagePermissions,
        stateShouldSyncPermissions = false,
        setStateShouldSyncPermissions,

        //! EVENTS
        handleEventSubmit = () => alert('submit')
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    const [stateIsActiveModalReviewPermissions, setStateIsActiveModalReviewPermissions] = useState(false);
    const [stateActiveCategoryTabs, setStateActiveCategoryTabs]                         = useState(categories.map(item => item.name));

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleToggleCategory(category)
    {
        if (stateActiveCategoryTabs.includes(category))
        {
            setStateActiveCategoryTabs(stateActiveCategoryTabs.filter(item => item != category));
        }
        else 
        {
            setStateActiveCategoryTabs([...stateActiveCategoryTabs, category]);
        }
    }
    
    function handleCheckPermission(permissionId)
    {
        if (stateInputSelectedPermissions.includes(permissionId))
        {
            setStateInputSelectedPermissions(stateInputSelectedPermissions.filter(permission => permission != permissionId));
        }
        else 
        {
            setStateInputSelectedPermissions([...stateInputSelectedPermissions, permissionId]);
        }
    };

    function handleCheckAllPermissions(isChecked)
    {
        setStateInputSelectedPermissions([]);

        if (isChecked)
        {
            setStateInputSelectedPermissions(activePermissions.map(permission => permission.permissionId))
        }
    }

    function handleCheckAllByCategory(categoryId, isChecked)
    {
        const permissions = categoryPermissions
            .filter(categoryPermission => categoryPermission.categoryId == categoryId)
            .map(categoryPermission => categoryPermission.permissionId);

        setStateInputSelectedPermissions(
            prev =>
            {
                if (isChecked)
                {
                    const newPermissions = permissions.filter(id => !prev.includes(id));

                    return [...prev, ...newPermissions];
                }
                else
                {
                    return prev.filter(id => !permissions.includes(id));
                }
            }
        );
    }

    return (
        <>
            <UserManagementPermissionsReviewModalComponent
                stateIsModalOpen={stateIsActiveModalReviewPermissions}
                selectedPermissions={allPermissions.filter(p => stateInputSelectedPermissions.includes(p.id))}
                handleEventModalClose={
                    () =>
                    {
                        setStateIsActiveModalReviewPermissions(false);
                    }
                }
                handleEventModalConfirm={
                    () => 
                    {
                        handleEventSubmit(); 
                        setStateIsActiveModalReviewPermissions(false);
                    }
                }
            />
            <div
                className='flex flex-col gap-5'
            >
                <section
                    className='flex flex-col gap-4'
                >
                    <div 
                        className='flex flex-col lg:w-[50%] gap-2'
                    >
                        <label htmlFor="name">
                            Role Name
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={stateInputName}
                            className='p-4 border border-gray-300 rounded-lg h-[40px]'
                            placeholder='Admin User'
                            onChange={(e) => { setStateInputName(e.target.value) }}
                        />
                        <InputError
                            message={stateErrorMessageName}
                        />
                    </div>
                    {
                        formMode == 'update' 
                            ?
                                <AppInputCheckboxComponent
                                    id={`syncPermissions`}
                                    name={`syncPermissions}`}
                                    label={'Sync Permissions of Users assigned to this role'}
                                    isChecked={stateShouldSyncPermissions}
                                    handleEventOnChange={(e) => setStateShouldSyncPermissions(e.target.checked)}
                                />
                            :
                                null
                    }
                </section>
                
                <hr/>

                <section
                    className='flex flex-col gap-5'
                >
                    <div
                        className={`
                            grid grid-cols-1 gap-4  md:grid-cols-2 xl:grid-cols-3 items-center
                            border py-4 px-4 rounded-lg
                        `}
                    >
                        <div 
                            className='text-lg col-span-2 font-semibold'
                        >
                            Permissions | {stateInputSelectedPermissions.length} selected
                        </div>
                        {
                            <AppInputCheckboxComponent
                                id={`permissionAll`}
                                name={`permissionAll}`}
                                label={'Select All'}
                                isChecked={activePermissions.every(p => stateInputSelectedPermissions.includes(p.permissionId))}
                                handleEventOnChange={(e) => handleCheckAllPermissions(e.target.checked)}
                            />
                        }
                    </div>

                    <div
                        className='flex flex-col gap-4'
                    >
                        {
                            categories.map(
                                (category, categoryIndex) => 
                                {
                                    return (
                                        <UserManagementSetPermissionsTabComponent
                                            key={categoryIndex}
                                            category={category} 
                                            categoryPermissions={categoryPermissions.filter(categoryPermission => categoryPermission.categoryId == category.id)}
                                            stateActiveCategoryTabs={stateActiveCategoryTabs}
                                            stateInputSelectedPermissions={stateInputSelectedPermissions}
                                            handleToggleCategory={handleToggleCategory} 
                                            handleCheckPermission={handleCheckPermission} 
                                            handleCheckAllByCategory={handleCheckAllByCategory}
                                        />
                                    );
                                }
                            )
                        }
                    </div>

                    <InputError
                        message={stateErrorMessagePermissions}
                    />
                </section>
                    
                <section
                    className='flex justify-end gap-2 w-full'
                >
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        isDisabled={stateInputSelectedPermissions.length == 0 || stateInputName.length == 0}
                        handleEventClick={() => setStateIsActiveModalReviewPermissions(true)}
                    >
                        Review
                    </AppButtonComponent>
                </section>
            </div>
        </>
    )
}
