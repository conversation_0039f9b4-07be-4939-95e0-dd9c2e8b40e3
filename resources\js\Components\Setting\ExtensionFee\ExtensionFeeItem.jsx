//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdMode, MdOutlineWarning } from "react-icons/md";

//* COMPONENTS
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function ExtensionFeeItem(
    {
        item,
        extension_type
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);
    const [updateValue, setUpdateValue] = useState(item.value);
    const [itemValue, setItemValue] = useState(item.value); 

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const onHandleChange = (event) => {
        setUpdateValue(getEventValue(event));
    };

    const handleUpdate = () => {
        let value = null;
        if (updateValue === "") {
            value = 0;
            setUpdateValue(value);
        }
        else value = updateValue;

        setShow(false);

        var total = parseFloat(item.fee_value) + parseFloat(value);
        if (total <= 0) { // no negative value
            toast.error("Adjustment Not Accepted. Total Fee of: " + total.toFixed(2));
        } else {
            router.patch(route("setting.extension.fee-update"), {
                id: item.id,
                extension: item.extension_name,
                type: item.type,
                value: value,
                extension_type: extension_type,
                extension_total: parseFloat(item.fee_value),
                is_default: item.is_default,
            }, {
                onError: (errors) => toast.error(errors.message, { autoClose: 8000 }),
                onSuccess: () => {
                    toast.success("Updated " + item.nameType.toLowerCase() + " fee for ." + item.extension_name);
                    setItemValue(value);
                },
            });
        }
    };

    const getTotal = () => {
        return parseFloat(item.fee_value) + parseFloat(item.value);
    }

    const totalFee = getTotal();

    return (
        <tr className="hover:bg-gray-100">
            <td className="w-1/5">
                <label className="flex items-center pl-2 space-x-2">
                    <span className=" font-semibold cursor-pointer">
                        {item.nameType}
                    </span>
                </label>
            </td>
            <td className="w-1/5">
                <span className=" lowercase">${parseFloat(item.fee_value).toFixed(2)}</span>
            </td>
            <td className="w-1/5">
                <div className={`${show && "hidden"}`}>
                    <span className="font-semibold">${parseFloat(itemValue).toFixed(2)}</span>
                </div>
                <div className={`${!show && "hidden"}`}>
                    <TextInput
                        type="number"
                        name="name"
                        placeholder="new value"
                        value={updateValue}
                        handleChange={onHandleChange}
                    />
                    {/* <span className="inline-flex items-center text-gray-500 hover:text-primary cursor-pointer">
                        <MdOutlinePendingActions /> draft
                    </span> */}
                </div>
            </td>
            <td className="w-1/5">
                <span>${totalFee.toFixed(2)}</span>
            </td>
            <td className="w-1/5">
                <div
                    className="flex justify-between py-4"
                >
                    {
                        hasPermission("setting.general-update") 
                            ?
                                !show 
                                    ?
                                        (
                                            <button
                                                className="inline-flex items-center text-gray-500 hover:text-primary"
                                                onClick={() => setShow(!show)}
                                            >
                                                <MdMode /> Edit
                                            </button>
                                        ) 
                                    : 
                                        (
                                            <div className="inline-flex space-x-4">
                                                <PrimaryButton onClick={() => handleUpdate()}>
                                                    Save
                                                </PrimaryButton>
                                                <SecondaryButton onClick={() => setShow(false)}>
                                                    Cancel
                                                </SecondaryButton>
                                            </div>
                                        )
                                    : 
                                        <div
                                            className='flex gap-2 items-center text-danger font-medium'
                                        >
                                            <MdOutlineWarning
                                                className='h-5 w-5'
                                            />
                                            <span>
                                                No Permitted Actions
                                            </span>
                                        </div>
                    }
                </div>
            </td>

        </tr>
    );
}
