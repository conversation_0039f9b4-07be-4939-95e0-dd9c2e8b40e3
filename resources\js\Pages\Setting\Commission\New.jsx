import InputLabel from "@/Components/InputLabel";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import AdminLayout from "@/Layouts/AdminLayout";
import { useForm } from "@inertiajs/react";
import { MdKeyboardBackspace } from "react-icons/md";
import DropDownContainer from "@/Components/DropDownContainer";
import { useRef, useState } from "react";
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
export default function New({ fees }) {
    const {
        data,
        setData,
        post,
        processing,
        setError,
        errors,
        transform,
        reset,
    } = useForm({
        id: 0,
        type: "",
        minimum_domain: 1,
        fix_rate: 0,
        percent_rate: 0,
    });

    const [show, setShow] = useState(false);
    const ref = useRef();
    useOutsideClick(ref, () => {
        setShow(false);
    });

    const onHandleChange = (event) => {
        setData(event.target.name, getEventValue(event));
    };

    const handleSelectType = (type) => {
        setData({ ...data, type: type, id: fees[type].id });

        setShow(false);
    };
    const submit = async (e) => {
        e.preventDefault();
        post(route("setting.commission-store"));
    };

    return (
        <AdminLayout hideNav={true}>
            <div className="mx-auto container max-w-[300px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                    <a href="#" onClick={() => window.history.back()}>
                        <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                    </a>
                    <span className=" text-inherit">
                        New commission setting
                    </span>
                </div>

                {errors.message != undefined && (
                    <span className=" text-danger ">{errors.message}</span>
                )}

                <form onSubmit={submit} className="flex flex-col space-y-4">
                    <div ref={ref} className="relative">
                        <InputLabel forInput="email" value="Commission type" />

                        <div className="flex flex-col items-start">
                            <input
                                placeholder="select type"
                                type="text"
                                name="typee"
                                value={data.type}
                                onFocus={(e) => setShow(true)}
                                className={`border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-md shadow-sm placeholder-gray-200 w-full`}
                                onChange={() => {}}
                            />
                        </div>
                        <DropDownContainer
                            show={show}
                            className=" left-0 min-w-full "
                        >
                            {Object.keys(fees).map((key) => {
                                return (
                                    <button
                                        key={"ddf-" + key}
                                        type="button"
                                        className="hover:bg-gray-100 px-5 py-1 text-left"
                                        onClick={() => handleSelectType(key)}
                                    >
                                        {key}
                                    </button>
                                );
                            })}
                        </DropDownContainer>
                        <InputError message={errors.type} className="mt-2" />
                    </div>
                    <div>
                        <InputLabel
                            forInput="minimum_domain"
                            value="Minimum domain"
                        />

                        <TextInput
                            type="number"
                            name="minimum_domain"
                            placeholder="minimum domain"
                            className="mt-1 block w-full"
                            autoComplete="minimum_domain"
                            value={data.minimum_domain}
                            handleChange={onHandleChange}
                        />
                        <InputError
                            message={errors.minimum_domain}
                            className="mt-2"
                        />
                    </div>
                    <div>
                        <InputLabel
                            forInput="percent_rate"
                            value="percentage commission"
                        />

                        <TextInput
                            type="number"
                            name="percent_rate"
                            placeholder="%"
                            className="mt-1 block w-full"
                            autoComplete="percent_rate-password"
                            value={data.percent_rate}
                            handleChange={onHandleChange}
                        />
                        <InputError
                            message={errors.percent_rate}
                            className="mt-2"
                        />
                    </div>

                    <div>
                        <InputLabel
                            forInput="fix_rate"
                            value="fix rate commission"
                        />

                        <TextInput
                            type="number"
                            name="fix_rate"
                            placeholder="$"
                            className="mt-1 block w-full"
                            autoComplete="percent_rate-password"
                            value={data.fix_rate}
                            handleChange={onHandleChange}
                        />
                        <InputError
                            message={errors.fix_rate}
                            className="mt-2"
                        />
                    </div>
                    <div className="pt-4">
                        <PrimaryButton
                            className="w-full p-t"
                            // onClick={handleSendRequest}
                            // type="button"
                            processing={data.id == 0}
                        >
                            Create commission setting
                        </PrimaryButton>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
