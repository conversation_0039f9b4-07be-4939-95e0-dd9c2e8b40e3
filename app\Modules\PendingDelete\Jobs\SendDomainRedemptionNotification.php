<?php

namespace App\Modules\PendingDelete\Jobs;

use App\Mail\DomainRedemptionPeriodMail;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Constant\QueueConnection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Exception;
use Throwable;

class SendDomainRedemptionNotification implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $domainsByEmail;

    public $tries = 3;

    public $timeout = 120;

    public function __construct(array $domainsByEmail)
    {
        $this->domainsByEmail = $domainsByEmail;
        
        $this->onConnection(QueueConnection::EMAIL);
        $this->onQueue('default');
    }

    public $uniqueFor = 3600;

    public function uniqueId(): string
    {
        $emails = array_keys($this->domainsByEmail);
        sort($emails);
        return 'domain_redemption_notification_' . md5(implode(',', $emails));
    }

    public function handle(): void
    {
        try {
            foreach ($this->domainsByEmail as $email => $userDomains) {
                $domainNames = array_map(function($domain) {
                    return $domain->name;
                }, $userDomains);

                $payload = [
                    'subject' => 'Domain Redemption Period',
                    'greeting' => 'Greetings!',
                    'body' => "We noticed that your domain(s) " . implode(', ', $domainNames) . " have entered the redemption period. This means the domain registration has expired and is now in a grace period where you can still restore it.",
                    'body2' => "If you wish to retain ownership of these domains, please initiate a domain restore as soon as possible.",
                    'redirect_url' => route('domain-redemption.view'),
                    'sender' => config('mail.from.sd_name'),
                    'domains' => $userDomains
                ];

                Mail::to($email)->send(new DomainRedemptionPeriodMail($payload));
                $this->emailTrack($email, $payload, $userDomains[0]); // Use first domain to get user info
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error('SendDomainRedemptionNotification failed: ' . $e->getMessage());
            throw $e;
        }
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error('SendDomainRedemptionNotification job failed: ' . $exception->getMessage());
    }

    public function backoff(): array
    {
        return [30, 60, 120];
    }

    private function emailTrack($email, array $payload, $domain){
        $userName = trim(($domain->first_name ?? '') . ' ' . ($domain->last_name ?? ''));
        if (empty($userName)) {
            $userName = 'System';
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $domain->user_id,
                'name' => $userName,
                'recipient_email' => $email,
                'subject' => 'Domain Redemption Period',
                'email_type' => 'Domain Redemption Period',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

}
