<?php

namespace App\Util\Helper;

use Illuminate\Support\Facades\RateLimiter;

class RateLimit
{
    public static function attempt(string $key, int $attempt = 3, int $decay = 300)
    {
        $executed = RateLimiter::attempt(
            $key,
            $attempt,
            function () {
                return true;
            }
        );

        return $executed ? true : false;
    }

    public static function clear(string $key)
    {
        RateLimiter::clear($key);
    }
}
