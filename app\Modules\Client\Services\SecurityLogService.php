<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\DB;
use App\Modules\Client\Constants\ClientStatus;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Traits\GroupDateFormatting;

class SecurityLogService
{
    use GroupDateFormatting;

    private const PAGE_LIMIT = 20;

    public function getSecurityLogsData(?int $userId, Request $request): array
    {
        $query = $this->buildBaseQuery($userId);
        $this->applyFilters($query, $request);
        
        $limit = $request->input('limit', 20);
        
        $logs = $query->paginate($limit)->appends($request->query());
        $formattedLogs = $this->formatLogs($logs);

        return [
            'email' => $userId ? $this->getUserEmail($userId) : null,
            'user_id' => $userId,
            'logs' => $formattedLogs->items(),
            'pagination' => $this->formatPagination($formattedLogs),
            'statuses' => ClientStatus::getAllStatuses()
        ];
    }

    private function getUserEmail(int $userId): ?string
    {
        return DB::table('public.users')
            ->where('id', $userId)
            ->value('email');
    }

    private function buildBaseQuery(?int $userId): Builder
    {
        $query = $this->getBaseQuery();
        
        if ($userId) {
            $query->where('uth.user_id', $userId);
        }
        
        return $query;
    }

    private function getBaseQuery(): Builder
    {
        return DB::table('public.user_transaction_histories as uth')
            ->join('public.users', 'uth.user_id', '=', 'users.id')
            ->select(
                'uth.id',
                'uth.user_id',
                'users.email',
                'uth.created_at',
                'uth.type',
                'uth.message'
            )
            ->orderBy('uth.id', 'desc');
    }

    private function applyFilters(Builder $query, Request $request): void
    {
        if ($request->has('date')) {
            $dates = is_array($request->input('date')) ? $request->input('date') : [$request->input('date')];
            $query->where(function ($query) use ($dates) {
                foreach ($dates as $index => $dateFilter) {
                    if ($index === 0) {
                        $this->applyDateFilter($query, $dateFilter);
                    } else {
                        $query->orWhere(function ($subQuery) use ($dateFilter) {
                            $this->applyDateFilter($subQuery, $dateFilter);
                        });
                    }
                }
            });
        }

        if ($request->has('type')) {
            $types = is_array($request->input('type')) ? $request->input('type') : [$request->input('type')];
            $query->whereIn('uth.type', $types);
        }

        if ($request->has('email')) {
            $query->where('users.email', 'LIKE',  "{$request->input('email')}%" );
        }
    }

    private function applyDateFilter(Builder $query, string $dateFilter): void
    {
        
        $filters = [
            'today' => fn() => $query->whereDate('uth.created_at', Carbon::today()),
            'yesterday' => fn() => $query->whereDate('uth.created_at', Carbon::yesterday()),
            'last 7 days' => fn() => $query->whereBetween('uth.created_at', [Carbon::now()->subDays(7), Carbon::now()->subDays(1)]),
            'last 30 days' => fn() => $query->where('uth.created_at', '<=', Carbon::now()->subDays(30))
        ];

        if (isset($filters[$dateFilter])) {
            $filters[$dateFilter]();
        }
    }

    private function formatLogs(LengthAwarePaginator $logs): LengthAwarePaginator
    {
        $logs->through(function ($log) {
            $log->formatted_created_at = $log->created_at ? $this->formatDate($log->created_at) : 'No date available';
            return $log;
        });

        return $logs;
    }

    private function formatPagination(LengthAwarePaginator $paginator): array
    {
        $perPage = $paginator->perPage();
        $from = ($paginator->currentPage() - 1) * $perPage + 1;
        $countOnPage = count($paginator->items());
        $to = $from + $countOnPage - 1;
        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'next_page_url' => $paginator->nextPageUrl(),
            'prev_page_url' => $paginator->previousPageUrl(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'itemCount' => $to,
            'to' => $paginator->lastItem(),
            'per_page' => $paginator->perPage()
        ];
    }

    public function getLogPayload(int $id): ?array
    {
        $log = DB::table('public.user_transaction_histories')
            ->where('id', $id)
            ->first();
        
        if (!$log) {
            return null;
        }
        
        $payload = $log->payload ?? null;

        if ($payload && is_string($payload)) {
            try {
                $payload = json_decode($payload, true);
            } catch (\Exception $e) {
                $payload = ['raw' => $payload];
            }
        }
        
        return [
            'id' => $log->id,
            'user_id' => $log->user_id,
            'type' => $log->type,
            'message' => $log->message,
            'created_at' => $log->created_at,
            'data' => $payload
        ];
    }
} 