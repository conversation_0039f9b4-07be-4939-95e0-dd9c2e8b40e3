<?php

namespace App\Modules\PendingDelete\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\PendingDelete\Requests\DomainDeleteRequest;
use App\Modules\PendingDelete\Requests\ShowListRequest;
use App\Modules\PendingDelete\Services\DatabaseQueryService;
use Inertia\Inertia;

class PendingDeleteController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('PendingDelete/Index', $request->show());
    }

    public function deleteSummary(DomainDeleteRequest $request)
    {
        $domains = ['domains' => DatabaseQueryService::instance()->domainSummary($request->ids)];
        return Inertia::render('PendingDelete/Summary', $domains);
    }

    public function delete(DomainDeleteRequest $request)
    {
        $request->delete();

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Delete Request has been sent.',
            'postRouteName' => 'domain.pending-delete.view',
            'redirect' => [['route' => route('domain.pending-delete.view'), 'label' => 'Return to Pending Deletes']]
        ]);
    }
}
