export const customStyles = {
	table: {
		style: {
			height: '500px',
		},
	},
    headCells: {
        style: {
            color: "#111111",
            fontSize: '15px',
        },
    },
    rows: {
        style: {
            minHeight: '54px',
            fontSize: '14px',
            '&:not(:last-of-type)': {
                borderBottomStyle: 'solid',
                borderBottomWidth: '1px',
                borderBottomColor: 'rgb(243 244 246 / 1)',
            },
        },
        highlightOnHoverStyle: {
            borderBottomColor: '#FFFFFF',
            outline: '1px solid #FFFFFF',
            background: '#d5e4ea'
        },
    },
    pagination: {
        style: {
            border: 'none',
        },
    },
};
