<?php

namespace App\Modules\ClientPaymentService\Services\Interfaces;

use App\Modules\ClientPaymentService\Constants\PaymentServiceType;
use App\Modules\ClientPaymentService\Contracts\PaymentServiceInterface;
use Illuminate\Support\Facades\DB;

class AccountDebitPaymentService implements PaymentServiceInterface
{
    public function getPaymentService(object $paymentService)
    {
        $data = DB::client()->table('payment_services')
            ->join('account_credits', 'account_credits.id', '=', 'payment_services.account_credit_id')
            ->join('payment_summaries', 'payment_summaries.payment_service_id', '=', 'payment_services.id')
            ->where('payment_services.id', $paymentService->id)
            ->where('payment_services.user_id', $paymentService->user_id)
            ->where('account_credits.id', $paymentService->account_credit_id)
            ->select(
                'payment_services.*',
                'account_credits.amount as amount',
                'account_credits.running_balance as running_balance',
                'account_credits.type as account_credit_type',
                'account_credits.created_at as status',
                'payment_summaries.paid_amount as paid_amount',
                'payment_summaries.total_amount as total_amount',
                'payment_summaries.name as summary_name',
                'payment_summaries.type as summary_type',
            )->get()->first();

        $data->payment_service_type = PaymentServiceType::ACCOUNT_DEBIT;

        return $data;
    }
}
