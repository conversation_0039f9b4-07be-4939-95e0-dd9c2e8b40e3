import Item from "@/Components/Client/Domains/Item";
import AdminLayout from "@/Layouts/AdminLayout";
import { useState } from "react";
import { Link, router } from "@inertiajs/react";
import {
    MdArrowBack,
    MdOutlineFilterAlt,
    MdOutlineSettings,
} from "react-icons/md";
import {
    ImSortAlphaAsc,
    ImSortAlphaDesc,
    ImCalendar,
} from 'react-icons/im'
import {
    TbSortAscending2,
    TbSortDescending2
} from 'react-icons/tb'
import CursorPaginate from "@/Components/Util/CursorPaginate";
import "react-toastify/dist/ReactToastify.css";
import Filter from "@/Components/Client/Domains/Filter";
export default function DomainIndex({
    id,
    owner,
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const STATUS_TYPE = {
        ACTIVE: "Active",
        EXPIRED: "Expired",
        TRANSFERRED: "Transferred",
    };

    const SORT_TYPE = {
        DOMAIN_ASC: "domain:asc",
        DOMAIN_DESC: "domain:desc",
        CREATE_DATE_ASC: "created:asc",
        CREATE_DATE_DESC: "created:desc",
        EXPIRE_DATE_ASC: "expiry:asc",
        EXPIRE_DATE_DESC: "expiry:desc",
    }

    const DT_FORMAT = {
        STRING: 1,
        COUNT: 2,
        LOCAL: 3,
        UTC: 4,
    }

    const paramStatus = route().params.status ?? STATUS_TYPE.ACTIVE;
    const paramTld = route().params.tld;
    const paramMinExpireDate = route().params.minExpireDate;
    const paramMaxExpireDate = route().params.maxExpireDate;
    const paramOrderBy = route().params.orderby ?? SORT_TYPE.EXPIRE_DATE_ASC;

    const [dateTimeFormat, setDatetimeFormat] = useState(DT_FORMAT.STRING);

    const orderToggle = (sort) => {
        let payload = {};

        payload.status = paramStatus;
        if (paramTld) payload.tld = paramTld;
        if (paramMinExpireDate) payload.minExpireDate = paramMinExpireDate;
        if (paramMaxExpireDate) payload.maxExpireDate = paramMaxExpireDate;
        payload.orderby = sort;


        router.get(route("client.domains", { id: id }), payload);
    };

    const dateTimeFormatToggle = (format) => {
        format++;
        if (format > Object.keys(DT_FORMAT).length) format = 1;
        setDatetimeFormat(format);
    }

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <button className="text-3xl">

                </button>
                <div className="flex space-x-2 text-3xl font-bold mb-2">
                    <button onClick={() => router.get(route("client"))}>
                        <MdArrowBack />
                    </button>
                    <span>Domains of: {owner}</span>
                </div>
                <div
                    id="sample"
                    className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                >
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">
                            Filter:
                        </span>
                    </label>

                    <Filter id={id} />
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    {Object.values(STATUS_TYPE).map((e) => {
                        let query = {};
                        query.id = id;
                        query.status = e;
                        return (
                            <Link
                                key={e}
                                as="button"
                                href={route("client.domains", query)}
                            >
                                <div
                                    className={`px-5 py-1 rounded-sm ${paramStatus == e
                                        ? "bg-gray-100 text-link"
                                        : "hover:bg-gray-100 hover:text-link"
                                        }`}
                                >
                                    <span className=" text-inherit">
                                        {e}
                                    </span>
                                </div>
                            </Link>
                        );
                    })}
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th className="w-1/4">
                                    <div className="flex space-x-2">
                                        <label className="flex items-center pl-2 space-x-2">
                                            <span className="">Domain Name</span>
                                        </label>
                                        <button
                                            disabled={items.length === 0}
                                            onClick={() => orderToggle(paramOrderBy === SORT_TYPE.DOMAIN_ASC ? SORT_TYPE.DOMAIN_DESC : SORT_TYPE.DOMAIN_ASC)}
                                        >
                                            {paramOrderBy === SORT_TYPE.DOMAIN_ASC ? <ImSortAlphaAsc /> : <ImSortAlphaDesc />}
                                        </button>
                                    </div>
                                </th>
                                <th className="w-1/4">
                                    <div className="flex space-x-2">
                                        <span>Expiration</span>
                                        <button
                                            disabled={items.length === 0}
                                            onClick={() => orderToggle(paramOrderBy === SORT_TYPE.EXPIRE_DATE_ASC ? SORT_TYPE.EXPIRE_DATE_DESC : SORT_TYPE.EXPIRE_DATE_ASC)}
                                        >
                                            {paramOrderBy === SORT_TYPE.EXPIRE_DATE_ASC ? <TbSortAscending2 /> : <TbSortDescending2 />}
                                        </button>
                                        <button
                                            disabled={items.length === 0}
                                            onClick={() => dateTimeFormatToggle(dateTimeFormat)}
                                        >
                                            <ImCalendar />
                                        </button>
                                    </div>
                                </th>
                                <th className="w-1/4">
                                    <div className="flex space-x-2">
                                        <span>Created</span>
                                        <button
                                            onClick={() => orderToggle(paramOrderBy === SORT_TYPE.CREATE_DATE_ASC ? SORT_TYPE.CREATE_DATE_DESC : SORT_TYPE.CREATE_DATE_ASC)}
                                        >
                                            {paramOrderBy === SORT_TYPE.CREATE_DATE_ASC ? <TbSortAscending2 /> : <TbSortDescending2 />}
                                        </button>
                                    </div>
                                </th>
                                <th className="w-1/4">
                                    <span>Status</span>
                                </th>
                                 <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {items.map((item, index) => {
                                return (
                                    <Item
                                        item={item}
                                        key={"dl-" + index}
                                        dateTimeFormat={dateTimeFormat}
                                    />
                                );
                            })}
                        </tbody>
                    </table>
                </div>

                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                />
            </div>
        </AdminLayout>
    );
}
