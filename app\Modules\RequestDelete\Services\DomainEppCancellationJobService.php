<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Util\Helper\DomainParser;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Carbon\Carbon;

class DomainEppCancellationJobService
{
    public static function instance(): DomainEppCancellationJobService
    {
        return new self;
    }

    private function getRegisteredDomainId($domainId): int
    {
        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        if (!$registeredDomain) {
            throw new \Exception("Registered domain not found for domain ID: {$domainId}");
        }

        return $registeredDomain->id;
    }

    public function eppDelete(array $domain): void
    {
        $eppInfoResponse = EppDomainService::instance()->callEppDomainInfo($domain['domainName']);
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

        if (!isset($eppInfoResponse['data'])) {
            app(AuthLogger::class)->error("EPP domain info response missing 'data' key for domain: {$domain['domainName']}. Response: " . json_encode($eppInfoResponse));
            throw new \Exception("EPP domain info response invalid for domain {$domain['domainName']}");
        }

        if (!isset($datastoreInfoResponse['data'])) {
            app(AuthLogger::class)->error("Datastore domain info response missing 'data' key for domain: {$domain['domainName']}. Response: " . json_encode($datastoreInfoResponse));
            throw new \Exception("Datastore domain info response invalid for domain {$domain['domainName']}");
        }

        $eppInfo = $eppInfoResponse['data'];
        $datastoreInfo = $datastoreInfoResponse['data'];

        if (in_array('pendingTransfer', $eppInfo['status'])) {
            $this->handlePendingTransferRetry($domain);
            return;
        }

        if (in_array('clientDeleteProhibited', $eppInfo['status'])) {

            $updatePayload = [
                'name' => $domain['domainName'],
                'statusRemove' => ['clientDeleteProhibited']
            ];

            try {
                EppDomainService::instance()->updateEppDomain($updatePayload);
            } catch (\Exception $e) {
                NotificationService::sendFailedDeleteNotif($domain['domainName']);
                $this->cancelDomainDeletionRequest($domain);
                $this->dispatchDomainHistory($domain, 'failed');
                throw new \Exception("Failed to remove clientDeleteProhibited status for domain {$domain['domainName']}");
            }
        }

        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domain['domainName']);
        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domain['domainName']);

        $eppSuccess = isset($eppDeleteResult['status']) && $eppDeleteResult['status'] === 'OK';
        $datastoreSuccess = isset($datastoreDeleteResult['status']) && $datastoreDeleteResult['status'] === 'OK';

        if ($eppSuccess && $datastoreSuccess) {
            $this->processRefundForExpiredDomain($domain, $datastoreInfo);
            $this->updateLocalDatabase($domain);
            $this->dispatchDomainHistory($domain, 'success');
        } else {
            NotificationService::sendFailedDeleteNotif($domain['domainName']);
            $this->cancelDomainDeletionRequest($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("EPP deletion failed for domain {$domain['domainName']}");
        }
    }

    private function handlePendingTransferRetry(array $domain): void
    {
        $retryAttempt = $domain['pendingTransferRetryAttempt'] ?? 0;
        $maxRetries = 3;

        if ($retryAttempt >= $maxRetries) {
            NotificationService::sendFailedDeletionNotifV2($domain['domainName']);
            $this->cancelDomainDeletionRequest($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("Domain {$domain['domainName']} has pendingTransfer status after {$maxRetries} retry attempts");
        }

        $nextAttempt = $retryAttempt + 1;
        $delayMinutes = 15;

        app(AuthLogger::class)->info("Domain {$domain['domainName']} has pendingTransfer status. Scheduling retry attempt {$nextAttempt}/{$maxRetries} in {$delayMinutes} minutes.");

        $domain['pendingTransferRetryAttempt'] = $nextAttempt;

        DomainEppCancellation::dispatch(
            $domain['domainId'],
            $domain['domainName'],
            $domain['userId'],
            $domain['userEmail'],
            $domain['reason'],
            $domain['createdDate'],
            $domain['supportNote'] ?? null,
            $domain['adminId'] ?? null,
            $domain['adminName'] ?? null,
            $domain['adminEmail'] ?? null,
            $nextAttempt
        )->delay(Carbon::now()->addMinutes($delayMinutes));
    }

    private function updateLocalDatabase(array $domain): void
    {
        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update($updates);

        $this->updateDomainCancellationRequest($domain);

        $registeredDomainId = $this->getRegisteredDomainId($domain['domainId']);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $registeredDomainId,
            'deleted_by' => $domain['userEmail'],
            'deleted_at' => $timestamp,
            'created_at' => $timestamp,
            'updated_at' => $timestamp,
        ]);
    }

    private function updateDomainCancellationRequest(array $domain): void
    {
        $registeredDomainId = $this->getRegisteredDomainId($domain['domainId']);

        $adminId = $domain['adminId'] ?? null;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} deleted domain: {$domain['domainName']}";

        DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
            ]);
    }

    private function dispatchDomainHistory(array $domain, string $status): void
    {
        $adminName = $domain['adminName'] ?? 'System';
        
        if ($status === 'success') {
            $message = 'Domain "' . $domain['domainName'] . '" deleted by ' . $adminName ;
        } else {
            $message = 'Domain "' . $domain['domainName'] . '" deletion failed by ' . $adminName;
        }

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $domain['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $domain['userId'],
            'status'    => $status,
            'message'   => $message,
            'payload'   => json_encode($domain),
            'created_at'=> now(),
            'updated_at'=> now(),
        ]);
    }

    private function cancelDomainDeletionRequest(array $domain): void
    {
        $request = (object) [
            'domainId' => $domain['domainId'],
            'support_note' => 'Domain deletion request cancelled due to EPP failure: ' . $domain['domainName']
        ];

        try {
            DomainDeleteService::instance()->cancelDeleteRequest($request);
        } catch (\Exception $e) {
            app(AuthLogger::class)->error("Failed to cancel domain deletion request for domain {$domain['domainName']}: " . $e->getMessage());
        }
    }

    private function processRefundForExpiredDomain(array $domain, array $datastoreInfo): void
    {
        try {
            $expiryDate = Carbon::parse($datastoreInfo['expiry']);
            $now = Carbon::now();

            if (!$expiryDate->isPast()) {
                return;
            }

            $daysExpired = $now->diffInDays($expiryDate);

            if ($daysExpired > 45) {
                return;
            }

            $registryId = DomainParser::getRegistryId($domain['domainName']);

            if (!$registryId) {
                return;
            }

            $registeredDomain = DB::client()->table('registered_domains')
                ->where('domain_id', $domain['domainId'])
                ->first();

            if (!$registeredDomain) {
                app(AuthLogger::class)->error("Registered domain not found for domain ID: {$domain['domainId']}");
                return;
            }

            $renewalFeeData = DB::client()->table('extension_fees')
                ->join('fees', 'extension_fees.fee_id', '=', 'fees.id')
                ->where('extension_fees.extension_id', $registeredDomain->extension_id)
                ->where('fees.type', 'RENEW')
                ->first();

            if (!$renewalFeeData) {
                app(AuthLogger::class)->error("Renewal fee not found for extension ID: {$registeredDomain->extension_id}");
                return;
            }

            $renewalCost = floatval($renewalFeeData->value);
            // $icannFee = floatval(GeneralSettingService::getValueByKey(SettingKey::DOMAIN_ICANN_FEE));

            $refundAmount = $renewalCost;

            if ($refundAmount <= 0) {
                app(AuthLogger::class)->info("Refund amount for domain {$domain['domainName']} is {$refundAmount}. No refund processed.");
                return;
            }

            $registryBalance = RegistryAccountBalanceService::balance($registryId);
            $description = "Refund for expired domain deletion: {$domain['domainName']} (Renewal: {$renewalCost}";

            RegistryAccountBalanceService::debit($registryBalance, $refundAmount, RegistryTransactionType::REFUND_TRANSACTION, $description);

            app(AuthLogger::class)->info("Processed refund for domain {$domain['domainName']}: {$refundAmount} (Renewal: {$renewalCost})");

        } catch (\Exception $e) {
            app(AuthLogger::class)->error("Error processing refund for domain {$domain['domainName']}: " . $e->getMessage());
        }
    }
}
