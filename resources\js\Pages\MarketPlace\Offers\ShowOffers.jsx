import React, { useState } from 'react'
import DataTable from 'react-data-table-component';
import OfferHistoryPopup from '../components/OfferHistoryPopup';
import { FaSearch } from "react-icons/fa";
import { PiCurrencyDollarDuotone } from "react-icons/pi";
import { FaBalanceScaleLeft } from "react-icons/fa";
import { ImCross } from "react-icons/im";
import AdminLayout from '@/Layouts/AdminLayout';
import { offerCustomStyles } from '../components/offerCustomStyles';
import { BiEdit } from "react-icons/bi";
import { useEffect } from 'react';

export default function ShowOffers({myoffers}) {

    const [offers, setOffers] = useState([]);
    const [modal, showModal] = useState(false);
    const [domain, setDomain] = useState({name: '', created_at: new Date(), offer_price: 0});

    const columns = [
        {
            id: 'user',
            name: 'User',
            left: "true",
            selector: row => `${row.first_name} ${row.last_name}`,
            sortable: true,
        },
        {
            id: 'domain',
            name: 'Domain',
            left: "true",
            selector: row => row.domain_name,
            sortable: true,
            width: '200px'
        },
        {
            id: 'price',
            name: 'Initial Offer',
            left: "true",
            selector: row => row.offer_price,
            cell: row => `$${row.offer_price}`,
            sortable: true,
        },
        {
            id: 'status_change',
            name: 'Last Status Update',
            left: "true",
            selector: row => row.updated_at,
            cell: row => `${new Date(row.updated_at).toLocaleString()}`,
            sortable: true,
            width: '180px'
        },
        {
            id: 'status',
            name: 'Status',
            left: "true",
            selector: row => row.offer_status,
            cell: row => getStatus(row.offer_status),
            sortable: true,
            width: '180px'
        },
        {
            id: 'current',
            name: 'Buy Now Price',
            left: "true",
            selector: row => row.counter_offer_price,
            cell: row => `${row.counter_offer_price > 0 ? `$${row.counter_offer_price}` : 'NA'}`,
            sortable: true,
        },
        {
            id: 'action',
            name: 'Action',
            selector: row => getDetailButton(row),
            sortable: true,
        },
    ];

    const handlePopUp = (row) => {
        setDomain(row)
        showModal(true);
    }

    const getDetailButton = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg bg-gray-100 text-green-700 px-3 py-1 -mt-8'>Edit</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-green-500 bg-opacity-20 rounded-md font-bold text-lg text-green-700 p-1.5 flex items-center space-x-2'>
                    <BiEdit className=' font-bold' />
                </button>
            </div>

        </div>
    }

    const getStatus = (status) => {
        if (status == 'waiting') return <span className='bg-yellow-500 bg-opacity-10 p-1 rounded-full px-3 text-xs font-bold capitalize text-yellow-600'>{status}</span>
        else if (status == 'offer_accepted') return <span className='bg-green-500 bg-opacity-10 p-1 rounded-full px-3 text-xs font-bold capitalize text-green-600'>accepted</span>
        else if (status == 'counter_offer') return <span className='bg-primary bg-opacity-10 p-1 rounded-full px-3 text-xs font-bold capitalize text-primary'>{status.replaceAll('_', ' ')}</span>
        else if (status == 'user_counter_offer') return <span className='bg-black bg-opacity-5 p-1 rounded-full px-3 text-xs font-bold capitalize text-black'>{status.replaceAll('_', ' ')}</span>
        else if (status == 'paid') return <span className='bg-indigo-500 bg-opacity-10 p-1 rounded-full px-3 text-xs font-bold capitalize text-indigo-700'>Offer Paid</span>
        else return <span className='bg-red-500 bg-opacity-10 p-1 rounded-full px-3 text-xs font-semibold capitalize text-red-600'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
    }

    const getDiv = (name, price, status) => {
        return <div className='flex flex-col py-3'>
            <div className='text-xl font-medium text-gray-800'>{name}</div>
            <div className='text-[15px] font-medium text-gray-500 flex gap-2 pt-1'>
                <div className='pt-0.5'>${price}</div>
                {getStatus(status)}
            </div>
        </div>
    }

    useEffect(() => {
        setOffers(myoffers)
    }, [])

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">

                <OfferHistoryPopup showModal={showModal} offers={offers} setOffers={setOffers} getStatus={getStatus} modal={modal} domain={domain} />

                <div className="mt-1 ml-5">
                    <DataTable
                        columns={columns}
                        data={offers}
                        pagination
                        persistTableHead
                        highlightOnHover
                        customStyles={offerCustomStyles}
                        pointerOnHover
                        fixedHeader
                        fixedHeaderScrollHeight="600px"
                    />
                </div>
            </div>
        </AdminLayout>
    )
}
