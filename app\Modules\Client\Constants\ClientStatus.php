<?php

namespace App\Modules\Client\Constants;

use ReflectionClass;

final class ClientStatus
{
    public const SIGN_IN = 'SIGN IN';

    public const REGISTER = 'REGISTER';

    public const PROFILE_UPDATE = 'PROFILE UPDATE';

    public const SECURITY_UPDATE = 'SECURITY UPDATE';

    public const CONTACT_UPDATE = 'CONTACT UPDATE';

    public const CATEGORY_UPDATE = 'CATEGORY UPDATE';

    public const DOMAIN_UPDATE = 'DOMAIN UPDATE';

    public const IDENTITY_VERIFICATION = 'IDENTITY VERIFICATION';

    public const PAYMENT_SUMMARY = 'PAYMENT SUMMARY';

    public static function getAllStatuses()
    {
        $reflection = new ReflectionClass(self::class);
        return $reflection->getConstants();
    }
}
