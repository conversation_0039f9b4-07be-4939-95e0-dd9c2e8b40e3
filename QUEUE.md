# SYSTEM QUEUE CONFIG

### queue setup
create connection to config/queue.php
```
 'connections' => [
        'email_jobs' => [
            'driver' => 'database',
            'table' => 'email_jobs',
            'queue' => 'default',
            'retry_after' => 120,
            'after_commit' => false,
        ],
        /*
        * other configs...
        */
 ]
```

setup database table
```
php artisan queue:table --connection=email_jobs
php artisan migrate --database=email_jobs
```

create jobs class
```
php artisan make:job SendEmailNotification
```

to send jobs to queue
```
SendEmailNotification::dispatch($id, $status)->onConnection(QueueConnection::EMAIL)->onQueue($status);
```
- $id , $status -> constructor param for  SendEmailNotification job
- ->onConnection(QueueConnection::EMAIL) -> name of the queue connection // QueueConnection::EMAIL = "email_jobs"
- ->onQueue($status); -> optional , name of the queue ; default name = "default" ; possible $status value = ACCEPT,DENIED

run worker 
```
php artisan queue:work email_jobs --queue=default,ACCEPT,DENIED --tries=0 --timeout=30 --max-time=900 --stop-when-empty

```
- queue = name of queue
- tries = number of job retries before it fail
- timeout = max process time before it timedout and fail
- max-time = 900 -> 15minutes of proccessing time before it stops to release memory and be restarted again
- stop-when-empty = terminate worker if job is empty


### additional config

adding ShouldBeUnique traits to jobs
- class SendEmailNotification implements ShouldQueue ,ShouldBeUnique

make sure it is unique
```
public $uniqueFor = 3600;

public function uniqueId(): int
{
    return $this->id;
}
```
use WithoutOverlapping middleware if jobs has the same signature but should not be processed at the same time.

### DEPLOY QUEUE

add supervisor.conf path to /etc/supervisor/supervisord.conf [include] file separated by space
- ie. files = /etc/supervisor/conf.d/*.conf /home/<USER>/sd-admin/supervisor.conf

run supervisor.sh script
```
./supervisor.sh
```