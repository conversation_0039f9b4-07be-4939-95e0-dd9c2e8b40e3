<?php

use App\Modules\SecretManager\ManualSecretManagerImplementatation;
use Illuminate\Support\Str;

$secretManager = new ManualSecretManagerImplementatation();

$DB_HOST = $secretManager->getMeta('DB_HOST');
$DB_DATABASE = $secretManager->getMeta('DB_DATABASE');
$DB_USERNAME = $secretManager->getMeta('DB_USERNAME');
$DB_PASSWORD = $secretManager->getPayload('DB_PASSWORD');

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'db_connection_not_configured'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'client' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => $DB_HOST,
            'port' => env('DB_CLIENT_PORT', '5432'),
            'database' => $DB_DATABASE,
            'username' => $DB_USERNAME,
            'password' => $DB_PASSWORD,
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('DB_CLIENT_SCHEMA', 'public'),
            'sslmode' => 'prefer',
        ],

        'admin' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => $DB_HOST,
            'port' => env('DB_ADMIN_PORT', '5432'),
            'database' => $DB_DATABASE,
            'username' => $DB_USERNAME,
            'password' => $DB_PASSWORD,
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => env('DB_ADMIN_SCHEMA', 'admin'),
            'sslmode' => 'prefer',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
