<?php

namespace App\Modules\CustomLogger\Services;

use Google\Cloud\Logging\LoggingClient;

class GoogleCloudLogger implements CustomLoggerInterface
{
    private $logger;

    public function __construct(string $projectId, string $logName)
    {

        $client = new LoggingClient([
            'projectId' => $projectId,
        ]);

        $this->logger = $client->logger($logName);
    }

    public function info(string $message)
    {
        $this->logger->write($this->logger->entry($message, [
            'severity' => 200,
        ]));
    }

    public function error(string $message)
    {
        $this->logger->write($this->logger->entry($message, [
            'severity' => 400,
        ]));
    }
}
