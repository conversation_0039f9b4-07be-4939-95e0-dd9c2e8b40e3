import EmptyResult from "@/Components/Util/EmptyResult";
import AdminLayout from "@/Layouts/AdminLayout";
import LoaderSpinner from "@/Components/LoaderSpinner";
import JobItem from "@/Components/Job/JobItem";
import { router } from "@inertiajs/react";
import { useState } from "react";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";

export default function Jobs({ jobs_stats = [], total_stats = [] }) {
    const { source } = route().params;
    const [startDate, setStartDate] = useState(new Date());
    const [hasSpinner, setSpinner] = useState(false);

    const config = {
        client: { active: "client" == source },
        admin: { active: "admin" == source }
    };

    router.on("start", () => setSpinner(true));
    router.on("finish", () => setSpinner(false));

    const handlePageChange = (url) => {
        const updatedParams = { source, limit };
        router.get(url, updatedParams, { preserveState: true });
    };

    const handleLimitChange = (e) => {
        const newLimit = e.target.value;
        setLimit(newLimit);

        router.get(route("job"), { source, limit: newLimit }, {
            preserveState: true,
        });
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[900px] mt-10 flex flex-col">
                <div className="mb-8">
                    <h1 className="text-4xl font-bold pl-3">Jobs Queue</h1>
                    <p className="text-gray-600 pl-3">
                        A summary of all current jobs, including how many were dispatched, completed, or failed.
                    </p>
                </div>
                <div className="flex poll-center flex-wrap cursor-pointer border-b text-default">
                    <a
                        href={route("job", { source: "client" })}
                        className={"flex-1 text-center hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${config.client.active && "bg-gray-100 text-link font-bold"}`}
                    >
                        <span className=" text-inherit">Client</span>
                    </a>
                    <a
                        href={route("job", { source: "admin" })}
                        className={"flex-1 text-center hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${config.admin.active && "bg-gray-100 text-link font-bold"}`}
                    >
                        <span className=" text-inherit">Admin</span>
                    </a>
                </div>
                {hasSpinner ? (
                    <div className="flex flex-col items-center justify-center space-x-4 min-h-[200px]">
                        <LoaderSpinner h="h-12" w="w-12" />
                        <span className="mt-4 text-gray-600">Loading Data...</span>
                    </div>
                ) : (!source ? (
                    <EmptyResult message="Select source to view jobs." />
                ) : (
                    <>
                        <div className="flex space-x-4 justify-between">
                            {/* <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                        <label className="text-xl font-semibold text-gray-600">
                            ON QUEUE
                        </label>
                        <span className=" text-7xl">
                            {queueCount}
                        </span>
                    </div> */}
                            <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                <label className="text-md font-semibold text-gray-600">
                                    DISPATCHED
                                </label>
                                <span className=" text-5xl">
                                    {total_stats.dispatch}
                                </span>
                            </div>
                            <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                <label className="text-md font-semibold text-gray-600">
                                    SUCCESS
                                </label>
                                <span className=" text-5xl">
                                    {total_stats.success}
                                </span>
                            </div>
                            <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                <label className="text-md font-semibold text-gray-600">
                                    FAILED
                                </label>
                                <span className=" text-5xl">
                                    {total_stats.failed}
                                </span>
                            </div>
                        </div>
                        <table className="min-w-full text-left border-spacing-y-1 border-separate mb-4">
                            <thead className="bg-gray-50 text-sm">
                                <tr>
                                    <th className="py-2 px-2"><span>Name</span></th>
                                    <th className="py-2 px-2 text-center"><span>On Queue</span></th>
                                    <th className="py-2 px-2 text-center"><span>On Failed</span></th>
                                    <th className="py-2 px-2 flex items-center justify-center text-center space-x-2">Stats</th>
                                </tr>
                            </thead>
                            <tbody className="text-sm">
                                {jobs_stats.map(job => (
                                    <JobItem
                                        key={job.connection}
                                        source={source}
                                        connection={job.connection}
                                        dispatches={job.dispatch}
                                        success={job.success}
                                        failed={job.failed}
                                    />
                                ))}
                            </tbody>
                        </table>
                    </>
                ))}
            </div>
        </AdminLayout >
    );
}
