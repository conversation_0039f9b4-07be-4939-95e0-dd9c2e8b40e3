<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Services\CommissionSettingService;
use Illuminate\Foundation\Http\FormRequest;

class ShowCommissionListForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'status' => ['string'],
        ];
    }

    public function show()
    {
        return CommissionSettingService::get($this);
    }
}
