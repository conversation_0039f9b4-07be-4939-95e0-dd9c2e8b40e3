<?php

namespace App\Modules\UserManagement\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserManagementRoleIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'showItems' => ['nullable', 'integer'], 
            'orderBy'   => 
            [
                'nullable',
                'string', 
                Rule::in(
                    [
                        "role:desc",
                        "role:asc",
                        "permissions:desc",
                        "permissions:asc",
                        "createdBy:desc",
                        "createdBy:asc",
                        "lastUpdated:desc",
                        "lastUpdated:asc",  
                    ]
                )
            ],
            'role' => ['nullable', 'string']
        ];
    }
}
