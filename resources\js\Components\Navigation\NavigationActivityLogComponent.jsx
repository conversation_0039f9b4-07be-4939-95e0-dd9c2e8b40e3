//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdExpandLess,
    MdExpandMore,
    MdSupervisedUserCircle,
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationActivityLogComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const activeRoutes =
    [
        "client.logs.security",
        "client.logs.domain",
        "client.logs.system",
        "client.logs.account"
    ];

    const routes =
    {
        logs       : activeRoutes.some(routePattern => currentRoute && currentRoute.startsWith(routePattern))
    };

    const links = 
    [
        {
            routeName: 'client.logs.security.all',
            hasAccess: hasPermission('client.logs.security.all'),
            isActive : routes.logs,
            label    : 'client activity logs'
        }, 
    ];

    //! STATES
    //...

    //! FUNCTIONS

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
