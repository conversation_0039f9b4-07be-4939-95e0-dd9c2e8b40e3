<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Services\ExtensionFeeService;
use App\Util\Constant\Transaction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class ShowUserCustomLimitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id'    => ['required', 'integer', 'exists:client.users,id'],
            'email' => ['required', 'email', 'exists:client.users,email']
        ];
    }

    public function getData()
    {
        $userTransactions = DB::client()->table('user_transactions')
            ->select('user_transactions.*', 'transactions.name as transaction_name', 'transactions.user_limit')
            ->join('transactions', 'transactions.id', '=', 'user_transactions.transaction_id')
            ->where('user_transactions.user_id', $this->id)
            ->whereIn('transactions.name', array_keys(Transaction::TYPES))
            ->orderBy('transactions.name')
            ->get();

        return [
            'userId' => $this->id,
            'email' => $this->email,
            'transactions' => $userTransactions
        ];
    }
}
