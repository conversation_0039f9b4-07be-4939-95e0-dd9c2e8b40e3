<?php

namespace App\Modules\BankTransfer\Constants;

final class BankTransferPurposeConstants
{
    public const ADD_CREDIT          = 'add credit';
    public const INITIAL_BALANCE     = 'initial balance';
    public const OFFER_PAYMENT       = 'offer payment';
    public const MARKETPLACE_PAYMENT = 'marketplace payment';

    public static function fetchAll()
    {
        $reflection = new \ReflectionClass(self::class);

        return array_values($reflection->getConstants());
    }
}


