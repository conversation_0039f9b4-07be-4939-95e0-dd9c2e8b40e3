<?php

namespace App\Modules\EmailHistory\Services;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class EmailSearchService
{
    public static function validateSearch($search)
    {
        return !is_null($search) ? strtolower($search) : null;
    }

    public static function applySearch(Builder $query, $search)
    {
        $search = self::validateSearch($search);
        if ($search) {
            if ($search === 'system') {
                $query->whereNull('user_id');
            } else {
                $query->where(DB::raw('LOWER(name)'), 'like', '%' . $search . '%');
            }
        }
    }
}