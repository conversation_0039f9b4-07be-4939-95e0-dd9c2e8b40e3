const convertToDatetime = (date, isUTC = false, getUTC = false) => {
    const targetDateObj = new Date(typeof date === 'number' ? date : (isUTC ? date + 'Z' : date));
    
    const targetDate = getUTC ?
                targetDateObj.getUTCFullYear() + "-" + 
                String(targetDateObj.getUTCMonth() + 1).padStart(2, '0') + "-" + 
                String(targetDateObj.getUTCDate()).padStart(2, '0')
                :
                targetDateObj.getFullYear() + "-" + 
                String(targetDateObj.getMonth() + 1).padStart(2, '0') + "-" + 
                String(targetDateObj.getDate()).padStart(2, '0');                

    const targetTime = getUTC ?
                String(targetDateObj.getUTCHours()).padStart(2, '0') + ":" + 
                String(targetDateObj.getUTCMinutes()).padStart(2, '0') + ":" + 
                String(targetDateObj.getUTCSeconds()).padStart(2, '0')
                :
                String(targetDateObj.getHours()).padStart(2, '0') + ":" + 
                String(targetDateObj.getMinutes()).padStart(2, '0') + ":" + 
                String(targetDateObj.getSeconds()).padStart(2, '0');

    return targetDate + " " + targetTime + (getUTC ? " UTC" : "");
};

export default convertToDatetime;
