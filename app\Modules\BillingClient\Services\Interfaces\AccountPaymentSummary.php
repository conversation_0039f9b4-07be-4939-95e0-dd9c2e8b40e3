<?php

namespace App\Modules\BillingClient\Services\Interfaces;

use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Modules\BillingClient\Contracts\PaymentSummaryInterface;
use App\Modules\ClientPaymentService\Services\PaymentServiceHelper;

class AccountPaymentSummary implements PaymentSummaryInterface
{
    public function getPaymentbyId(int $id, int $userId) 
    {
        $data = PaymentServiceHelper::instance()->getPaymentServiceById($id, $userId);

        return [
            'data' => [$data],
            'icann_total' => 0,
            'summary_type' => PaymentSummaryType::ACCOUNT_BALANCE,
        ];
    }

    public function getRefundbyId(int $id, int $userId) {}
}
