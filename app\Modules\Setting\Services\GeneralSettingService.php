<?php

namespace App\Modules\Setting\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Setting\Constants\SettingType;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GeneralSettingService
{
    private const MIN_FEE = 0.1;

    private const MAX_FEE = 10000;

    public static function get()
    {
        $list = DB::client()->table('settings')->orderby('id', 'asc')->get()->all();
        $settings = [];

        foreach ($list as $value) {
            $settings[$value->key] = (array) $value;
        }

        return $settings;
    }

    public static function update($column, $value, $type)
    {
        $validated = self::validateFeeValue($value, $type);

        DB::client()->table('settings')->where('key', $column)->update(['value' => $value]);
        app(AuthLogger::class)->info('update general setting value of '.$column.' to '.$value);
    }

    public static function getValueByKey($key)
    {
        try {
            $item = DB::client()->table('settings')
                ->where('key', $key)
                ->select('value')
                ->get()->first();

            return $item->value;
        } catch (Exception $e) {
            Log::error($e->getMessage());
            app(AuthLogger::class)->error($e->getMessage());

            return '0';
        }
    }

    public static function validateFeeValue(float $value, string $type)
    {
        if ($type != SettingType::DOLLAR) {
            return true;
        }

        if ($value < self::MIN_FEE || $value > self::MAX_FEE) {
            throw new FailedRequestException(400, 'Value not valid.', 'Bad request');
        }

        return true;
    }
}
