<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class CheckRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'item_id' => 'required',
            'code' => 'required'
        ];
    }

    public function check()
    {
        $code = $this->code;
        $id  = $this->item_id;

        $hashed = DB::table('afternic_sale_audits')->select('confirm_code')->where('id', $id)->first();

        if(!Hash::check($code, $hashed->confirm_code)) throw ValidationException::withMessages(['errors' => 'Invalid Authorization Code']);

        DB::table('afternic_sale_audits')
        ->where('id', $id)
        ->update(['status' => 'filed']);
    }
}
