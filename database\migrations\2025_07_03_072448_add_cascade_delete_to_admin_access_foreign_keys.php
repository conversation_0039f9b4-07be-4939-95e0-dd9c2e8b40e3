<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_access', function (Blueprint $table) {
            //! Drop foreign keys first (use their constraint names or generated names)
            $table->dropForeign(['admin_id']);
            $table->dropForeign(['access_id']);

            //! Re-add with cascade on delete
            $table->foreign('admin_id')->references('id')->on('admins')->onDelete('cascade');
            $table->foreign('access_id')->references('id')->on('access')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_access', function (Blueprint $table) {
            $table->dropForeign(['admin_id']);
            $table->dropForeign(['access_id']);

            // Re-add original foreign keys without cascade
            $table->foreign('admin_id')->references('id')->on('admins');
            $table->foreign('access_id')->references('id')->on('access');
        });
    }
};
