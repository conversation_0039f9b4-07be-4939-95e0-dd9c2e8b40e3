//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppInputCheckboxComponent(
    {
        id                  = 'checkbox',
        name                = 'checkbox',
        value               = '',
        label               = null,
        isChecked           = false,
        isDisabled          = false,
        classNameCheckbox   = '',
        classNameContainer  = '', 
        classNameLabel      = '',
        handleEventOnChange = () => alert('checked')
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    //...

    //! FUNCTIONS
    //...

    return (
        <div
            className={`flex gap-2 items-center ${classNameContainer}`}
        >
            <input
                id={id}
                name={name} 
                type='checkbox'
                className={`
                    rounded border-gray-300 text-gray-600 shadow-sm focus:ring-0 cursor-pointer ${classNameCheckbox}
                    disabled:cursor-not-allowed disabled:bg-gray-200    
                `}
                value={value}
                checked={isChecked}
                disabled={isDisabled}
                onChange={handleEventOnChange}
            />            
            {
                label == null 
                    ?
                        null
                    :
                        <label
                            htmlFor={id}
                            className={`text-sm cursor-pointer ${classNameLabel}`}
                        >
                            {label}
                        </label>
            }
        </div>
    );
}
