<?php

namespace App\Providers;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Services\EppUrlInterface;
use App\Modules\Epp\Services\LocalEppUrl;
use App\Modules\Epp\Services\ProductionEppUrl;
use App\Modules\SecretManager\ManualSecretManagerImplementatation;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $secretManager = new ManualSecretManagerImplementatation();
        $this->registerEPPUrl($secretManager);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(EppUrlInterface $eppUrl): void
    {
        DB::macro('client', function () {
            return DB::connection('client');
        });

        Http::macro('client', function () {
            return Http::baseUrl(env('CLIENT_API_URL'))
                ->throw(function (Response $response, RequestException $e) {
                    app(AuthLogger::class)->error($e->getMessage() . ' ; ' . json_encode($response->json()));
                });
        });

        Http::macro('registry', function ($registry) use ($eppUrl) {
            return Http::baseUrl($eppUrl->poll($registry))
                ->withToken($eppUrl->secretKey())
                ->throw(function (Response $response, RequestException $e) {
                    app(AuthLogger::class)->error($e->getMessage() . ' ; ' . json_encode($response->json()));
                });
        });

        Http::macro('poll', function ($registry) use ($eppUrl) {
            return Http::baseUrl($eppUrl->poll($registry))
                ->withToken($eppUrl->secretKey())
                ->throw(function (Response $response, RequestException $e) {
                    app(AuthLogger::class)->error($e->getMessage() . ' ; ' . json_encode($response->json()));
                });
        });

        Http::macro('domain', function ($registry) use ($eppUrl) {
            return Http::baseUrl($eppUrl->domain($registry))
                ->withToken($eppUrl->secretKey())
                ->throw(function (Response $response, RequestException $e) {
                    app(AuthLogger::class)->error($e->getMessage() . ' ; ' . json_encode($response->json()));
                });
        });

        Http::macro('transfer', function ($registry) use ($eppUrl) {
            return Http::baseUrl($eppUrl->transfer($registry))
                ->withToken($eppUrl->secretKey())
                ->throw(function (Response $response, RequestException $e) {
                    app(AuthLogger::class)->error($e->getMessage().' ; '.json_encode($response->json()));
                });
        });
    }

    private function registerEPPUrl(ManualSecretManagerImplementatation $secretManager): void
    {
        $APP_KEY = $secretManager->getMeta('REGISTRY_API_URL');
        $REGISTRY_SECRET_KEY = $secretManager->getPayload('REGISTRY_API_URL_SECRET_KEY');

        $this->app->bind(EppUrlInterface::class, function ($app) use ($APP_KEY, $REGISTRY_SECRET_KEY) {
            return $app->environment('production')
                ? new ProductionEppUrl($APP_KEY, $REGISTRY_SECRET_KEY)
                : new LocalEppUrl($APP_KEY, $REGISTRY_SECRET_KEY);
        });
    }
}
