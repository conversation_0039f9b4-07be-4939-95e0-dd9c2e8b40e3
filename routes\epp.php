<?php

use App\Modules\Epp\Controllers\EppAccountController;
use App\Modules\Epp\Controllers\EppLogController;
use App\Modules\Epp\Controllers\EppPollController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('epp')->group(function () {
    Route::prefix('account')->group(function () {
        Route::get('/', [EppAccountController::class, 'index'])->name('epp.account');
        Route::get('/adjust-balance', [EppAccountController::class, 'adjustBalance'])->name('epp.account.adjust-balance');
        Route::post('/adjust-balance', [EppAccountController::class, 'store'])->name('epp.account.adjust-balance.store');
    });

    Route::prefix('poll')->group(function () {
        Route::get('/', [EppPollController::class, 'index'])->name('epp.poll');
        Route::post('/view/{id}', [EppPollController::class, 'view'])->name('epp.poll.view');
        Route::get('/pop', [EppPollController::class, 'pop'])->name('epp.poll.pop');
    });

    Route::prefix('log')->group(function () {
        Route::get('/', [EppLogController::class, 'index'])->name('epp.log');
        Route::get('/more', [EppLogController::class, 'more'])->name('epp.log.more');
        Route::get('/refresh', [EppLogController::class, 'refresh'])->name('epp.log.refresh');
    });
});
