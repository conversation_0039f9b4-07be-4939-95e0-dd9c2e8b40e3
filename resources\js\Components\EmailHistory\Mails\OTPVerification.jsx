import React from "react";

const OTPVerification = ({ emailBody, subject }) => {
    const data =
        typeof emailBody === "string" ? JSON.parse(emailBody) : emailBody;

    if (subject === "Email Verification Code") {
        return (
            <div
                className="bg-slate-100 min-h-fit flex items-center justify-center"
                style={{
                    fontFamily:
                        "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
                    color: "#1a202c",
                }}
            >
                <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full text-slate-500">
                    <p className="mb-4 font-bold">Hello {data.username},</p>
                    <p className="mb-4">
                        Here is your verification code. This is only valid
                        for 5 minutes. Never share it with anyone.
                    </p>
                    <div className="my-6 text-center">
                        <h1 className="text-3xl font-mono text-blue-800 font-bold tracking-wider">
                            {data.code}
                        </h1>
                    </div>
                    <p className="font-bold">Sincerely,</p>
                    <br />
                    <p className="font-bold">StrangeDomains</p>
                </div>
            </div>
        );
    }

    if (subject === "Email Verification Code - Password Reset") {
        return (
            <div>
                <div
                    className="bg-slate-100 min-h-fit flex items-center justify-center"
                    style={{
                        fontFamily:
                            "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
                        color: "#1a202c",
                    }}
                >
                    <p className="mb-4 font-bold">Hello {data.username},</p>
                    <p className="mb-4">
                        Use the code below to complete your verification process
                        to proceed to account registration. This code is only
                        valid for 5 minutes. Never share the code with anyone
                    </p>
                    <div className="my-6 text-center">
                        <h1 className="text-3xl font-mono text-blue-800 font-bold tracking-wider">
                            {data.code}
                        </h1>
                    </div>
                    <p className="text-gray-600 italic">Sincerely,</p>
                    <p className="text-gray-600 italic">StrangeDomains</p>
                </div>
            </div>
        );
    }

    return (
        <div
            className="bg-slate-100 min-h-fit flex items-center justify-center"
            style={{
                fontFamily:
                    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
                color: "#1a202c",
            }}
        >
            <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full text-slate-500">
                <p className="mb-4 font-bold">Hello {data.username},</p>

                <p className="mb-4">
                    Use the code below to complete your login procedure using
                    email authentication. This code is only valid for 5 minutes.
                    Never share the Code with anyone.
                </p>

                <div className="my-6 text-center">
                    <h1 className="text-3xl font-mono text-blue-800 font-bold tracking-wider">
                        {data.code}
                    </h1>
                </div>

                <p className="font-bold">Sincerely,</p>
                <br />
                <p className="font-bold">StrangeDomains</p>
            </div>
        </div>
    );
};

export default OTPVerification;
