import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/js/**/*.jsx",
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ["Figtree", ...defaultTheme.fontFamily.sans],
            },
            colors: {
                primary: "#147EA7",
                link: "#247CE3",
                success: "#0FA430",
                danger: "#E57869",
                DEFAULT: "#147EA7",
            },
            animation: {
                "spin-slow": "spin 3s linear infinite",
            },
            screens: {
                  // 👇 Custom breakpoint at 1366px
                  'lg1366': '1366px',
            },
            fontSize: {
                'xs-custom': '12px', // example size
            },
        },

    },

    plugins: [forms],
    
};
