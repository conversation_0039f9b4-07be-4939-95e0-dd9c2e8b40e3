//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { FaGear } from 'react-icons/fa6';
import { TbSettingsQuestion, TbTrash } from 'react-icons/tb';

//* COMPONENTS
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';
import AppTableComponent from '@/Components/App/AppTableComponent';
import AppTableRowDropdownActionsComponent from '@/Components/App/AppTableRowDropdownActionsComponent';
import UserManagementCategoryTableFilterComponent from '@/Components/UserManagement/UserManagementCategoryTableFilterComponent';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserManagementCategoryTable(
    {
        allItems,
        stateTableItems,      
        setStateTableItems,   
        stateSelectedItems,   
        setStateSelectedItems, 
        stateSelectedItem,
        setStateSelectedItem,
        stateIsActiveModalViewPermissions,
        setStateIsActiveModalViewPermissions,
        paginationInfo
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! STATES
    const [stateVerificationAction, setStateVerificationAction]                 = useState(null);
    const [stateSelectedActionItem, setStateSelectedActionItem]                 = useState(null);
    const [stateIsActiveModalVerification, setStateIsActiveModalVerification]   = useState(false);
    
    //! FUNCTIONS
    function handleCheckAll(e)
    {
        if (e.target.checked == true)
        {
            setStateSelectedItems([]);
            setStateSelectedItems(allItems);
        }
        else 
        {
            setStateSelectedItems([]);
        }
    }

    function handleCheck(e)
    {
        const {value, checked } = e.target;

        if (checked)
        {
            setStateSelectedItems([...stateSelectedItems, parseInt(value)]);
        }
        else
        {
            setStateSelectedItems(stateSelectedItems.filter(item => parseInt(item) != parseInt(value)));
        }
    }

    function checkIfAllRowsSelected(requiredItems, selectedItems)
    {
        return requiredItems.every(p =>
            selectedItems.some(s => s === p)
        );
    }

    async function handleViewPermissions(categoryId)
    {
        try 
        {
            const response = await axios.post(route("user-management.category.view-permissions", { id: categoryId }));

            if (response.status == 200)
            {
                setStateSelectedItem(response.data)
                setStateIsActiveModalViewPermissions(true);
            }
        }
        catch (error)
        {
            const response = error.response;
            
            toast.error('Could not fetch permissions');
        }
    }

    function handleDelete(categoryId)
    {
        router.delete(
            route('user-management.category.delete', {id : categoryId}),
            {
                onSuccess: () =>
                {
                    toast.success(
                        'Category Deleted',
                        {
                            autoClose: 5000
                        }
                    )
                    
                    setStateSelectedItems([]);
                    setStateSelectedActionItem(null);
                },
                onError: () => toast.error('Something went wrong'),
            }
        );
    }
    function handleActionSubmission()
    {
        switch (stateVerificationAction)
        {
            case 'delete': 
                handleDelete(stateSelectedActionItem.id);
                break; 
        }
    }

    //! VARIABLES
    const columnHeaders =
        [
            {
                label    : (
                    <AppInputCheckboxComponent
                        id='checkAll'
                        name="checkAll"
                        value={'all'}
                        isDisabled={stateTableItems.length == 0}
                        isChecked={checkIfAllRowsSelected(allItems, stateSelectedItems) && stateTableItems.length != 0}
                        handleEventOnChange={(e) => handleCheckAll(e)}
                    />                    
                ),
                isSortable : false
            }, 
            {
                label     : 'category',
                isSortable: true,
            }, 
            {
                label     : 'permissions',
                isSortable: true,
            },
            {
                label     : 'created by',
                isSortable: true,
            },
            {
                label     : 'last updated',
                isSortable: true,
            },
            {
                label     : <FaGear />,
                isSortable: false,
            },
        ];
    
    const rowActionClass = 'w-6 h-6'; 

    const rowActions = 
    [
        {
            label           : 'update category', 
            icon            : <TbSettingsQuestion className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.category.edit') && hasPermission('user-management.category.update'),
            shouldDisplay   : (item) => { return true},
            handleEventClick: (item) =>
            {
                return router.get(
                    route('user-management.category.edit', {id : item.id}),
                )            
            } 
        },
        {
            label           : 'delete category',
            icon            : <TbTrash className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.category.delete'),
            shouldDisplay   : (item) => { return true},
            handleEventClick: (item) =>
            {
                setStateSelectedActionItem(item);
                setStateVerificationAction('delete');
                setStateIsActiveModalVerification(true);
            } 
        }, 
    ];
        
    const columnRows = stateTableItems.map(
        (row) =>
        {
            return [
                {
                    value: (
                        <AppInputCheckboxComponent
                            id={row.id}
                            name={`check_${row.id}`}
                            value={row.id}
                            isDisabled={false}
                            isChecked={stateSelectedItems.includes(row.id)}
                            handleEventOnChange={(e) => handleCheck(e)}
                        />                    
                    )
                }, 
                {
                    value: row.category,
                },
                {
                    value: (
                        hasPermission('user-management.category.view-permissions') 
                            ? 
                                <div
                                    className="text-primary underline cursor-pointer"
                                    onClick   = {() => { handleViewPermissions(row.id) }}
                                >
                                    {row.access}
                                </div>
                            :
                                <div
                                    className=""
                                >
                                    {row.access}
                                </div>
                    )
                },
                {
                    value: row.createdBy,
                },
                {
                    value: row.lastUpdated
                },
                {
                    value: <AppTableRowDropdownActionsComponent row={row} rowActions={rowActions}/>
                }
            ]
        }
    ); 

    return (
        <>
            {/* <div
                className='flex gap-2'
            >
                {
                    stateSelectedItems.map(
                        (item, index) =>
                        {
                            return(
                                <span
                                    key={index}
                                    className='text-sm'
                                >
                                    {item}
                                </span>
                            )
                        }
                    )
                }
            </div> */}
            
            <AppPromptPasswordVerificationComponent
                show={stateIsActiveModalVerification}
                onSubmitSuccess={handleActionSubmission}
                onClose={() => 
                    {
                        setStateSelectedItem(null);
                        setStateSelectedActionItem(null); 
                        setStateVerificationAction(null);
                        setStateIsActiveModalVerification(false);
                    }
                }
            />

            <AppTableComponent
                pageRoute={'user-management.category'}
                tableName={'categories'}
                columnHeaders={columnHeaders}
                columnRows={columnRows}
                paginationInfo={paginationInfo}
                shouldPreserveState={true}
                filterComponent={<UserManagementCategoryTableFilterComponent pageRoute={'user-management.category'}/>}
            />
        </>
    );
}
