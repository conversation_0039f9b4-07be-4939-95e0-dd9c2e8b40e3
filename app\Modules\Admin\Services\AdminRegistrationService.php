<?php

namespace App\Modules\Admin\Services;

use App\Models\Admin;
use App\Modules\Admin\Constants\AdminStatusConstants;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

use App\Modules\CustomLogger\Services\AuthLogger; 


class AdminRegistrationService
{
    /**
     * Fetch Invitation
     * 
     * @param string $token 
     */
    public function fetchInvitation(string $token)
    {
        $invitation = DB::table('admin_invitations')
            ->where('token', '=', $token)
            ->first(); 

        if ($invitation == null)
        {
            abort(404); 
        }

        if ($invitation->valid_until <= now())
        {
            abort(404);
        }
    
        $admin = DB::table('admins')
            ->where('id', '=', $invitation->user_id)
            ->first(); 

        return [
            'name'  => $admin->name,
            'email' => $admin->email,
            'token' => $token
        ]; 
    }

    /**
     * Register
     * 
     * @param array $data
     * @param string $token
     */
    public function register(array $data, string $token)
    {
        $invitation = DB::table('admin_invitations')
            ->where('token', '=', $token)
            ->first();

        DB::table('admins')
            ->where('id', '=', $invitation->user_id)
            ->update(
                [
                    'name'     => $data['name'],
                    'password' => Hash::make($data['password']),
                    'status'   => AdminStatusConstants::STATUS_ACTIVE
                ]
            );

        //! Fetch the updated admin
        $admin = DB::table('admins')->where('id', $invitation->user_id)->first();

        //! Convert stdClass to array and log in using a custom guard (if needed)
        $adminModel = Admin::find($admin->id);

        //! Automatically log in the user
        Auth::login($adminModel);

        $invitation = DB::table('admin_invitations')
            ->delete($invitation->id);

        app(AuthLogger::class)->info("admin {$data['name']} |  {data['email']} with ID Number {$admin->id} account setup successful");
    }
}
