import React from "react";

import EmptyList from "../../../Components/Billing/EmptyList";
import AdminLayout from "../../../Layouts/AdminLayout";
import PaymentContainer from "../../../Components/Billing/Client/PaymentContainer";
import MultiPaymentContainer from "../../../Components/Billing/Client/MultiPaymentContainer";
import _PaymentSummary from "../../../Constant/_PaymentSummary";
import AccountBalanceContainer from "../../../Components/Billing/Client/AccountBalanceContainer";

export default function ShowPayment({ data, icann_total, summaryData }) {
    return (
        <AdminLayout hideNav={true}>

            {data.length == 0 ? (
                <EmptyList
                    message={"You Don't Have Any Transactions."} />
            ) : (
                <>
                    {(summaryData.type == _PaymentSummary.TYPES.ACCOUNT_BALANCE) ? (
                        <AccountBalanceContainer
                            data={data}
                            icann_total={icann_total}
                            summaryData={summaryData}
                        />
                    ) : (summaryData.type == _PaymentSummary.TYPES.MULTI_CHECKOUT_INVOICE) ? (
                        <MultiPaymentContainer
                            data={data}
                            icann_total={icann_total}
                            summaryData={summaryData}
                        />
                    ) : (<PaymentContainer
                        data={data}
                        icann_total={icann_total}
                        summaryData={summaryData}
                    />)}
                </>
            )}
        </AdminLayout>
    );
}
