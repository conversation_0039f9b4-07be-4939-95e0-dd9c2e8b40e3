//* PACKAGES
import React, {useState, useEffect} from 'react'
import { <PERSON>, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import {
    FiGlobe,
    FiUsers,
    <PERSON><PERSON>ist,
    <PERSON><PERSON>ser,
    FiFileText,
    FiClock,
} from "react-icons/fi";

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialDashboardQuickAccess(props)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const links = 
    [
        {
            icon       : <FiGlobe />,
            title      : "Registry",
            link       : "epp.account",
            hasAccess  : hasPermission('epp.account'),
            description: 'View & Manage Registry'
        },
        {
            icon       : <FiUsers />,
            title      : "Users",
            link       : "user-management.admin",
            hasAccess  : hasPermission('user-management.admin'),
            description: 'View & Manage Users'
        },
        {
            icon       : <FiList />,
            title      : "Category",
            link       : "user-management.category",
            hasAccess  : hasPermission('user-management.category'),
            description: 'View & Manage Categories'
        },
        {
            icon       : <FiUser />,
            title      : "Client",
            link       : "client",
            hasAccess  : hasPermission('client'),
            description: 'View & Manage Clients'
        },
        {
            icon     : <FiList />,
            title    : "General",
            link     : "setting.general",
            hasAccess: hasPermission('setting.general'),
            description: 'View & Manage Settings'
        },
    ];

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <div className="bg-gray-100 p-6 rounded-xl">
            <h2 className="text-lg font-semibold mb-4">Quick Access</h2>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                {
                    links.filter(link => link.hasAccess)
                        .map(
                            (item, i) => (
                            <div
                                key={i}
                                className="p-4 border rounded-lg hover:shadow cursor-pointer group"
                            >
                                <Link href={route(item.link)}>
                                    <div className="text-2xl text-gray-600 mb-2 group-hover:text-blue-600">
                                        {item.icon}
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-sm font-semibold">
                                            {item.title}
                                        </h3>
                                        <span className="text-xl">›</span>
                                    </div>
                                    <p className="text-xs text-gray-500 mt-2">
                                        {item.description}
                                    </p>
                                </Link>
                            </div>
                        ))}
            </div>
        </div>
    );
}
