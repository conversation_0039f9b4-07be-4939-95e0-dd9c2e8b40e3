<?php

use App\Modules\UserManagement\Controllers\UserManagementAdminController; 
use App\Modules\UserManagement\Controllers\UserManagementCategoryController; 
use App\Modules\UserManagement\Controllers\UserManagementRoleController;
use App\Modules\UserManagement\Controllers\UserManagementSettingsController;

use Illuminate\Support\Facades\Route;

Route::middleware(
    [
            'auth', 
            'auth.active',
            'auth.permission.check'
        ]
    )
    ->prefix('user-management')
    ->group(
        function () 
        {
            Route::prefix('admin')
                ->group(
                    function ()
                    {
                        Route::get('',                          [UserManagementAdminController::class, 'index'])->name('user-management.admin');
                        Route::get('create',                    [UserManagementAdminController::class, 'create'])->name('user-management.admin.create');
                        Route::get('edit/{id}',                 [UserManagementAdminController::class, 'edit'])->name('user-management.admin.edit');
                        Route::post('store',                    [UserManagementAdminController::class, 'store'])->name('user-management.admin.store');
                        Route::post('view-permissions/{id}',    [UserManagementAdminController::class, 'fetchPermissions'])->name('user-management.admin.view-permissions');
                        Route::post('invitation-resend/{id}',   [UserManagementAdminController::class, 'resendInvitation'])->name('user-management.admin.invitation-resend');
                        Route::patch('update/{id}',             [UserManagementAdminController::class, 'update'])->name('user-management.admin.update');
                        Route::patch('enable/{id}',             [UserManagementAdminController::class, 'enable'])->name('user-management.admin.enable');
                        Route::patch('disable/{id}',            [UserManagementAdminController::class, 'disable'])->name('user-management.admin.disable');
                        Route::delete('delete/{id}',                          [UserManagementAdminController::class, 'delete'])->name('user-management.admin.delete');
                    }
                );

            Route::prefix('roles')
                ->group(
                    function () 
                    {
                        Route::get('',                         [UserManagementRoleController::class, 'index'])->name('user-management.role');
                        Route::get('create',                   [UserManagementRoleController::class, 'create'])->name('user-management.role.create');
                        Route::get('edit/{id}',                    [UserManagementRoleController::class, 'edit'])->name('user-management.role.edit');
                        Route::post('store',                   [UserManagementRoleController::class, 'store'])->name('user-management.role.store');
                        Route::post('view-permissions/{id}',        [UserManagementRoleController::class, 'fetchPermissions'])->name('user-management.role.view-permissions');
                        Route::post('view-role-permissions',   [UserManagementRoleController::class, 'getRolePermissions'])->name('user-management.role.view-role-permissions');
                        Route::post('update/{id}',                  [UserManagementRoleController::class, 'update'])->name('user-management.role.update');
                        Route::delete('delete/{id}',                              [UserManagementRoleController::class, 'delete'])->name('user-management.role.delete');
                        Route::delete('bulk-delete',                         [UserManagementRoleController::class, 'bulkDelete'])->name('user-management.role.bulk-delete');
                    }
                );

            Route::prefix('category')
                ->group(
                    function () 
                    {
                        Route::get('',                         [UserManagementCategoryController::class, 'index'])->name('user-management.category');
                        Route::get('create',                   [UserManagementCategoryController::class, 'create'])->name('user-management.category.create');
                        Route::get('edit/{id}',                    [UserManagementCategoryController::class, 'edit'])->name('user-management.category.edit');
                        Route::post('store',                   [UserManagementCategoryController::class, 'store'])->name('user-management.category.store');
                        Route::post('view-permissions/{id}',   [UserManagementCategoryController::class, 'fetchPermissions'])->name('user-management.category.view-permissions');
                        Route::post('update/{id}',                  [UserManagementCategoryController::class, 'update'])->name('user-management.category.update');
                        Route::delete('delete/{id}',                              [UserManagementCategoryController::class, 'delete'])->name('user-management.category.delete');
                        Route::delete('bulk-delete',                         [UserManagementCategoryController::class, 'bulkDelete'])->name('user-management.category.bulk-delete');
                    }
                );

            Route::prefix('settings')
                ->group(
                    function () 
                    {
                        Route::get('',                          [UserManagementSettingsController::class, 'index'])->name('user-management.settings');
                        Route::post('sync-permissions',         [UserManagementSettingsController::class, 'syncPermissions'])->name('user-management.settings.sync-permissions');
                        Route::post('category/load-defaults',   [UserManagementSettingsController::class, 'loadDefaultCategories'])->name('user-management.settings.category.load-defaults');
                        Route::post('roles/load-defaults',      [UserManagementSettingsController::class, 'loadDefaultRoles'])->name('user-management.settings.role.load-defaults');
                    }
                );
        }
    );
