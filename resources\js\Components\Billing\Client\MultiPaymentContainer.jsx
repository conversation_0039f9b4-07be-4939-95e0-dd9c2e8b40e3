
import { MdKeyboardBackspace } from "react-icons/md";
import PaymentItem from "./PaymentItem";

export default function MultiPaymentContainer({ data, icann_total, summaryData }) {
    const getReimbursementTotal = () => {
        let total = 0;
        {
            data.map((e) => {
                if (e.reimbursement_total_amount > 0) {
                    // console.log(e.reimbursement_total_amount)
                    total += parseFloat(e.reimbursement_total_amount);
                    // console.log(total)
                }

            })
        }

        return total;
    }

    const formatAmount = (amount) => {
        return amount ? parseFloat(amount).toFixed(2) : "0.00";
    }

    const setDate = date => {
        return new Date(date + 'Z').toDateString() + ' ' + new Date(date + 'Z').toLocaleTimeString();
    }

    const totalReimbursement = getReimbursementTotal();
    const otherFeesTotal = parseFloat(data[0].invoice_paid_amount) - parseFloat(data[0].invoice_total_amount);

    return (
        <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
            <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                <a href="#" onClick={() => window.history.back()}>
                    <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                </a>
                <span className="text-inherit">
                    Payment Invoice
                </span>
                <span className="text-gray-500">{data.length} {(data.length == 1) ? "item" : "items"}</span>

            </div>
            <span>{setDate(data[0].created_at)}</span>
            <div className="flex flex-col space-y-8 pt-8">
                {data.map((e, index) => {
                    return (
                        <PaymentItem
                            key={"pi-" + index}
                            index={index}
                            item={e}
                            isRate={false}
                        />
                    );
                })}
            </div>
            <div className="flex items-center justify-between space-y-8 pt-8 text-gray-600 border-b border-gray-200 pb-2 mb-4 ">
            </div>
            <div className="flex flex-col text-lg text-gray-700 font-semibold">
                {(data[0].node_type == 'REGISTRATION') && (
                    <div className="flex items-center justify-between">
                        <span className=" text-inherit">Registration Total</span>
                        <span className=" text-inherit">${formatAmount(data[0].invoice_paid_amount ?? 0)}</span>
                    </div>
                )}
                {data[data.length - 1].marketplace_payment_invoice_id && (
                    <div className="flex items-center justify-between">
                        <span className=" text-inherit">Premium Total</span>
                        <span className=" text-inherit">${formatAmount(data[data.length - 1].invoice_paid_amount ?? 0)}</span>
                    </div>
                )}
                {icann_total > 0 && (
                    <div className="flex items-center justify-between">
                        <span className=" text-inherit">Total ICANN Fee</span>
                        <span className=" text-inherit">${formatAmount(icann_total)}</span>
                    </div>
                )}
                <div className="flex items-center justify-between">
                    <span className=" text-inherit">Total Paid</span>
                    <span className=" text-inherit">${formatAmount(summaryData.paid_amount ?? 0)}</span>
                </div>
            </div>
        </div>
    );
}
