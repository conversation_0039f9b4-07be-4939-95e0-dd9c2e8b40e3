<?php

namespace App\Console\Commands\Client;

use App\Modules\Client\Services\ClientService;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Console\Command;

class InviteExpirySystemRefund extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:invite-expiry-system-refund';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'InviteExpiry: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('InviteExpiry: Running...');
        
        ClientService::handleExpiry();

        app(AuthLogger::class)->info('InviteExpiry: Done');
    }
}
