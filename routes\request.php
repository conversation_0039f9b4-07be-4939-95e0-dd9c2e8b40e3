<?php

use App\Modules\Guest\Controllers\AdminResponseController;
use App\Modules\Guest\Controllers\GuestRequestController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('request')->group(function () {
    // Route::get('/', [GuestRequestController::class, 'index'])->name('request');
    // Route::patch('/response', [AdminResponseController::class, 'update'])->name('request.response');
    // Route::delete('/delete', [AdminResponseController::class, 'destroy'])->name('request.delete');
});
