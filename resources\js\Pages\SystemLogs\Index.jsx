import React from "react";
import AdminLayout from "@/Layouts/AdminLayout";
import { MdFilterList } from "react-icons/md";
import { router } from "@inertiajs/react";
import Filter from "@/Components/Client/ClientLogs/SystemLogs/Filter";
import CursorPaginate from "@/Components/Util/CursorPaginate";

export default function SystemLogs({ 
    email, 
    logs,
    nextPageUrl,
    previousPageUrl,
    itemCount,
    total = 0,
    onFirstPage,
    onLastPage,
    itemName
}) {
    const groupedLogs = logs?.reduce((acc, log) => {
        const date = log.formatted_created_at;
        if (!acc[date]) {
            acc[date] = [];
        }
        acc[date].push(log);
        return acc;
    }, {}) || {};

    const loadMore = (cursor) => {
        router.visit(route("system.logs"), {
            data: {
                cursor: cursor,
                type: router.page.url.searchParams.get('type'),
                date: router.page.url.searchParams.get('date'),
            },
            preserveState: true,
            preserveScroll: true,
        });
    };

    const currentPageCount = Object.values(groupedLogs).reduce((count, items) => 
        count + items.length, 0
    );

    const { limit = 20 } = route().params ?? {};

    const handleLimitChange = (e) => {
        router.get(route("system.logs"), {
            ...route().params,
            limit: e.target.value,
            page: 1,
        });
    };

    return (
        <AdminLayout>
            <div className="flex flex-col">
                <div className="px-4 w-full">
                    <div className="mx-auto container max-w-[1200px] mt-4 flex flex-col justify-between">
                        <h2 className="text-4xl font-bold mb-4">System Logs</h2>
                        <div
                            className="flex justify-start"
                            style={{ position: "relative", top: "15px" }}
                        >
                            <label className="mr-2 text-sm pt-1 text-gray-600">
                                Show
                            </label>
                            <select
                                value={limit}
                                onChange={handleLimitChange}
                                className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                            >
                                {[20, 25, 30, 40, 50, 100].map((val) => (
                                    <option key={val} value={val}>
                                        {val}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="flex justify-between items-center mt-4 pt-4 pb-4">
                            <div className="flex items-center space-x-2">
                                <MdFilterList className="text-xl" />
                                <span>Filter:</span>
                                <Filter 
                                    routeName="system.logs"
                                />
                            </div>
                        </div>
                        <div className="mt-4">
                            {Object.entries(groupedLogs).length > 0 ? (
                                Object.entries(groupedLogs).map(([date, items]) => (
                                    <React.Fragment key={date}>
                                        <div className="flex items-center mb-4">
                                            <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                                {date}
                                            </div>
                                            <div className="flex-grow border-t border-gray-300 ml-4"></div>
                                        </div>
                                        <div className="space-y-4 mb-8">
                                            {items.map((log, index) => (
                                                <div key={index} className="flex items-start px-6">
                                                    <div className="w-1/5 min-w-[240px] pr-4 pt-4">
                                                        {log.type}
                                                    </div>
                                                    <div className="w-[500px] px-4 pt-4">
                                                        <div className="break-words">
                                                            {log.message}
                                                        </div>
                                                    </div>
                                                    <div className="w-[100px] pl-5 text-left pt-4">
                                                        {new Date(log.created_at).toLocaleString('en-US', {
                                                            hour: '2-digit',
                                                            minute: '2-digit',
                                                            hour12: true
                                                        })}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </React.Fragment>
                                ))
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No System Logs Found
                                </div>
                            )}
                            
                            <CursorPaginate
                                onFirstPage={onFirstPage}
                                onLastPage={onLastPage}
                                nextPageUrl={nextPageUrl}
                                previousPageUrl={previousPageUrl}
                                itemCount={itemCount}
                                total={total}
                                itemName={itemName}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
} 