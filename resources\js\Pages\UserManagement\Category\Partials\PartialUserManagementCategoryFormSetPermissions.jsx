//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { CiSearch } from 'react-icons/ci';
import { MdKeyboardBackspace } from 'react-icons/md';

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import InputError from '@/Components/InputError';
import UserManagementPermissionsReviewModalComponent from '@/Components/UserManagement/UserManagementPermissionsReviewModalComponent';

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserManagementCategoryFormSetPermissions(
    {
        statePermissions = [], 
        setStatePermissions, 
        stateInputName, 
        setStateInputName, 
        stateSearch, 
        setStateSearch, 
        stateInputSelectedPermissions, 
        setStateInputSelectedPermissions, 
        stateErrorMessageName, 
        setStateErrorMessageName,
        stateErrorMessagePermissions, 
        setStateErrorMessagePermissions, 
        handleEventSubmit    = () => alert('submit')
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    const searchTerm          = stateSearch.trim().toLowerCase();
    const filteredPermissions = statePermissions.filter(permission => searchTerm === "" || permission.name.toLowerCase().includes(searchTerm));

    //! STATES
    const [stateIsActiveModalReviewPermissions, setStateIsActiveModalReviewPermissions] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleCheck(e)
    {
        const { id, checked } = e.target;

        setStateInputSelectedPermissions([...stateInputSelectedPermissions, parseInt(id)]);

        if (!checked)
        {
            setStateInputSelectedPermissions(stateInputSelectedPermissions.filter(item => parseInt(item) !== parseInt(id)));
        }
    };

    function handleCheckAll(e)
    {
        if (e.target.checked == true)
        {
            setStateInputSelectedPermissions([]);
            setStateInputSelectedPermissions(statePermissions.map(permission => permission.id));
        }
        else 
        {
            setStateInputSelectedPermissions([]);
        }
    }
    
    function checkIfAllPermissionsSelected(requiredItems, selectedItems)
    {
        return requiredItems.every(p =>
            selectedItems.some(s => s === p.id)
        );
    }

    return (
        <>
            <UserManagementPermissionsReviewModalComponent
                stateIsModalOpen={stateIsActiveModalReviewPermissions}
                selectedPermissions={statePermissions.filter(p => stateInputSelectedPermissions.includes(p.id))}
                handleEventModalClose={
                    () =>
                    {
                        setStateIsActiveModalReviewPermissions(false);
                    }
                }
                handleEventModalConfirm={
                    () => 
                    {
                        handleEventSubmit(); 
                        setStateIsActiveModalReviewPermissions(false);
                    }
                }
            />
            <div
                className='w-full flex flex-col gap-5'
            >
                <div
                    className='flex flex-col lg:w-[50%] gap-3'
                >
                    <label
                        htmlFor="name"
                    >
                        Category Name
                    </label>
                    <input
                        type="text"
                        name="name"
                        value={stateInputName}
                        onChange={(e) => { setStateInputName(e.target.value) }}
                        className='p-4 border border-gray-300 rounded-lg h-[40px]'
                        placeholder='Category Name'
                    />
                    <InputError
                        message={stateErrorMessageName}
                    />
                </div>
                <div
                    className='flex flex-col w-full gap-3'
                >
                    <label>
                        Permissions | {stateInputSelectedPermissions.length} Selected
                    </label>
                    <div
                        className='flex flex-col gap-5 px-5 py-3 border border-gray-300 rounded-md min-h-[550px]'
                    >
                        <div
                            className='flex justify-between items-center w-full border-b border-gray-300 pb-3'
                        >
                            <label
                                className='flex flex-row gap-2 items-center'
                            >
                                <AppInputCheckboxComponent
                                    id={'checkAll'}
                                    name={'checkAll'}
                                    value={true}
                                    isDisabled={statePermissions.length == 0}
                                    isChecked={checkIfAllPermissionsSelected(statePermissions, stateInputSelectedPermissions) && statePermissions.length > 0}
                                    handleEventOnChange={(e) =>  handleCheckAll(e) }
                                />
                                <span className="text-sm">
                                    Select All
                                </span>
                            </label>
                            <div className="relative">
                                <input
                                    type="text"
                                    disabled={statePermissions.length == 0}
                                    value={stateSearch}
                                    onChange={(e) => { setStateSearch(e.target.value); }}
                                    className="pl-10 pr-4 p-2 border border-gray-300 disabled:bg-gray-100 rounded-lg h-[35px]"
                                    placeholder="Search Permissions"
                                />
                                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                                    <CiSearch className="h-6 w-6"/>
                                </div>
                            </div>
                        </div>
                        <div
                            className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5 max-h-[550px] overflow-y-auto'
                        >
                            {
                                filteredPermissions.length === 0
                                    ?
                                        (
                                            <div 
                                                className="col-span-3 h-96 flex items-center justify-center 
                                                text-gray-500"
                                            >
                                                No Permissions Found
                                            </div>
                                        )
                                    :
                                        (
                                            filteredPermissions.map(
                                                (permission, i) =>
                                                (
                                                    <div
                                                        key={i}
                                                        className="flex gap-2 items-center"
                                                    >
                                                        <AppInputCheckboxComponent
                                                            id={permission.id}
                                                            name={permission.name}
                                                            value={permission.id}
                                                            isChecked={stateInputSelectedPermissions.includes(permission.id)}
                                                            handleEventOnChange={(e) => handleCheck(e)}
                                                        />
                                                        <span className="text-sm">{permission.name}</span>
                                                    </div>
                                                )
                                            )
                                        )
                            }
                        </div>
                    </div>
                    <InputError
                        message={stateErrorMessagePermissions}
                    />
                    <div className='flex w-full my-3 justify-end'>
                        <AppButtonComponent
                            type='button'
                            isDisabled={stateInputSelectedPermissions.length == 0}
                            handleEventClick={() => setStateIsActiveModalReviewPermissions(true)}
                        >
                            Review Permissions
                        </AppButtonComponent>
                    </div>
                </div>
            </div>
        </>
    )
}
