import React from "react";
import { Link } from "@inertiajs/react";
export default function PrimaryLink({
    href = "#",
    className = "",
    method = "get",
    children,
    processing,
}) {
    return (
        <Link
            as="button"
            href={href}
            method={method}
            className={
                `block px-4 py-2 bg-primary border border-transparent rounded-md font-semibold text-sm text-white  tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150 ${
                    processing && "opacity-25"
                } ` + className
            }
            disabled={processing}
        >
            {children}
        </Link>
    );
}
