<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Requests\ShowListRequest;
use Inertia\Inertia;

class DomainController extends Controller
{
    public function get(ShowListRequest $request, $id)
    {
        if ($request->get_owner_exists($id)) {
            $data = $request->domains_get($id);
            $data['id'] = $id;
            $data['owner'] = $request->get_domains_owner($id);

            return Inertia::render('Client/DomainIndex', $data);
        } else {
            abort(404);
        }
    }
}
