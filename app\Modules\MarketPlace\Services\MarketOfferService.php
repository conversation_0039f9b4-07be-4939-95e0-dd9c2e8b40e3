<?php

namespace App\Modules\MarketPlace\Services;

use App\Models\AfternicOfferHistory;
use App\Models\AfternicOffers;
use App\Modules\MarketPlace\Mail\MarketOfferMail;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class MarketOfferService
{
    public static function instance()
    {
        $marketOfferService = new self;

        return $marketOfferService;
    }

    public static function getOffers()
    {
        return DB::table('public.afternic_offers AS PA')
        ->select('PA.*', 'PU.first_name', 'PU.last_name')
        ->join('public.users AS PU', 'PU.id', 'PA.user_id')
        ->orderBy('PA.id', 'desc')
        ->get();
    }
}
