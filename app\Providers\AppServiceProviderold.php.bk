<?php

namespace App\Providers;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        DB::macro('client', function () {
            return DB::connection('client');
        });

        Http::macro('client', function () {
            return Http::baseUrl(env('CLIENT_API_URL'))
                ->throw(function (Response $response, RequestException $e) {
                    app(AuthLogger::class)->error($e->getMessage().' ; '.json_encode($response->json()));
                });
        });
    }
}