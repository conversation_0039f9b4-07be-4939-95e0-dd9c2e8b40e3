<?php

use App\Modules\Ip\Controllers\IpAddressController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('ip')->group(function () {
    // Route::get('/', [IpAddressController::class, 'index'])->name('ip');
    // Route::patch('/update-status', [IpAddressController::class, 'update'])->name('ip.update-status');

});
