<?php

namespace App\Providers;

use App\Modules\BillingClient\Contracts\PaymentSummaryPicker;
use App\Modules\BillingClient\Contracts\PaymentSummaryPickerInterface;
use App\Modules\ClientPaymentService\Contracts\PaymentServicePicker;
use App\Modules\ClientPaymentService\Contracts\PaymentServicePickerInterface;
use Illuminate\Support\ServiceProvider;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(PaymentSummaryPickerInterface::class, PaymentSummaryPicker::class);
        $this->app->singleton(PaymentServicePickerInterface::class, PaymentServicePicker::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void {}
}
