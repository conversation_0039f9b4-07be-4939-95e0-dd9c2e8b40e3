<?php

namespace App\Modules\Setting\Services;

use App\Modules\Setting\Constants\TransactionSettings;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class TransactionThresholdService
{
    public static function instance()
    {
        $transactionThresholdService = new self;

        return $transactionThresholdService;
    }

    public function transactionDataQuery(): Builder
    {
        return DB::client()->table('transactions')
            ->select(
                'transactions.id as transaction_id',
                'transactions.name',
                'transactions.system_limit',
                'transactions.user_limit',
                'transactions.length',
                'transaction_triggers.id as trigger_id',
                'transaction_triggers.allow_action',
                'transaction_triggers.notify_subscriber'
            )
            ->join('transaction_triggers', 'transaction_triggers.transaction_id', '=', 'transactions.id');
    }

    public function updateTransactionSettings(string $transactionId, ?array $transactionPayload, ?array $triggerPayload): void
    {
        if (empty($transactionPayload) && empty($triggerPayload)) return;

        $this->updateTransaction($transactionId, $transactionPayload);

        if (array_key_exists('length', $transactionPayload) && $transactionPayload['length'] !== null)
            $this->deactivateSystemObserver($transactionId);
        if (!empty($triggerPayload))
            $this->updateTriggerAction($transactionId, $triggerPayload);
    }

    public function getUpdatePayload(string $transactionId): array
    {
        return (array) $this->transactionDataQuery()
            ->where('transactions.id', $transactionId)->first();
    }

    public function updateAll(array $data)
    {
        $current = $this->transactionDataQuery()->get()->keyBy('name')->toArray();
        $transactionTypes = array_keys($data);

        foreach ($transactionTypes as $type) {
            $transactionId = $data[$type]['transaction_id'];
            $payload = array_diff_assoc($data[$type], (array) $current[$type]);

            if (empty($payload)) continue;

            $transactionPayload = Arr::only($payload, TransactionSettings::TRANSACTION_FIELDS);
            $triggerPayload = Arr::only($payload, TransactionSettings::TRIGGER_FIELDS);

            $this->updateTransactionSettings($transactionId, $transactionPayload, $triggerPayload);
        }
    }

    // PRIVATE Functions

    private function updateTransaction(string $transactionId, array $updateData): void
    {
        $updateData['updated_at'] = Carbon::now();
        DB::client()->table('transactions')->where('id', $transactionId)->update($updateData);
    }

    private function updateTriggerAction(string $transactionId, array $updateData): void
    {
        $updateData['updated_at'] = Carbon::now();

        DB::client()->table('transaction_triggers')
            ->where('transaction_id', $transactionId)
            ->update($updateData);
    }

    private function deactivateSystemObserver(string $transactionId)
    {
        DB::client()->table('system_transaction_observers')
            ->where('transaction_id', $transactionId)
            ->update(['is_active' => false]);
    }
}
