//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    MdExpandMore,
    MdTune,
    MdExpandLess,
    MdOutlineCorporateFare,
    MdOutlineImportExport,
    MdOutlineBugReport,
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationRegistryComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        account: route().current("epp.account") || currentRoute.includes('epp.account'),
        poll   : route().current("epp.poll") || currentRoute.includes('epp.poll'),
        log    : route().current("epp.log") || currentRoute.includes('epp.log'),
    };

    const links = 
    [
        {
            routeName: 'epp.account',
            hasAccess: hasPermission('epp.account'),
            isActive : routes.account,
            icon     : <MdOutlineCorporateFare className="text-2xl"/>,
            label    : 'account'
        }, 
        {
            routeName: 'epp.poll',
            hasAccess: hasPermission('epp.poll'),
            isActive : routes.poll,
            icon     : <MdOutlineImportExport className="text-2xl"/>,
            label    : 'poll request'
        }, 
        {
            routeName: 'epp.log',
            hasAccess: hasPermission('epp.log'),
            isActive : routes.log,
            icon     : <MdOutlineBugReport className="text-2xl"/>,
            label    : 'logs'
        }, 
    ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () =>
    {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span
                    className=" text-inherit"
                >
                    Registry
                </span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
