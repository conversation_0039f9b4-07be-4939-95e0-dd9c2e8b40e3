<?php

namespace App\Exceptions;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Inertia\Inertia;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Routing\Exceptions\InvalidSignatureException;

use Throwable;

class Hand<PERSON> extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->renderable(function (MethodNotAllowedHttpException $e, $request) {

            return Inertia::render('Errors/CustomMessage', [
                'code' => 405,
                'help' => 'Sorry, the way you access the page is not allowed. It is also possible that your session expires',
                'error' => 'Method not allowed',
            ]);
        });

        $this->renderable(function (\Illuminate\Auth\AuthenticationException $e, $request) {
            return Inertia::render('Errors/LoginRequiredPage', [
                'code' => 401,
                'help' => 'Please log in to access this page.',
                'error' => 'Authentication Required',
            ]);
        });

        $this->renderable(function (\Illuminate\Session\TokenMismatchException $e, $request) {
            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 419,
                'help' => 'Your session has expired. Please refresh the page and try again.',
                'error' => 'Page Expired',
            ]);
        });

        $this->renderable(function (InvalidSignatureException $e) {
            return Inertia::render('Errors/LoginRequiredPage',[
                'code'  => 413,
                'help'  => 'This Page has Expired. Please try again by logging in',
                'error' => 'Signature Invalid',
            ]);
        });

        $this->renderable(function (NotFoundHttpException $e, $request) {
            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 404,
                'error' => 'Page Not Found',
                'help' => 'The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.',
            ]);
        });


    
        $this->renderable(function (\Illuminate\Http\Exceptions\ThrottleRequestsException $e, $request) {
            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 429,
                'help' => 'You have made too many requests. Please wait a while before trying again.',
                'error' => 'Too Many Requests',
            ]);
        });

        // $this->renderable(function (Throwable $e, $request) {
        //     return Inertia::render('Errors/BackCustomMessage', [
        //         'code' => 500,
        //         'error' => 'Internal Server Error',
        //         'help' => 'Something went wrong on our end. Please try again later or contact support if the issue persists.',
        //     ]);
        // });

        // MethodNotAllowedHttpException

        //! 403 ERRORS 
        $this->renderable(
            function (HttpException $e, $request) {
                if (
                    $e->getStatusCode() === 403
                    && $e->getMessage() === "You do not have permissions to access this page"
                ) {
                    return Inertia::render('Errors/BackCustomMessage', [
                        'code'  => 403,
                        'error' => 'Access Forbidden',
                        'help'  => $e->getMessage(),
                    ]);
                }
            }
        );

        $this->renderable(
            function (HttpException $e, $request) {
                if (
                    $e->getStatusCode() === 403
                    && $e->getMessage() === "Your account is disabled. Please contact administrators"
                ) {
                    return Inertia::render('Errors/AccountDisabledError', [
                        'code'  => 403,
                        'error' => 'Access Forbidden',
                        'help'  => $e->getMessage(),
                    ]);
                }
            }
        );
    
        $this->reportable(function (Throwable $e) {});
    }

    public function report(Throwable $exception)
    {
        $request = app('request');
        $errorBody = [
            'query' => $request->query(),
            'parameter' => $request->all(),
            'error' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
        ];

        app(AuthLogger::class)->error(json_encode($errorBody));
    }
}
