import SecondaryButton from "@/Components/SecondaryButton";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { router } from "@inertiajs/react";
import { useState } from "react";
import {
    MdOutlineFilterAlt,
    MdOutlineSettings,
    MdOutlineSortByAlpha,
} from "react-icons/md";
import {
    ImSortAlphaAsc,
    ImSortAlphaDesc,
} from 'react-icons/im'
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";
import Item from "../../../Components/Billing/Client/Item";
import BillingClientFilter from "../../../Components/Billing/Client/BillingClientFilter";
import { _BillingClient } from "../../../Constant/_BillingClient";
import EmptyList from "../../../Components/Billing/EmptyList";
import IndexTable from "../../../Components/Billing/Client/IndexTable";

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
    others = [],
}) {
    const { orderby } = route().params?.orderby ? route().params : { orderby: _BillingClient.sortTypes.DATE_DESC };
    
    // Extract limit from route params or set default limit to 10
    const { limit = 10 } = route().params || {}; 

    // Handle limit change (when user selects different number of items per page)
    const handleLimitChange = (event) => {
        const newLimit = event.target.value;
        router.get(route("billing.client", {...route().params, limit: newLimit, page: 1})); // Send updated request
    };

    // Function to toggle order type and send updated request
    const orderToggle = (sort) => {
        const { type, name, email } = route().params;
        let payload = {};

        payload.orderby = sort;
        if (type) payload.type = type;
        if (email) payload.email = email;
        if (name) payload.name = name;
        if (limit) payload.limit = limit;  // Add the limit to the payload

        router.get(route("billing.client", payload));  // Send request with updated params
    }

    // State to manage spinner visibility during loading
    const [hasSpinner, setSpinner] = useState(false);

    // Event listeners for router loading state
    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    // Return empty list message if no items and no filters applied
    if (items.length == 0 && (Object.keys(route().params).length == 0))
        return (
            <AdminLayout hideNav={true}>
                <EmptyList message={"You don't have any transactions."} />
            </AdminLayout>
        );

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-2 flex-wrap min-h-[2rem]">
                    <label className="flex items-center">
                        <MdOutlineFilterAlt />
                        <span className="ml-2 text-sm text-gray-600">Filter:</span>
                    </label>

                    <BillingClientFilter summaryTypes={others.summary_types} />
                </div>
                
                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>
                
                {
                    // If no items are found and filters are applied, show empty list
                    (items.length == 0 && (Object.keys(route().params).length > 0)) ? (
                        <EmptyList message={'No payments found.'} />
                    ) : (
                        // Show index table with items
                        <IndexTable
                            hasSpinner={hasSpinner}
                            orderby={orderby}
                            items={items}
                            orderToggle={orderToggle}
                        />
                    )
                }
                
                {hasSpinner ? " " : (
                    // Show pagination controls when data is loaded
                    <CursorPaginate
                        onFirstPage={onFirstPage}
                        onLastPage={onLastPage}
                        nextPageUrl={nextPageUrl}
                        previousPageUrl={previousPageUrl}
                        itemCount={itemCount}
                        total={total}
                        itemName={itemName}
                    />
                )}
            </div>
        </AdminLayout>
    );
}
