import AdminLayout from '@/Layouts/AdminLayout'
import React from 'react'
import { useState } from 'react';
import DataTable from 'react-data-table-component'
import { customStyles } from './components/customStyles';
import { IoMdRefresh } from "react-icons/io";
import { FaSearch } from 'react-icons/fa';
import { FaRegEdit } from "react-icons/fa";
import SetDomainStatusPopup from './components/SetDomainStatusPopup';

export default function MarketPlaceDomains(props) {

    const [page, setPage] = useState(0);
    const [row, setRow] = useState([]);
    const [modal, setModal] = useState(false);
    const [perPage, setperPage] = useState(10);
    const [totalRows, settotalRows] = useState(0);
    const [domains, setDomains] = useState(props.data);

    const [tempDomains, setTempDomains] = useState(domains);

    const getStatus = (status) => {
        let color = 'bg-primary';

        if(status.toLowerCase() == 'pending') color = `bg-yellow-500`
        else if(status.toLowerCase() == 'completed') color = `bg-green-500`
        else if(status.toLowerCase() == 'cancelled') color = `bg-red-500`

        return <div className='flex'>
            <span className={`w-2 h-2 mt-1 mr-2 rounded-full ${color}`}> </span>
            <span className='capitalize'>{status}</span>
        </div>
    }

    const handlePageChange = async (npage) => {
        setPage(npage)
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        setperPage(newPerPage)
    };

    const handlePopUp = (row) => {
        setRow(row)
        setModal(true)
    }

    const getAction = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-primary px-1 -mt-8'>View History</span>
                <button onClick={() => { }} className='bg-primary rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaSearch className=' font-bold' />
                </button>
            </div>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-green-600 px-1 -mt-8'>Change Status</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-green-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaRegEdit className=' font-bold' />
                </button>
            </div> 
        </div>
    }

    const columns = [
        {
            id: 'User',
            name: 'User',
            selector: row => row.name,
            cell: row => <div className='capitalize'>{`${row.first_name} ${row.last_name}`}</div>,
            sortable: true,
            // width: '150px'
        },
        {
            id: 'Domain',
            name: 'Domain',
            selector: row => row.domain,
            cell: row => <div className='lowercase'>{row.domain}</div>,
            sortable: true,
            // width: '150px'
        },
        {
            id: 'Status',
            name: 'Status',
            selector: row => row.status,
            cell: row => getStatus(row.status),
            sortable: true,
            // width: '170px'
        },
        {
            id: 'Price',
            name: 'Price',
            selector: row => parseInt(row.price),
            cell: row => `$${parseInt(row.price)}`,
            sortable: true,
            width: '125px',
        },
        {
            id: "Actions",
            name: 'Actions',
            selector: row => row.id,
            cell: row => getAction(row),
            width: '110px',
        },
    ];

    return (
        <AdminLayout>
            <SetDomainStatusPopup row={row} modal={modal} showModal={setModal} tempDomains={tempDomains} setTempDomains={setTempDomains} ></SetDomainStatusPopup>
            <div className='mx-auto container max-w-[1200px] mt-20 flex flex-col px-5 rounded-lg '>
                <DataTable
                    columns={columns}
                    data={tempDomains}
                    pagination
                    persistTableHead
                    highlightOnHover
                    customStyles={customStyles}
                    pointerOnHover
                    selectableRows
                    fixedHeader
                    paginationServer
                    paginationTotalRows={totalRows}
                    paginationDefaultPage={page}
                    onChangeRowsPerPage={handlePerRowsChange}
                    onChangePage={handlePageChange}
                    style={{minHeight: '200vh'}}
                    fixedHeaderScrollHeight="100vh"
                    paginationRowsPerPageOptions={[10,25,50,100,250]}
                />
            </div>
        </AdminLayout>
    )
}
