<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('access_category', function (Blueprint $table) {
            //! Drop foreign keys first (use their constraint names or generated names)
            $table->dropForeign(['category_id']);
            $table->dropForeign(['access_id']);

            //! Re-add with cascade on delete
            $table->foreign('category_id')->references('id')->on('category')->onDelete('cascade');
            $table->foreign('access_id')->references('id')->on('access')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('access_category', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropForeign(['access_id']);

            // Re-add original foreign keys without cascade
            $table->foreign('category_id')->references('id')->on('category');
            $table->foreign('access_id')->references('id')->on('access');
        });
    }
};
