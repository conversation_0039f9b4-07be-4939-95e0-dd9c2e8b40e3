<?php

namespace App\Modules\BillingClient\Services\Interfaces;

use App\Modules\BillingClient\Contracts\PaymentSummaryInterface;
use App\Modules\BillingClient\Services\PaymentInvoiceService;
use App\Modules\CustomLogger\Services\UserLoggerTrait;

class DomainPaymentSummary implements PaymentSummaryInterface
{
    use UserLoggerTrait;

    public function getPaymentbyId(int $id, int $userId)
    {
        return PaymentInvoiceService::instance()->getPaymentInvoice($id, $userId);
    }

    public function getRefundbyId(int $id, int $userId)
    {
        return PaymentInvoiceService::instance()->getPaymentReimbursement($id, $userId);
    }
}
