//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//... 

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppPasswordStrengthIndicatorComponent(
    {
        password
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    //...
    
    //! VARIABLES
    //... 

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const getStrength = (password) =>
    {
        const length = password.length;

        if (length === 0) return { label: '', colorClass: '' };
        if (length < 10) return { label: 'Weak', colorClass: 'text-danger' };
        if (length < 16) return { label: 'Medium', colorClass: 'text-yellow-500' };
        return { label: 'Strong', colorClass: 'text-green-500' };
    };

    const strength = getStrength(password);
    
    return (
        <div
            className="text-xs"
        >
            {
                strength.label && (
                <span className={`font-semibold ${strength.colorClass}`}>
                    Password Strength: {strength.label}
                </span>
            )}
        </div>
    );
}
