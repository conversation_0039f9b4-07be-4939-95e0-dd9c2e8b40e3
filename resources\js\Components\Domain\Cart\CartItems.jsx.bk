import { useState, useRef, useEffect } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";
import SecondaryButton from "@/Components/SecondaryButton";
import { MdRemoveShoppingCart, MdAppRegistration } from "react-icons/md";
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
export default function CartItems({ index, domain, handleChange }) {
    const annualPrice = 8; //temporary
    const [yearToggle, setYearToggle] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setYearToggle(false);
    });

    const update = async (id, column, value) => {
        // await axios
        //     .patch(route("mycart.update"), {
        //         id,
        //         column,
        //         value,
        //     })
        //     .then((response) => {
        //         setYearToggle(false);
        //     })
        //     .catch((error) => {
        //         console.log(error.response.data);
        //     });
    };

    const deleteCartItem = async () => {
        // await axios
        //     .delete(route("mycart.delete", domain.id))
        //     .then((response) => {
        //         handleChange(index);
        //     })
        //     .catch((error) => {
        //         console.log(error);
        //     });
    };

    const onHandleChange = (event) => {
        

        update(domain.id, event.target.name, getEventValue(event));
        domain[event.target.name] = getEventValue(event);

        handleChange(index, domain);
    };

    const onHandleChangeYear = (year) => {
        update(domain.id, "year", year);
        domain.year = year;
        handleChange(index, domain);
    };

    const yearList = [...Array(10).keys()].map((i) => (
        <button
            key={"yearlist" + i}
            className="hover:bg-gray-100 px-5 py-1"
            onClick={() => onHandleChangeYear(i + 1)}
        >
            {i + 1} {i > 0 ? " years" : "  year"}
        </button>
    ));

    return (
        <div className={`" space-y-2" }`}>
            <div className="flex items-center justify-between text-gray-600 border-b border-gray-200 pb-2 mb-4">
                <label className=" text-2xl">{domain.name}</label>
                <button onClick={() => deleteCartItem()}>
                    <MdRemoveShoppingCart className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer " />
                </button>
            </div>
            <div
                ref={ref}
                className="flex items-center justify-between hover:bg-gray-50 relative"
            >
                <div className="flex items-center">
                    <MdAppRegistration className=" text-lg" />
                    <span className="ml-2 text-md text-gray-600">
                        Registration
                    </span>
                </div>
                <SecondaryButton
                    name="show_year"
                    onClick={() => setYearToggle(!yearToggle)}
                >
                    <span>
                        ${domain.year * annualPrice} / {domain.year}{" "}
                        {domain.year > 1 ? "years" : "year"}
                    </span>
                </SecondaryButton>
                <DropDownContainer
                    show={yearToggle}
                    className="right-0 -bottom-[3rem] bg-white top-10 h-36 overflow-y-scroll"
                >
                    {yearList}
                </DropDownContainer>
            </div>
            <label className="flex items-center">
                <Checkbox
                    name="privacy"
                    value="COM"
                    checked={domain.privacy}
                    handleChange={onHandleChange}
                />
                <span className="ml-2 text-sm text-gray-600">
                    Privacy protection
                </span>
            </label>
            <label className="flex items-center">
                <Checkbox
                    name="auto_renew"
                    value="COM"
                    checked={domain.auto_renew}
                    handleChange={onHandleChange}
                />
                <span className="ml-2 text-sm text-gray-600">Auto Renew</span>
            </label>
        </div>
    );
}
