<?php

use App\Modules\DomainRedemption\Controllers\DomainRedemptionController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance'])->prefix('domain-redemption')->group(function () {
    Route::get('/', [DomainRedemptionController::class, 'index'])->name('domain-redemption.view');
    Route::post('/create-payment', [DomainRedemptionController::class, 'createPayment'])->name('domain-redemption.create-payment');
    Route::post('/delete', [DomainRedemptionController::class, 'delete'])->name('domain-redemption.delete');
});