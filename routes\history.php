<?php

use Illuminate\Support\Facades\Route;
use App\Modules\EmailHistory\Controllers\EmailHistoryController;
use App\Modules\DomainHistory\Controllers\DomainHistoryController;
use App\Modules\DomainHistory\Controllers\DomainLogController;
use App\Modules\Domain\Controllers\DomainClientHoldController;

Route::middleware(['auth', 'auth.active', 'auth.permission.check'])->prefix('system-email-history')->group(function () {
    Route::get('/', [EmailHistoryController::class, 'index'])->name('email.history');
    Route::get('/download-attachment/{id}', [EmailHistoryController::class, 'downloadAttachment'])->name('email.history.download-attachment');
    /// activity log
});

Route::middleware(['auth', 'auth.active', 'auth.permission.check'])->prefix('domain-history')->group(function () {
    Route::get('/', [DomainHistoryController::class, 'index'])->name('domain.history');
    Route::get('/{id}/logs', [DomainLogController::class, 'showDomainLogs'])->name('domain.history.show');
    Route::post('/client-hold', [DomainClientHoldController::class, 'update'])->name('domain.client-hold.update');
    Route::post('/client-unhold', [DomainClientHoldController::class, 'remove'])->name('domain.client-hold.remove');
});
