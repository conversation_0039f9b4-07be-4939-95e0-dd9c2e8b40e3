import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/Client/Item";

import GuestLayout from "@/Layouts/GuestLayout";
import { router } from "@inertiajs/react";
import { useState } from "react";
import {
    MdOutlineFilterAlt,
    MdOutlineSettings,
    MdOutlineSortByAlpha,
} from "react-icons/md";
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";
export default function Index({ user, disableVerification, disableDeposit }) {

    const [form, setForm] = useState({
        email: user.email || "",
        first_name: "",
        last_name: "",
        apt: "",
        street: "",
        city: "",
        state: "",
        postal_code: "",
        country_code: "",
        password: "",
        password_confirmation: "",
        disableVerification: disableVerification == 1,
        disableDeposit: disableDeposit == 1,
    });

    const handleChange = (e) => {
        setForm({ ...form, [e.target.name]: e.target.value });
    };

    const [errors, setErrors] = useState({});
    const [hasSpinner, setSpinner] = useState(false);

    const submitForm = (e) => {
        e.preventDefault();

        let newErrors = {};

        // Check if passwords match
        if (form.password !== form.password_confirmation) {
            newErrors.password_confirmation = "Passwords do not match.";
        }

        // Check if password is empty
        if (!form.password) {
            newErrors.password = "Password is required.";
        }

        // If there are errors, stop submission
        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            toast.error("Please Fix the Errors Before Submitting.");
            return;
        }

        toast.info("Processing Registration...");

        router.patch("/client/vip/registered", form, {
            onSuccess: () => toast.success("Registration Successfull!"),
            onError: (errors) => {
                toast.error("Registration Failed!");
                console.log(errors);
            },
        });
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <GuestLayout widthsm="sm:max-w-3xl">
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div className="w-full bg-white p-8 rounded-lg shadow-lg">
                    <h2 className="text-2xl font-semibold text-center mb-6">
                        Account Information
                    </h2>
                    <form
                        onSubmit={submitForm}
                        className="w-full mx-auto bg-white p-2 rounded-lg shadow-md"
                    >
                        {/* <!-- Registrant Email --> */}
                        <div className="mb-4">
                            <label
                                for="email"
                                className="block text-gray-700 font-medium"
                            >
                                Registrant Email
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                disabled
                                value={user.email}
                            />
                            <input
                                type="hidden"
                                id="disableVerification"
                                name="disableVerification"
                                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                value={disableVerification == 1}
                            />
                            <input
                                type="hidden"
                                id="disable_deposit"
                                name="disable_deposit"
                                className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                disabled
                                value={disableDeposit == 1}
                            />

                            {/* <p className="text-red-500 text-sm mt-1">{{ $message }}</p> */}
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            {/* <!-- First Name --> */}
                            <div>
                                <label
                                    for="first_name"
                                    className="block text-gray-700 font-medium"
                                >
                                    First Name
                                </label>
                                <input
                                    type="text"
                                    id="first_name"
                                    name="first_name"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="First Name"
                                    value={form.first_name}
                                    onChange={handleChange}
                                />
                                {/* @error('first_name')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>

                            {/* <!-- Last Name --> */}
                            <div>
                                <label
                                    for="last_name"
                                    className="block text-gray-700 font-medium"
                                >
                                    Last Name
                                </label>
                                <input
                                    type="text"
                                    id="last_name"
                                    name="last_name"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="last_name"
                                    value={form.last_name}
                                    onChange={handleChange}
                                />
                                {/* @error('last_name')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-4">
                            {/* <!-- Apartment/Suite --> */}
                            <div>
                                <label
                                    for="apt"
                                    className="block text-gray-700 font-medium"
                                >
                                    Apt, Suite, etc. (optional)
                                </label>
                                <input
                                    type="text"
                                    id="apt"
                                    name="apt"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={form.apt}
                                    onChange={handleChange}
                                />
                            </div>

                            {/* <!-- Street --> */}
                            <div>
                                <label
                                    for="street"
                                    className="block text-gray-700 font-medium"
                                >
                                    Street
                                </label>
                                <input
                                    type="text"
                                    id="street"
                                    name="street"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="Street"
                                    value={form.street}
                                    onChange={handleChange}
                                />
                                {/* @error('street')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-4">
                            {/* <!-- City --> */}
                            <div>
                                <label
                                    for="city"
                                    className="block text-gray-700 font-medium"
                                >
                                    City
                                </label>
                                <input
                                    type="text"
                                    id="city"
                                    name="city"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="City"
                                    value={form.city}
                                    onChange={handleChange}
                                />
                                {/* @error('city')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>

                            {/* <!-- State/Province --> */}
                            <div>
                                <label
                                    for="state"
                                    className="block text-gray-700 font-medium"
                                >
                                    State / Province
                                </label>
                                <input
                                    type="text"
                                    id="state"
                                    name="state"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="State / Province"
                                    value={form.state}
                                    onChange={handleChange}
                                />
                                {/* @error('state')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-4">
                            {/* <!-- Postal Code --> */}
                            <div>
                                <label
                                    for="postal_code"
                                    className="block text-gray-700 font-medium"
                                >
                                    Postal Code
                                </label>
                                <input
                                    type="text"
                                    id="postal_code"
                                    name="postal_code"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="Postal Code"
                                    value={form.postal_code}
                                    onChange={handleChange}
                                />
                                {/* @error('postal_code')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>

                            {/* <!-- Country Code --> */}
                            <div>
                                <label
                                    for="country_code"
                                    className="block text-gray-700 font-medium"
                                >
                                    Country Code
                                </label>
                                <input
                                    type="text"
                                    id="country_code"
                                    name="country_code"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="Country Code"
                                    value={form.country_code}
                                    onChange={handleChange}
                                />
                                {/* @error('country_code')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mt-4">
                            {/* <!-- Password --> */}
                            <div>
                                <label
                                    for="password"
                                    className="block text-gray-700 font-medium"
                                >
                                    Password
                                </label>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    
                                    placeholder="Password"
                                    value={form.password}
                                    onChange={(e) =>
                                        setForm({
                                            ...form,
                                            password: e.target.value,
                                        })
                                    }
                                />
                                {errors.password && (
                                    <p className="text-red-500 text-sm mt-1">
                                        {errors.password}
                                    </p>
                                )}
                                {/* @error('password')
                        <p className="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror */}
                            </div>

                            {/* <!-- Confirm Password --> */}
                            <div>
                                <label
                                    for="password_confirmation"
                                    className="block text-gray-700 font-medium"
                                >
                                    Confirm Password
                                </label>
                                <input
                                    type="password"
                                    id="password_confirmation"
                                    name="password_confirmation"
                                    className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                    placeholder="Confirm Password"
                                    value={form.password_confirmation}
                                    onChange={(e) =>
                                        setForm({
                                            ...form,
                                            password_confirmation:
                                                e.target.value,
                                        })
                                    }
                                />
                                {errors.password_confirmation && (
                                    <p className="text-red-500 text-sm mt-1">
                                        {errors.password_confirmation}
                                    </p>
                                )}
                            </div>
                        </div>
                        {/* <!-- Submit Button --> */}
                        <div className="mt-6">
                            <button
                                type="submit"
                                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition"
                            >
                                Create Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </GuestLayout>
    );
}
