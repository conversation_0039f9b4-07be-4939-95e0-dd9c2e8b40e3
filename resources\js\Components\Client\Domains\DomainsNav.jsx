import { useState } from "react";
import NavLink from "@/Components/NavLink";
import {
    MdDelete,
    MdOutlineHistory,
    MdExpandLess,
    MdExpandMore,
    MdRestore,
    MdAutoDelete,
    MdOutlineRestoreFromTrash
} from "react-icons/md";

export default function DomainsNav({ postRouteName }) {
    const currentRoute = route().current() || postRouteName;

    const routes = {
        history: route().current("domain.history"),
//         pendingDelete: route().current("pending-delete.view"),
        domainRedemption: route().current("domain-redemption.view"),
        pendingDelete: route().current("domain.pending-delete.view"),
        requestDelete: route().current("domain.delete-request.view"),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Domains</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("domain.history")}
                active={routes.history}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdOutlineHistory className="text-2xl " />
                    <span className=" text-inherit">Domains</span>
                </span>

            </NavLink>
            <NavLink
                href={route("domain.pending-delete.view")}
                active={routes.pendingDelete}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdDelete className="text-2xl" />
                    <span className=" text-inherit">Expired</span>
                </span>

            </NavLink>
            <NavLink
                href={route("domain-redemption.view")}
                active={routes.domainRedemption}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdOutlineRestoreFromTrash className="text-2xl" />  
                    <span className=" text-inherit">Deleted</span>
                </span>

            </NavLink>
            <NavLink
                href={route("domain.delete-request.view")}
                active={routes.requestDelete}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdAutoDelete className="text-2xl" />
                    <span className=" text-inherit">Delete Request</span>
                </span>

            </NavLink>
        </>
    );
}
