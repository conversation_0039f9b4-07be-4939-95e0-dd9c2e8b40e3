<?php

namespace App\Traits\Epp;

trait PollCursorPaginate
{
    private static $size = 10;

    private static $sessionCursor = 'pollCursor';

    public static function cursor(array $cursor, array $param = [])
    {
        $isPrev = false;

        if (empty($cursor['data'])) {
            return self::getEmptyPage($cursor, $param);
        }

        if (array_key_exists('isPrev', $param)) {
            $isPrev = ($param['isPrev'] == '1') ? true : false;
        }

        if ($isPrev) {
            // dd(self::getPreviousPage($cursor, $param));
            return self::getPreviousPage($cursor, $param);
        }

        // dd(self::getNextPage($cursor, $param));

        return self::getNextPage($cursor, $param);
    }

    private static function getEmptyPage(array $cursor, array $param = [])
    {
        $onFirstPage = true;
        $onLastPage = true;
        $previousPageUrl = $cursor['url'];
        $nextPageUrl = $cursor['url'];

        return [
            'onFirstPage' => $onFirstPage,
            'onLastPage' => $onLastPage,
            'nextPageUrl' => $nextPageUrl,
            'previousPageUrl' => $previousPageUrl,
            'items' => $cursor['data'],
        ];
    }

    private static function getPreviousPage(array $cursor, array $param = [])
    {
        $onFirstPage = false;
        $onLastPage = false;
        $previousPageUrl = $cursor['url'];
        $nextPageUrl = $cursor['url'];

        $prevCursor = self::getSessionCursorKey();
        if (($param['cursor'] != $prevCursor)) {
            $prevCursor = PollCursorPaginate::getSessionCursorKey(true);
        }

        if (count(session()->get(self::$sessionCursor)) == 1) {
            $prevCursor = '';
        }

        if (session()->has(self::$sessionCursor) && empty(session()->get(self::$sessionCursor))) {
            $onFirstPage = true;
            $param['cursor'] = '';
            $previousPageUrl = $previousPageUrl . self::getParams($param, true);
            $param['cursor'] = $cursor['data']['cursor'];
            $nextPageUrl = $nextPageUrl . self::getParams($param);
        } else {
            $param['cursor'] = $prevCursor;
            $previousPageUrl = $previousPageUrl . self::getParams($param, true);
            $param['cursor'] = $cursor['data']['cursor'];
            $nextPageUrl = $nextPageUrl . self::getParams($param);
        }

        if (array_key_exists('polls', $cursor['data'])) {
            if (count($cursor['data']['polls']) < self::$size) {
                $onLastPage = true;
            }
        }

        if (array_key_exists('polls', $cursor['data'])) {
            if (empty($cursor['data']['polls'])) {
                $onLastPage = true;
            }
        }

        return [
            'onFirstPage' => $onFirstPage,
            'onLastPage' => $onLastPage,
            'nextPageUrl' => $nextPageUrl,
            'previousPageUrl' => $previousPageUrl,
            'items' => $cursor['data'],
        ];
    }

    private static function getNextPage(array $cursor, array $param = [])
    {
        $onFirstPage = false;
        $onLastPage = false;
        $previousPageUrl = $cursor['url'];
        $nextPageUrl = $cursor['url'];

        $prevCursor = self::getSessionCursorKey();

        if ($param) {
            if (empty(trim($param['cursor']))) {
                $onFirstPage = true;
                $param['cursor'] = '';
                $previousPageUrl = $previousPageUrl . self::getParams($param, true);
                $param['cursor'] = $cursor['data']['cursor'];
                $nextPageUrl = $nextPageUrl . self::getParams($param);
                // session()->pull(self::$sessionCursor);
            } else {
                session()->push(self::$sessionCursor, $param['cursor']);
                $param['cursor'] = $prevCursor;
                $previousPageUrl = $previousPageUrl . self::getParams($param, true);
                $param['cursor'] = $cursor['data']['cursor'];
                $nextPageUrl = $nextPageUrl . self::getParams($param);
            }
        }

        if (array_key_exists('polls', $cursor['data'])) {
            if (count($cursor['data']['polls']) < self::$size) {
                $onLastPage = true;
            }
        }
        if (array_key_exists('polls', $cursor['data'])) {
            if (empty($cursor['data']['polls'])) {
                $onLastPage = true;
            }
        }

        return [
            'onFirstPage' => $onFirstPage,
            'onLastPage' => $onLastPage,
            'nextPageUrl' => $nextPageUrl,
            'previousPageUrl' => $previousPageUrl,
            'items' => $cursor['data'],
        ];
    }

    private static function getParams(array $param = [], bool $isPrev = false)
    {
        $link = '?';
        $param['isPrev'] = $isPrev ? '1' : '0';

        if (count($param) > 0) {
            foreach ($param as $key => $p) {
                $link = $link . $key . '=' . $p . '&';
            }
            $param = '&' . implode('&', $param);
        } else {
            $link = '';
        }

        return $link;
    }

    public static function getSessionCursorKey($pull = false)
    {
        if (! session()->has(self::$sessionCursor)) {
            return '';
        }

        $cursor = session()->get(self::$sessionCursor);
        if ($pull) {
            array_pop($cursor);
            session([self::$sessionCursor => $cursor]);
        }

        $last = end($cursor);

        return $last;
    }
}
