function BankTransferNotification({ emailData, subject, links }) {
    const data = JSON.parse(emailData);

    if (subject === "Your Bank Transfer Request is Pending - StrangeDomains") {
        return (
            <div
                className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500 text-sm"
                style={{
                    fontFamily:
                        "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
                }}
            >
                <div className="p-9 bg-white max-w-xl w-full">
                    <p className="font-bold mb-2">Dear {data.name}</p>
                    <p className="mt-4">
                        We are pleased to inform you that your bank transfer
                        request has been successfully submitted. Your request is
                        now under review and is currently pending.
                    </p>
                    <p className="mt-4">
                        We appreciate your patience and will update you as soon
                        as there is any progress on your request.
                    </p>
                    <p className="mt-4">
                        If you have any questions or need assistance, feel free
                        to contact us at{" "}
                        <a href={links.CONTACT_US_PAGE} className="text-blue-600 underline italic">
                            StrangeDomains.com{" "}
                        </a>
                        . Or call us at{" "}
                        <strong className="text-gray-600 italic">
                            +**********
                        </strong>
                    </p>
                    <p className="mt-4">
                        <p className="font-bold">Sincerely,</p>
                        <br />
                        <p className="font-bold">Strange Domains</p>
                    </p>
                </div>
            </div>
        );
    }

    if (
        subject ===
        "Contact Support for Your Bank Transfer Request - StrangeDomains"
    ) {
        return (
            <div
                className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500 text-sm"
                style={{
                    fontFamily:
                        "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
                }}
            >
                <div className="p-9 bg-white max-w-xl w-full">
                    <p className="font-bold mb-2">Dear {data.name}</p>
                    <p className="mt-4">
                        Thank you for reaching out regarding your bank transfer
                        request. To proceed with your request, we require
                        additional supporting documents.
                    </p>
                    <p className="mt-4">
                        Kindly contact our customer support team at +**********
                        at your earliest convenience. Or send us a message at
                        <a href={links.CONTACT_US_PAGE} className="text-blue-600 underline italic">
                            {" "}
                            StrangeDomains.com
                        </a>
                        .
                    </p>
                    <p className="mt-4">
                        Our team will be happy to guide you on the specific
                        documents needed and assist you through the process, We
                        appreciate your cooperation and look forward to
                        resolving this for you.
                    </p>
                    <p className="mt-4">
                        <p className="font-bold">Sincerely,</p>
                        <br />
                        <p className="font-bold">Strange Domains</p>
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div
            className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500 text-sm"
            style={{
                fontFamily:
                    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
            }}
        >
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">Dear {data.name}</p>
                <p className="mt-4">
                    Unfortunately, we were unable to verify your bank transfer
                    request.
                </p>
                <p className="mt-4">
                    Please try a new request or select another payment method to
                    add funds to your account by visiting {" "}
                    <a href={links.ACCOUNT_BALANCE_INDEX} className="text-blue-600 underline italic">
                        {" "}this link
                    </a>
                </p>
                <p className="mt-4">
                    If you have any questions or need assistance, feel free to
                    contact us at{" "}
                    <a href={links.CONTACT_US_PAGE} className="text-blue-600 underline italic">
                        StrangeDomains.com
                    </a>
                    . Or call us at{" "}
                    <strong className="text-gray-600 italic">
                        +**********
                    </strong>
                </p>
                <p className="mt-4">
                    <p className="font-bold">Sincerely,</p>
                    <br />
                    <p className="font-bold">Strange Domains</p>
                </p>
            </div>
        </div>
    );
}

export default BankTransferNotification;
