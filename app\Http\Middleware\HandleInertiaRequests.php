<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Inertia\Middleware;
use Illuminate\Support\Facades\Cache; 
use Illuminate\Support\Facades\DB;

use Tightenco\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        return array_merge(
            parent::share($request), 
            [
                'auth' => 
                [
                    'user' =>  fn() => $request->user()
                        ? 
                            array_merge(
                                $request->user()->only(['id', 'name', 'email']), // adjust fields as needed
                                [
                                    'permissions' => $this->getUserPermissions($request->user()->id),
                                    // 'isSuperAdmin' => $request->user()->is_super_admin ?? false
                                ]
                            )
                        : 
                            null,
                ],
                'ziggy' => function () use ($request) 
                {
                    return array_merge(
                        (new Ziggy)->toArray(), 
                        [
                            'location' => $request->url(),
                        ]
                    );
                },
                'registryArray' => fn () => $request->session()->get('registryArray'),
                'registryFlag' => 
                [
                    'verisign' => fn () => $request->session()->get('verisign_flag'),
                    'pir'      => fn () => $request->session()->get('pir_flag'),
                ],
                'registryWarning' => 
                [
                    'verisign' => fn () => $request->session()->get('verisign_warning'),
                    'pir'      => fn () => $request->session()->get('pir_warning'),
                ],
                'flash' => 
                [
                    'route_message'  => fn () => $request->session()->get('route_message'),
                    'successMessage' => fn()  => $request->session()->get('successMessage'),
                ],
            ]
        );
    }

    protected function getUserPermissions($adminId)
    {
        return Cache::remember(
            "user_permissions_{$adminId}", 
            60, 
            function () use ($adminId) 
            {
                return DB::table('admin_access')
                    ->select('access.name')
                    ->join('access', 'admin_access.access_id', '=', 'access.id')
                    ->where('admin_access.admin_id', $adminId)
                    ->pluck('access.name')
                    ->toArray();
            }
        );
    }
}
