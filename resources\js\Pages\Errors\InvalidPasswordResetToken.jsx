import React from "react";
import PrimaryLink from "@/Components/PrimaryLink";
export default function InvalidPasswordResetToken({ errors }) {
    return (
        <section className="flex items-center h-screen p-16 ">
            <div className="container flex flex-col items-center justify-center px-5 mx-auto my-8">
                <div className="max-w-md text-center">
                    <h2 className="mb-8 font-extrabold text-9xl text-primary">
                        498
                    </h2>
                    <p className="text-2xl font-semibold md:text-3xl text-gray-700">
                        Invalid Token
                    </p>
                    <p className="mt-4 mb-8 dark:text-gray-400">
                        {errors.message}
                    </p>
                    <div className="flex justify-center">
                        <PrimaryLink href={route("home")} method="get">
                            <span className="text-inherit">Home</span>
                        </PrimaryLink>
                    </div>
                </div>
            </div>
        </section>
    );
}
