//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import useOutsideClick from "@/Util/useOutsideClick";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";

//* ICONS
//...

//* COMPONENTS
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementAdminTableFilterComponent(
    {
        pageRoute = 'user-management.admin'
    }
)
{
    //! PACKAGE
    const { orderBy, name, email, status} = route().params;
    const refContainer                    = useRef();

    //! VARIABLES
    const [stateInputName, setStateInputName]   = useState(name || "");
    const [stateInputEmail, setStateInputEmail] = useState(email || "");

    const config =
    {
        container:
        {
            active: false,
        },
        field:
        {
            orderBy:
            {
                active: false,
                value: orderBy ? [orderBy] : [],
                type: "option",
                items:
                [
                    "name:desc",
                    "name:asc",
                    "email:desc",
                    "email:asc",
                    "status:desc",
                    "status:asc",
                    "permissions:desc",
                    "permissions:asc",
                    "createdAt:desc",
                    "createdAt:asc",
                ],
                name: "Order By",
            },
            status:
            {
                active: false,
                value: status ? [status] : [],
                type: "option",
                items:
                [
                    'ACTIVE', 
                    'DISABLED', 
                    'PENDING'
                ],
                name: "Status",
            },
            name:
            {
                active   : false,
                value    : name ? [name]: [],
                type     : "text",
                name     : "Name",
                tempValue: stateInputName,
            },
            email:
            {
                active   : false,
                value    : email ? [email]: [],
                type     : "text",
                name     : "Email",
                tempValue: stateInputEmail,
            },
        },
    };
    //! STATES
    const [filter, setFilter]         = useState(config);
    const { field } = filter;

    //! FUNCTIONS
    useOutsideClick(
        refContainer,
        () =>
        {
            setFilter(
                (prevFilter) =>
                {
                    const updatedFilter = offFilter(prevFilter);

                    return {
                        ...updatedFilter,
                        field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                            ...acc,
                            [key]: {
                                ...updatedFilter.field[key],
                                active: false
                            }
                        }), {})
                    };
                }
            );
        }
    );

    function handleSubmit(updatedFilter)
    {
        let { orderBy, name, email, status} = updatedFilter.field;
        let payload = {};

        if (orderBy.value.length > 0) payload.orderBy = orderBy.value[0];
        if (status.value.length > 0) payload.status = status.value[0];
        if (name.value.length > 0) payload.name       = name.value[0];
        if (email.value.length > 0) payload.email     = email.value[0];

        router.get(
            route(pageRoute),
            payload, 
            {
                preserveState: false, 
                replace : true, 
            }
        ); 
    };

    function handleDisplayToggle(newObject)
    {
        const closedFilter = offFilter(filter);
        
        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    function handleFieldUpdateValue(key, value)
    {
        if (key == "name")
        {            
            setStateInputName(value);
            
            if (!value || value === stateInputName)
            {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter =
                {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue }
                    },
                };

                setFilter(offFilter(updatedFilter));
                handleSubmit(updatedFilter);
                
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    name: {
                        ...prevFilter.field.name,
                        tempValue: value
                    }
                }
            }));

            return;
        }

        if (key == "email")
        {                        
            setStateInputEmail(value);
            
            if (!value || value === stateInputEmail)
            {                
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter =
                {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue }
                    },
                };

                setFilter(offFilter(updatedFilter));
                handleSubmit(updatedFilter);
                
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));

            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        handleSubmit(updatedFilter);
    };

    return (
        <div
            className="flex items-center relative"
            ref={refContainer}
        >
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div
                className="relative"
            >
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderBy}
                    fieldKey="orderBy"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <TextFilter
                    fieldProp={field.name}
                    fieldKey="name"
                    placeholder='Search Name'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () =>
                        {
                            const currentValue = field.name.tempValue || field.name.value[0] || "";

                            handleFieldUpdateValue("name", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />

                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    placeholder='Search Email'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={
                        () =>
                        {
                            const currentValue = field.email.tempValue || field.email.value[0] || "";

                            handleFieldUpdateValue("email", currentValue);
                            setFilter(offFilter(filter));
                        }
                    }
                />
            </div>
        </div>
    );
}
