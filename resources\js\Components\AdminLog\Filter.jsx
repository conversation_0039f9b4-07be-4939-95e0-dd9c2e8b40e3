import React, { useRef, useState } from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import { updateFieldValue, offFilter } from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import useOutsideClick from "@/Util/useOutsideClick";

export default function Filter({ routeName }) {
    const { type, date } = route().params;
    const containerRef = useRef();

    const [filterOrder, setFilterOrder] = useState([]);

    const config = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            type: {
                active: false,
                value: type ? [type] : [],
                type: "option",
                items: [
                    "Login",
                    "Logout",
                    "Payment Created",
                    "Domain Deleted",
                    "Domain Restored",
                    "Domain Update",
                    "Client Delete",
                    "Client Invite",
                    "Bank Transfer Verified",
                    "Bank Transfer Rejected",
                    "Notification Created",
                    "User Management",
                ],
                name: "Type",
            },
            date: {
                active: false,
                value: date ? [date] : [],
                type: "option",
                items: ["today", "yesterday", "last 7 days", "last 30 days"],
                name: "Date",
            },
        },
    };

    const [filter, setFilter] = useState(config);


    React.useEffect(() => {
        const initialOrder = [];
        if (type) initialOrder.push('type');
        if (date) initialOrder.push('date');
        setFilterOrder(initialOrder);
    }, []);

    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        const { type, date } = updatedFilter.field;
        let payload = {};

        if (type.value.length > 0) payload.type = type.value[0];
        if (date.value.length > 0) payload.date = date.value[0];

        router.get(route(routeName), payload, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        const newValue = updateFieldValue(value, {
            ...filter.field[key],
        });

        const reload = forceReload || !(newValue.value.length === 0 && value !== "");

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                ...Object.keys(filter.field).reduce((acc, fieldKey) => ({
                    ...acc,
                    [fieldKey]: {
                        ...filter.field[fieldKey],
                        active: false
                    }
                }), {}),
                [key]: { ...newValue, active: false },
            },
        };

        setFilter(updatedFilter);

        if (newValue.value.length > 0) {
            setFilterOrder(prev => {
                const newOrder = prev.filter(k => k !== key);
                return [...newOrder, key];
            });
        } else {
            setFilterOrder(prev => prev.filter(k => k !== key));
        }
        if (reload) {
            submit(updatedFilter);
        }
    };

    const orderedField = filterOrder.reduce((acc, key) => {
        if (field[key].value.length > 0) {
            acc[key] = field[key];
        }
        return acc;
    }, {});

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={orderedField}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={filter.field.type}
                    fieldKey="type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.date}
                    fieldKey="date"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}