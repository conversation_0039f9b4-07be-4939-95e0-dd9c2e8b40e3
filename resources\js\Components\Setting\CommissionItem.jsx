import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import { getEventValue } from "@/Util/TargetInputEvent";
import useOutsideClick from "@/Util/useOutsideClick";
import { router } from "@inertiajs/react";
import { useRef, useState } from "react";
import {
    MdMoreVert,
    MdOutlineCheckCircleOutline,
    MdOutlineStopCircle,
} from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import { toast } from "react-toastify";
export default function CommissionItem({ item, isSelected, onCheckboxChange }) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleStatusUpdate = (status) => {
        setShow(false);
        toast.info("Updating Status");
        router.patch(route("setting.commission-update-status"), {
            id: item.id,
            value: status,
        });
    };

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, getEventValue(e));
    };

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <Checkbox
                        checked={isSelected}
                        handleChange={(e) => handleCheckboxChange(e)}
                    />

                    <span className=" font-semibold cursor-pointer">
                        {item.minimum_total_node}
                    </span>
                </label>
            </td>
            <td>
                <span className=" lowercase">{item.fee_type}</span>
            </td>
            <td>
                <span>{item.percent_rate}%</span>
            </td>
            <td>
                <span>{item.fix_rate}</span>
            </td>

            <td>
                {item.is_active ? (
                    <MdOutlineCheckCircleOutline className="text-success text-lg" />
                ) : (
                    <MdOutlineStopCircle className="text-gray-400 text-lg" />
                )}
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <button
                            className={`hover:bg-gray-100 px-5 py-1 ${
                                item.is_active && "hidden"
                            }`}
                            onClick={() => handleStatusUpdate(1)}
                        >
                            Enable
                        </button>

                        <button
                            className={`hover:bg-gray-100 px-5 py-1 ${
                                !item.is_active && "hidden"
                            }`}
                            onClick={() => handleStatusUpdate(0)}
                        >
                            Disable
                        </button>
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
