<?php

namespace App\Modules\AdminCredit\Requests;

use App\Modules\AdminCredit\Services\AdminCreditService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SystemCreditStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'amount' => 'required',
            'description' => 'required',
            'type' => ['required', Rule::in(['credit', 'debit'])]
        ];
    }

    public function store() : void
    {
        AdminCreditService::instance()->store($this->all());
    }
}
