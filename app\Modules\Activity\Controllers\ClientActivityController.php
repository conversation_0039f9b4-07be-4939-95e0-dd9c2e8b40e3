<?php

namespace App\Modules\Activity\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Activity\Services\ClientActivityService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ClientActivityController extends Controller
{
    public function index()
    {
        return Inertia::render('Activity/Client', ['log' => ClientActivityService::get(0)]);
    }

    public function more(Request $request)
    {
        $day = $request->has('page') ? $request->page : 0;

        return response()->json(['log' => ClientActivityService::get($day)], 200);
    }
}
