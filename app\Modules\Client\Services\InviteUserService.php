<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Mail\UserInviteMail;
use App\Modules\AdminCredit\Services\AdminCreditService;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class InviteUserService
{
    public static function sendInvitation(array $dataRequest)
    {
        $email = $dataRequest['email'];
        $balance = $dataRequest['balance'];

        if (self::checkEmail($email)) {
            return false;
        }

        if ($dataRequest['default_balance'] && ($balance > 0.00)) {
            if (!(self::checkRunningBalance($balance))) return false;
        }

        $invitationId = Str::uuid();
        $invitationLink = config('app.client_url') . "/client-invite/{$invitationId}";
        $expirationTimestamp = Carbon::now()->addDays(30)->timestamp;

        $userId = self::createUser($email, $dataRequest);

        self::trackUserInvite($userId, $invitationId, $email, $invitationLink, $expirationTimestamp, $dataRequest);

        self::sendInvitationEmail($email, $invitationLink, $expirationTimestamp);

        self::deductFromSystemCredit($email, $balance, $userId);

        self::logInvitationAdminAction($email, $dataRequest);

        return true;
    }

    public static function resendInvitation($userId)
    {
        $inviteData = self::inviteData($userId);

        $invitationId = Str::uuid();
        $invitationLink = config('app.client_url') . "/client-invite/{$invitationId}";
        $expirationTimestamp = Carbon::now()->addDays(30)->timestamp;

        self::updateUserInvite($inviteData->id, $invitationId, $invitationLink, $expirationTimestamp);

        self::sendInvitationEmail($inviteData->email, $invitationLink, $expirationTimestamp);

        self::logResendInvitationAdminAction($inviteData->email);

        return true;
    }

    private static function checkRunningBalance($balance)
    {
        $credit = AdminCreditService::instance()->balance();
        $running_balance = ($credit === null || $credit?->running_balance == 0) ? 0 : $credit->running_balance;
        return (floatval($running_balance) - floatval($balance)) >= 0.00;
    }

    private static function deductFromSystemCredit($email, $balance, $userId): void
    {
        if ($balance <= 0.00) return;

        $array = [
            'type' => 'credit',
            'amount' => $balance,
            'description' => 'Credit for invited user: ' . $email,
        ];

        AdminCreditService::instance()->store($array, $userId);
    }

    private static function checkEmail($email)
    {
        return DB::client()
            ->table('users')
            ->where('email', $email)
            ->whereNull('deleted_at')
            ->first();
    }
    private static function createUser($email, $data)
    {
        $account_credite_setup = ($data['disable_deposit'] ?? false) === true ? true : 0;

        $user = DB::client()->table('users')
            ->where('email', $email)
            ->first();

        if ($user) {
            // Update existing (and restore if soft-deleted)
            DB::client()->table('users')
                ->where('id', $user->id)
                ->update([
                    'deleted_at' => null,
                    'contact_setup' => false,
                    'account_credit_setup' => $account_credite_setup,
                ]);
            $userId = $user->id;
        } else {
            // Insert new and get ID
            $userId = DB::client()->table('users')->insertGetId([
                'email' => $email,
                'first_name' => $email,
                'last_name' => $email,
                'password' => bcrypt(Str::random(32)),
                'street' => '',
                'city' => '',
                'state_province' => '',
                'postal_code' => '8000',
                'country_code' => 'PH',
                'is_active' => false,
                'is_invited' => true,
                'is_identity_verified' => ($data['disable_verification'] ?? 0) == 1,
                'account_credit_setup' => $account_credite_setup,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        return $userId;
    }

    private static function trackUserInvite($userId, $invitationId, $email, $invitationLink, $expirationTimestamp, $dataRequest)
    {
        $now = now();
        $expiresAt = Carbon::createFromTimestamp($expirationTimestamp)->toDateTimeString();
        $additionalOptions = json_encode([
            'disable_verification' => $dataRequest['disable_verification'] ?? 0,
            'disable_deposit'      => $dataRequest['disable_deposit'] ?? 0,
            'balance'              => $dataRequest['balance'] ?? 0,
        ]);

        $existingInvite = DB::client()->table('user_invites')
            ->where('email', $email)
            ->first();

        $inviteData = [
            'uuid'               => $invitationId,
            'invite_url'         => $invitationLink,
            'expires_at'         => $expiresAt,
            'additional_options' => $additionalOptions,
            'updated_at'         => $now,
        ];

        if ($existingInvite) {
            // Restore and update the user invite
            DB::client()->table('user_invites')
                ->where('id', $existingInvite->id)
                ->update(array_merge($inviteData, [
                    'deleted_at' => null,
                    'status'     => 'pending',
                    'created_at' => $now, // Optional: only if you want to reset this
                ]));
        } else {
            DB::client()->table('user_invites')->insert(array_merge($inviteData, [
                'admin_id'  => Auth::id(),
                'user_id'   => $userId,
                'email'     => $email,
                'created_at' => $now,
            ]));
        }
    }

    private static function updateUserInvite($inviteId, $invitationId, $inviLink, $expTimestamp)
    {
        $now = now();
        $expiresAt = Carbon::createFromTimestamp($expTimestamp)->toDateTimeString();

        DB::client()->table('user_invites')
            ->where('id', $inviteId)
            ->update([
                'uuid' => $invitationId,
                'invite_url'     => $inviLink,
                'expires_at' => $expiresAt,
                'updated_at' => $now,
            ]);
    }


    private static function sendInvitationEmail($email, $inviteUrl, $expiration)
    {
        $invite = [
            'subject' => 'User Registration Invitation',
            'greeting' => 'Greetings!',
            'body' => 'You are invited to register for Strange Domain. Click the link below to complete your registration.',
            'url' => $inviteUrl,
            'text' => Carbon::createFromTimestamp($expiration)->format('Y-m-d H:i:s'),
            'sender' => 'StrangeDomains Support',
        ];

        Mail::to($email)->send(new UserInviteMail($invite));
        self::emailTrack($email, $invite);
        
    }

    private static function emailTrack($email, array $invite){
        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => null,
                'name' => $email,
                'recipient_email' => $email,
                'subject' => 'User Registration Invitation',
                'email_type' => 'User Invite',
                'email_body' => json_encode($invite),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
        return $emailSent;
    }

    public static function checkIfEmailExists($email)
    {
        return DB::connection('client')
            ->table('users')
            ->where('email', $email)
            ->whereNull('deleted_at')
            ->exists();
    }
    public static function inviteData($userID)
    {
        return DB::connection('client')
            ->table('user_invites')
            ->where('user_id', $userID)
            ->where('status', 'pending')
            ->whereNull('deleted_at')
            ->first();
    }

    private static function logInvitationAdminAction(string $email, array $dataRequest): void
    {
        $message = self::buildInvitationMessage($email, $dataRequest);

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::CLIENT_INVITE,
            $message
        ));
    }

    private static function logResendInvitationAdminAction(string $email): void
    {
        $message = "Client invitation resent to: {$email} by " . auth()->user()->email;

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::CLIENT_INVITE_RESEND,
            $message
        ));
    }

    private static function buildInvitationMessage(string $email, array $dataRequest): string
    {
        $adminEmail = auth()->user()->email;
        $balance = $dataRequest['balance'] ?? 0;
        $disableVerification = ($dataRequest['disable_verification'] ?? 0) == 1;
        $disableDeposit = ($dataRequest['disable_deposit'] ?? 0) == 1;
        $defaultBalance = ($dataRequest['default_balance'] ?? false) && ($balance > 0);

        $message = "Client invited: {$email} by {$adminEmail}";

        $actions = [];

        if ($disableVerification) {
            $actions[] = "Identity verification disabled";
        }

        if ($disableDeposit) {
            $actions[] = "Initial deposit disabled";
        }

        if ($defaultBalance) {
            $actions[] = "Default account balance set to $" . number_format($balance, 2);
        }

        if (!empty($actions)) {
            $message .= " - Actions: " . implode(', ', $actions);
        }

        return $message;
    }
}
