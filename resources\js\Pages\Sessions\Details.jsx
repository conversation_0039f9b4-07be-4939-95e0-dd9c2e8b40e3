import AdminLayout from "@/Layouts/AdminLayout";
import { usePage } from "@inertiajs/react";
import {
    MdArrowBack,
    MdDesktopMac,
    MdKeyboardBackspace,
    MdLaptopWindows,
    MdOutlineTabletAndroid,
    MdOutlineTabletMac,
} from "react-icons/md";
import { FaChrome, FaEdge, FaSafari } from "react-icons/fa";

// Function to get OS icon
const sessionOS = (os) => {
    switch (os) {
        case "Windows":
            return <MdLaptopWindows className="text-2xl" />;
        case "iOS":
            return <MdOutlineTabletMac className="text-2xl" />;
        case "AndroidOS":
            return <MdOutlineTabletAndroid className="text-2xl" />;
        default:
            return <MdDesktopMac className="text-2xl" />;
    }
};

// Function to get Browser icon
const sessionBrowser = (browser) => {
    switch (browser) {
        case "Chrome":
            return <FaChrome className="text-3xl" />;
        case "Edge":
            return <FaEdge className="text-3xl" />;
        case "Safari":
            return <FaSafari className="text-3xl" />;
        default:
            return <MdDesktopMac className="text-3xl" />;
    }
};

export default function Index() {
    const { sessions } = usePage().props;

    return (
        <AdminLayout>
            <div className="p-6 bg-gray-50 min-h-screen max-w-[1024px] mx-auto container mt-10 flex flex-col w-auto sm:w-auto content-evenly">
                {/* Back Button */}
                <div className="w-full max-w-md mb-4">
                    <button
                        onClick={() => window.history.back()}
                        className="flex items-center text-gray-600 hover:text-gray-800"
                    >
                        <MdKeyboardBackspace className="text-3xl hover:bg-black hover:bg-opacity-20 rounded-full p-1 transition duration-150 cursor-pointer" />
                        <span className="ml-2 text-xl font-semibold mb-2">Back</span>
                    </button>
                </div>

                {/* Sessions List */}
                {sessions.length > 0 ? (
                    sessions.map((session) => (
                        <div key={session.id} className="bg-white rounded-lg shadow-md p-14 w-full max-w-4xl mb-4">
                            {/* Title */}
                            <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                                {sessionOS(session.os)}
                                <span className="ml-2">{session.os}</span>
                            </h2>
                            <p className="text-base text-gray-600">{session.device}</p>
                            <p className="text-base text-gray-600 mb-4">{session.ip_address}</p>

                            {/* Last Activity Time */}
                            <div className="mb-6">
                                <h3 className="text-xl font-medium text-blue-400">Date and Time</h3>
                                <div className="flex items-center space-x-2 mt-2">
                                    <span className="text-gray-800">{session.formattedDate}</span>
                                </div>
                            </div>

                            {/* Browser Section */}
                            <div className="mb-6">
                                <h3 className="text-xl font-medium text-blue-400">Browser</h3>
                                <div className="flex items-center space-x-2 mt-2">
                                    {sessionBrowser(session.browser)}
                                    <span className="text-gray-800">{session.browser}</span>
                                </div>
                            </div>

                            {/* IP/User Agent Section */}
                            <div>
                                <h3 className="text-xl font-medium text-blue-400">IP / User Agent</h3>
                                <p className="text-base text-gray-800 mt-2">{session.ip_address}</p>
                                <p className="text-base text-gray-600 mt-1">{session.user_agent}</p>
                            </div>
                        </div>
                    ))
                ) : (
                    <p className="text-gray-500">No sessions found.</p>
                )}
            </div>
        </AdminLayout>
    );
}
