<?php

namespace App\Console\Commands\Admin;

use App\Modules\CustomLogger\Services\AuthLogger;

use Exception;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AdminInvitationDeleteExpired extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:admin-invitation-delete-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deletes expired admin invitations from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try
        {
            $deletedCount = DB::table('admin_invitations')
                ->where('valid_until', '<', now())
                ->delete();

            app(AuthLogger::class)->info("Deleted {$deletedCount} expired admin invitation entries.");
        }
        catch(Exception $error)
        {
            app(AuthLogger::class)->error("Error Deleting Expired Admin Invitation Entries {$error->getMessage()}");
        }
    }
}
