//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdMoreVert} from "react-icons/md";
import { FaEye, FaRegThumbsUp, FaRegThumbsDown, FaRegHourglass, FaRegQuestionCircle } from 'react-icons/fa';

//* COMPONENTS
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import setDefaultDateFormat from "../../Util/setDefaultDateFormat";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferItemComponent(
    {
        item,
        isSelected,
        onCheckboxChange,   
        handleClickNote, 
    }
) 
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow]                             = useState(false);


    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, e);
    };

    const onHandleOnClick = (link, method, data) => {
        setShow(false);
        router.visit(link, { method: method, data: data });
    };

    const onHandleDelete = (domainCount, id) => {
        setShow(false);
        if (domainCount > 0) {
            router.post(route("category.warn"), { ids: [id] });
        }
        else {
            router.delete(
                route("category.delete", { ids: [id] }),
                { onSuccess: () => toast.success("Category Has Successfully Been Deleted.") }
            );
        }
    }

    const getStatus = () =>
    {
        let classNamesCommon   = "flex items-center gap-2 uppercase font-semibold"
        let classNamesSpecific = ""
        let icon               = <FaRegHourglass />;
        let status             = 'pending';

        if (item.deleted_at)
        {
            classNamesSpecific = 'text-danger';
            icon               = <FaRegThumbsDown />;
            status             = "rejected";
        }
        else if (item.verified_at)
        {
            classNamesSpecific = 'text-success';
            icon               = <FaRegThumbsUp />;
            status             = "verified";
        }
        else if (item.reviewed_at && !item.deleted_at)
        {
            classNamesSpecific = 'text-orange-500';
            icon               = <FaRegQuestionCircle />;
            status             = "unverified";
        }
        else if (!item.reviewed_at && !item.deleted_at)
        {
            classNamesSpecific = 'text-slate-500';
            icon               = <FaRegHourglass />;
            status             = "pending";
        }

        return (
            <div
                className={`
                    ${classNamesCommon} 
                    ${classNamesSpecific}     
                `}
            >
                <span>
                    {status}
                </span>
                {icon}
            </div>
        );
    };

    const showActionButton = () =>
    {
        let shouldShow = false; 

        if (item.deleted_at)
        {
            shouldShow = false; 
        }
        else if (item.verified_at)
        {
            shouldShow = false; 
        }
        else if (item.reviewed_at && !item.deleted_at)
        {
            shouldShow = true; 
        }
        else if (!item.reviewed_at && !item.deleted_at)
        {
            shouldShow = true;             
        }

        return shouldShow;
    };

    const rowActions = 
    [
        {
            label           : 'verify',
            hasAccess       : hasPermission('billing.wire.transfer.verify-edit'),
            shouldDisplay   : true,
            handleEventClick: () =>
            {
                router.get(route("billing.wire.transfer.verify-edit", { id: [item.id] }));
            } 
        },
    ];

    const permittedActions = rowActions.filter(rowAction => rowAction.hasAccess && rowAction.shouldDisplay);

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <div className="flex items-center pl-2 space-x-2">
                    {/* <Checkbox
                        name="name"
                        value={item.name}
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    /> */}
                    <div title={item.account_name}>
                        <span>{item.account_name}</span>
                    </div>
                </div>
            </td>
            <td>
                <span>{item.company}</span>
            </td>
            <td>
                <span>{parseFloat(item.amount).toFixed(2)}</span>
            </td>
            <td>
                <span
                    className='font-semibold text-primary uppercase'
                >
                    {item.purpose}
                </span>
            </td>
            <td>
                <span>{getStatus()}</span>
            </td>
            <td>
                <div
                    className='
                        flex gap-2 items-center 
                        text-slate-500
                        cursor-pointer hover:text-primary ease-linear duration-100
                    '
                    onClick={handleClickNote}
                >
                    <span>View</span>
                    <FaEye/>
                </div>
            </td>
            <td>
                <span>{setDefaultDateFormat(item.created_at) + ' ' + new Date(item.created_at + 'Z').toLocaleTimeString()}</span>
            </td>
            <td>
                <span>{setDefaultDateFormat(item.updated_at) + ' ' + new Date(item.updated_at + 'Z').toLocaleTimeString()}</span>
            </td>
            <td>
                {showActionButton() && <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        {
                            permittedActions.length == 0
                                ?
                                    <div
                                        className='text-sm font-medium rounded-md p-2 text-danger'
                                    >
                                        No Actions Permitted
                                    </div>
                                :
                                    permittedActions.map(
                                        (rowAction, rowActionIndex) =>
                                        {
                                            return (
                                                <button
                                                    key={rowActionIndex}
                                                    className="hover:bg-gray-100 px-5 py-1 w-full text-left capitalize"
                                                    onClick={rowAction.handleEventClick}
                                                >
                                                    {rowAction.label}
                                                </button>
                                            );
                                        }
                                    )
                        }
                    </DropDownContainer>
                </span>}
            </td>
        </tr>
    );
}