//* PACKAGES
import React, {useState, useEffect} from 'react'
import { <PERSON>, router } from '@inertiajs/react';

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function ActivitiesNav({ activeTab, user_id })
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const items = 
    [
        {
            hasAccess: hasPermission('client.logs.security') || hasPermission('client.logs.security.all'),
            link     : route(user_id ? "client.logs.security" : "client.logs.security.all", user_id ? { user_id } : {}),
            isActive : activeTab == "AccountSecurityLogs",
            label    : 'account security logs'
        }, 
        {
            hasAccess: hasPermission('client.logs.domain') || hasPermission('client.logs.domain.all'),
            link     : route(user_id ? "client.logs.domain" : "client.logs.domain.all", user_id ? { user_id } : {}),
            isActive : activeTab == "DomainLogs",
            label    : 'domain logs'
        }
    ];

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <div
            className="p-2 border-r border-gray-300 pr-4 w-full md:w-1/6 text-sm text-md"
        >

            {
                items.filter(item => item.hasAccess)
                    .map(
                        (item, itemIndex) =>
                        {
                            return (
                                <Link
                                    key={itemIndex}
                                    href={item.link}
                                    className={`
                                        mt-4 block px-4 py-2 hover:bg-gray-100 hover:text-blue-500 rounded text-xs md:text-sm capitalize
                                        ${item.isActive ? "bg-gray-100 text-blue-500" : ""}
                                    `}
                                >
                                    {item.label}
                                </Link>
                            );
                        }
                    )
            } 
        </div>
    );
}
