//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import Modal from '@/Components/Modal'; 

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementPermissionsReviewModalComponent(
    {
        //! PROPS
        selectedPermissions,

        //! STATES 
        stateIsModalOpen,
        
        //! EVENTS
        handleEventModalClose = () => alert('close'),
        handleEventModalConfirm = () => alert('confirm')
    }
)
{
    //! PACKAGE
    //... 

    //! VARIABLES
    //...

    //! STATES
    //...

    //! FUNCTIONS
    function handleSelfClose()
    {
        handleEventModalClose(); 
    }

    function handleConfirm()
    {
        handleEventModalConfirm();     
    }

    return (
        <Modal
            show={stateIsModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='3xl'
        >
            <div
                className={`
                    flex flex-col justify-around
                    px-10 py-5
                    gap-y-2
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex flex-col gap-2 pb-4'
                >
                    <div
                        className='text-lg text-primary font-bold'
                    >
                        Review Permissions | {selectedPermissions.length} Selected
                    </div>
                </section>

                <hr />

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-y-6 py-2 max-h-96 overflow-auto'
                >
                    <aside
                        className='flex gap-4'
                    >
                    {
                        selectedPermissions.length == 0
                            ?
                                'No Permissions Found' 
                            :
                                <div
                                    className='grid grid-cols-2 gap-4 w-full'
                                >
                                    {
                                        selectedPermissions
                                            .map(
                                                (item, index) => 
                                                {
                                                    return (
                                                        <div
                                                            key={index}
                                                            className='font-medium text-sm text-gray-700'
                                                        >
                                                            {item.name}
                                                        </div>
                                                    ); 
                                                }
                                            )
                                    }
                                </div>
                        } 
                    </aside>
                </section>

                <section
                    className='flex justify-end gap-x-5'
                >
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleSelfClose}
                    >
                        Close
                    </AppButtonComponent>
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleConfirm}
                    >
                        Confirm
                    </AppButtonComponent>
                </section>
            </div>
        </Modal>
    );
}
