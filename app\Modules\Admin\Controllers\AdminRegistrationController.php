<?php

namespace App\Modules\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Admin\Requests\AdminRegistrationStoreRequest;
use App\Modules\Admin\Services\AdminRegistrationService;

use Inertia\Inertia;

class AdminRegistrationController extends Controller
{
    public function renderSetupForm(string $token)
    {
        $data = (new AdminRegistrationService())->fetchInvitation($token);

        return Inertia::render(
            'Admin/AdminRegistration/AdminRegistrationSetupForm',
            ['data' => $data]
        );
    }

    public function register(AdminRegistrationStoreRequest $request, string $token)
    {
        (new AdminRegistrationService())->register($request->only('name', 'email', 'password'), $token);

        return redirect()->route('dashboard')
            ->with('successMessage', 'Account Setup Successful');
    }
}
