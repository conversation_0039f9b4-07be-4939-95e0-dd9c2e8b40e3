export const offFilter = (filter) => {
    let { container, field } = filter;

    container.active = false;

    Object.keys(field).map((e) => {
        field[e].active = false;
    });

    return { container: container, field: field };
};

export const updateFieldValue = (value, fieldBody) => {
    let newValue = {};
    let checkValue = [...fieldBody.value];
    let i = checkValue.indexOf(value);

    if (value.length == 0) {
        return { ...fieldBody, value: [] };
    }

    switch (fieldBody.type) {
        case "option":
            newValue = {
                ...fieldBody,
                value: i == -1 ? [value] : [],
            };
            break;
        case "multiOption":
            checkValue =
                i == -1
                    ? [...checkValue, value]
                    : [...checkValue.filter((e) => e != value)];

            newValue = { ...fieldBody, value: checkValue };
            break;
        case "text":
            const textValue = value.length == 0 ? [] : [value];
            newValue = { ...fieldBody, value: textValue };
            break;
        default:
            newValue = { ...fieldBody, value: [] };
            break;
    }

    return newValue;
};
