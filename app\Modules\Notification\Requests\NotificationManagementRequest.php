<?php

namespace App\Modules\Notification\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NotificationManagementRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'status' => 'nullable|string|in:pending,disabled,expired',
            'type' => 'nullable|string|in:Important,Normal',
            'limit' => 'nullable|integer|in:15,25,50,100',
        ];
    }
} 