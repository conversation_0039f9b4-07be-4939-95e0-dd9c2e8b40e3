import React from "react";

export default function RadioButton({
    name,
    value,
    handleChange,
    checked = false,
}) {
    return (
        <input
            type="radio"
            maxLength={100}
            checked={checked}
            name={name}
            value={value}
            className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
            onChange={(e) => handleChange(e)}
        />
    );
}
