<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admins', function (Blueprint $table) 
            {
                $table->boolean('is_super_admin')->default(false); 
                $table->timestamp('last_active')->nullable(); 
                $table->enum('status', ['ACTIVE', 'PENDING', 'INACTIVE', 'DISABLED'])->default('PENDING');
            }
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admins', function (Blueprint $table) {
            $table->dropColumn(['is_super_admin', 'last_active', 'status']);
        });
    }
};
