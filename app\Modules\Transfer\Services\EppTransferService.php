<?php

namespace App\Modules\Transfer\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\RegisteredDomain\Services\DomainRegistrationService;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Modules\Transfer\Constants\TransferTransactionTypes;
use App\Modules\Transfer\Jobs\UpdateTransferFromPoll;
use App\Traits\UserContact;
use App\Util\Helper\DomainParser;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Encryption\DecryptException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use stdClass;

class EppTransferService
{
    use UserLoggerTrait;

    public static function instance()
    {
        $eppDomainService = new self;

        return $eppDomainService;
    }
    public function updateTransferStatusFromPoll($data)
    {
        if (empty($data)) {
            return;
        }

        // foreach ($data as $item) {
        //     $pollId = DB::table('polls')->where('server_id', $item['id'])->value('id');
        //     $status = $item['status'];
        //     $name = strtolower($item['name']);

        //     UpdateTransferFromPoll::dispatch($pollId, $status, $name);
        // }
    }

    public function callEppTransferClientResponse(string $domain, string $action, string $email): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer.'.$action);
        $payload = ['name' => $domain];

        return $this->sendHttpPostRequest($registry, $path, $payload, $email);
    }

    public function callTransferQuery($domain, $email): array
    {
        $registry = DomainParser::getRegistryName($domain);

        if (is_null($registry)) {
            return ['status' => Config::get('transfer.response.error')];
        }

        $path = Config::get('transfer.'.TransferTransactionTypes::QUERY);
        $payload = ['name' => $domain];

        return $this->sendHttpPostRequest($registry, $path, $payload, $email);
    }


    private function sendHttpPostRequest(string $registry, string $path, array $payload, string $email): array
    {
        try {
            $request = Http::transfer($registry)->post($path, $payload);
        } catch (RequestException $e) {
            return $e->response->json();
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected Response');
        }

        return $request->json();
    }

}