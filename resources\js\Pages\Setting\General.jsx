import GeneralItem from "@/Components/Setting/GeneralItem";
import AdminLayout from "@/Layouts/AdminLayout";
export default function General({ settings }) {
    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-10 px-8">
                <div className="flex flex-wrap space-y-2 md:divide-x-2 justify-between">
                    <div className="flex-none md:w-1/2 px-2">
                        <div>
                            <span className="text-lg text-gray-700">
                                Domain
                            </span>
                        </div>
                        <div>
                            <span className="text-gray-600 ">
                                Settings that would affect registration and
                                renewal fees.
                            </span>
                        </div>
                    </div>
                    <div className="flex flex-col space-y-2 md:pl-8 flex-auto">
                        {Object.keys(settings).map((key) => {
                            // if (key.toLowerCase().includes("domain_"))
                            if (key.toLowerCase().includes("domain_") && key !== "domain_vat_percent") // exclude vat percent
                                return (
                                    <GeneralItem
                                        key={"si-" + key}
                                        item={settings[key]}
                                    />
                                );
                            else return false;
                        })}
                    </div>
                </div>

                {/* <div className="flex flex-wrap space-y-2 md:divide-x-2 justify-between">
                    <div className="flex-none  md:w-1/2 px-2">
                        <div>
                            <span className="text-lg text-gray-700">
                                Other Setting
                            </span>
                        </div>
                        <div>
                            <span className="text-gray-600 ">
                                Sample of other settings
                            </span>
                        </div>
                    </div>
                    <div className="flex flex-col space-y-2 md:pl-8 flex-auto">
                        {Object.keys(settings).map((key) => {
                            if (!key.toLowerCase().includes("domain_"))
                                return (
                                    <GeneralItem
                                        key={"si-" + key}
                                        item={settings[key]}
                                    />
                                );
                            else return false;
                        })}
                    </div>
                </div> */}
            </div>
        </AdminLayout>
    );
}
