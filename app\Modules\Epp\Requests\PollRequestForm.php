<?php

namespace App\Modules\Epp\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Epp\Services\PollService;
use App\Util\Constant\RegistryName;
use App\Util\Poll\PollXMLParser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\Rule;

class PollRequestForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the reques.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        // dd($this->all());
        return [
            'registry' => [Rule::in(RegistryName::SUPPORTED)],
        ];
    }

    public function allPoll()
    {
        if (! $this->has('registry')) {
            return [
                'onFirstPage' => 1,
                'onLastPage' => 0,
                'nextPageCursor' => null,
                'previousPageCursor' => null,
                'items' => ['polls' => []],
                'sessionCursor' => null
            ];
        }

        return PollService::get($this->all());
    }

    public function viewById()
    {
        $result['poll'] = [];
        $result['type'] = $this->item['type'];
        $source = 'client';

        if (! $this->id) {
            return [];
        }

        // dd($this->item);
        // if ($this->item['body']) {
        //     $result['poll'] = PollXMLParser::xmlToJson($this->item['body'], $this->item['type']);

        //     return $result;
        // }

        $response = PollService::view($source, $this->id, $this->registry);
        // print_r($response);
        if ($response) $result['poll'] = $response;

        // dd($result);

        return $result;
    }

    public function pop()
    {
        if (! $this->has('registry')) {
            throw new FailedRequestException(400, 'Registry not specified', 'Bad request');
        }

        PollService::pop($this->registry);
    }
}
