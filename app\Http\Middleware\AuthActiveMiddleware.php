<?php

namespace App\Http\Middleware;

use App\Modules\Admin\Constants\AdminStatusConstants; 

use Illuminate\Support\Facades\Auth; 
use Illuminate\Http\Request;

use Closure;

use Symfony\Component\HttpFoundation\Response;

class AuthActiveMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) 
        {
            abort(401);
        }

        if ($user->status != AdminStatusConstants::STATUS_ACTIVE)
        {
            if (Auth::check()) 
            {
                Auth::logout();
            }

            abort(403, 'Your account is disabled. Please contact administrators');
        }

        return $next($request);
    }
}
