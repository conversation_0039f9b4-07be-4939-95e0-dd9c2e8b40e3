//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import useOutsideClick from "@/Util/useOutsideClick";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";

//* ICONS
//...

//* COMPONENTS
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementRoleTableFilterComponent(
    {
        pageRoute = 'user-management.role'
    }
)
{
    //! PACKAGE
    const { orderBy, role } = route().params;
    const refContainer         = useRef();

    //! VARIABLES
    const [stateInput, setStateInput] = useState(role || "");

    const config =
    {
        container:
        {
            active: false,
        },
        field: {
            orderBy:
            {
                active: false,
                value: orderBy ? [orderBy] : [],
                type: "option",
                items:
                [
                    "role:desc",
                    "role:asc",
                    "permissions:desc",
                    "permissions:asc",
                    "createdBy:desc",
                    "createdBy:asc",
                    "lastUpdated:desc",
                    "lastUpdated:asc",  
                ],
                name: "Order By",
            },
            role:
            {
                active: false,
                value: role ? [role] : [],
                type: "text",
                name: "Role",
                tempValue: stateInput,
            },
        },
    };
    //! STATES
    const [filter, setFilter]         = useState(config);
    const { field } = filter;

    //! FUNCTIONS
    useOutsideClick(refContainer, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });



    function handleSubmit(updatedFilter)
    {
        let { orderBy, role } = updatedFilter.field;
        let payload = {};

        if (orderBy.value.length > 0) payload.orderBy = orderBy.value[0];
        if (role.value.length > 0) payload.role = role.value[0];

        router.get(
            route(pageRoute),
            payload, 
            {
                preserveState: false, 
                replace : true, 
            }
        ); 
    };

    function handleDisplayToggle(newObject)
    {
        const closedFilter = offFilter(filter);
        
        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    function handleFieldUpdateValue(key, value)
    {
        if (key == "role")
        {
            setStateInput(value);
            
            if (!value || value === stateInput)
            {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter =
                {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue }
                    },
                };

                setFilter(offFilter(updatedFilter));
                handleSubmit(updatedFilter);
                
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    role: {
                        ...prevFilter.field.role,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        handleSubmit(updatedFilter);
    };

    return (
        <div
            className="flex items-center relative"
            ref={refContainer}
        >
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderBy}
                    fieldKey="orderBy"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <TextFilter
                    fieldProp={field.role}
                    fieldKey="role"
                    placeholder='Search Role'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.role.tempValue || field.role.value[0] || "";
                        handleFieldUpdateValue("role", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
            </div>
        </div>
    );
}
