//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdKeyboardBackspace } from "react-icons/md";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import InputError from "@/Components/InputError";
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index()
{
    //! PACKAGE
    const [form, setForm] = useState({
        email: "",
        disable_verification: false,
        disable_deposit: false,
        default_balance: false,
        balance: 0.1,
    });        

    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [hasSpinner, setSpinner] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const submitForm = (e) => {
        e.preventDefault();
        setSpinner(true);

        router.post(route("client.invite.send"), form, {
            onError: (errors) => {
                // setSpinner(false);

                // if (errors.email) {
                //     toast.error(errors.email); // Show toast notification
                //     setForm((prevForm) => ({
                //         ...prevForm,
                //         errors, // Store errors in form state
                //     }));
                // }
                setSpinner(false);
                setForm((prevForm) => ({
                    ...prevForm,
                    errors,
                }));

                Object.values(errors).forEach((messageArray) => {
                    // Laravel sends errors as arrays
                    if (Array.isArray(messageArray)) {
                        messageArray.forEach((msg) => toast.error(msg));
                    } else {
                        toast.error(messageArray);
                    }
                });
            },
            onSuccess: () => {
                setSpinner(false);
                toast.success("Invite Sent Successfully.");
                setForm({
                    email: "",
                    disable_verification: false,
                    disable_deposit: false,
                    default_balance: false,
                    balance: "",
                    errors: {}, // Clear errors after success
                });
                router.visit(route("client"));
            },
        });
    };

    const BackButton = () => (
        <button
            onClick={() => window.history.back()}
            className="flex items-center text-gray-600 hover:text-gray-800"
        >
            <MdKeyboardBackspace className="text-3xl hover:bg-black hover:bg-opacity-20 rounded-full p-1 transition duration-150 cursor-pointer" />
            <span className="ml-2 text-xl font-semibold mb-2">Back</span>
        </button>
    );

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });
    const [value, setValue] = useState("");

    const handleBalanceChange = (e) => {
        let input = e.target.value.replace(/[^0-9.]/g, "");

        const parts = input.split(".");
        if (parts.length > 2) return;

        const [integerPart, decimalPart] = parts;
        const formattedInteger = Number(integerPart || 0).toLocaleString(
            "en-US"
        );

        const formattedValue =
            decimalPart !== undefined
                ? `$${formattedInteger}.${decimalPart.slice(0, 2)}`
                : `$${formattedInteger}`;

        setValue(formattedValue);

        const numericValue = parseFloat(input) || 0;
        setForm((prev) => ({
            ...prev,
            balance: numericValue,
        }));
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div className="w-3/5 mx-auto bg-white rounded-lg">
                    <BackButton />
                </div>
                <form
                    onSubmit={submitForm}
                    className="w-3/5 mx-auto bg-white p-2 rounded-lg shadow-md"
                >
                    <h2 className="text-lg font-semibold mb-4">Invite User</h2>

                    <label
                        for="email"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Email
                    </label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="Email Address"
                        onChange={(e) =>
                            setForm({ ...form, email: e.target.value })
                        }
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4"
                    />
                    {form.errors?.email && (
                        <InputError
                            message={form.errors.email}
                            className="-mt-3 text-red-600 mb-4"
                        />
                    )}
                    <div className="mb-4">
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                name="disable_verification"
                                checked={form.disable_verification}
                                onChange={(e) => {
                                    const checked = e.target.checked;
                                    setForm({
                                        ...form,
                                        disable_verification: checked,
                                    });
                                    if (checked) toast.success("Identity verification disabled.");
                                }}
                                className="text-blue-600 border-gray-300 rounded"
                            />
                            <span className="text-gray-700">
                                Disable identity verification
                            </span>
                        </label>
                    </div>
                    <div className="mb-4">
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                name="disable_deposit"
                                checked={form.disable_deposit}
                                onChange={(e) => {
                                    const checked = e.target.checked;
                                    setForm({
                                        ...form,
                                        disable_deposit: checked,
                                    });
                                    if (checked) toast.success("Initial deposit disabled.");
                                }}
                                className="text-blue-600 border-gray-300 rounded"
                            />
                            <span className="text-gray-700">
                                Disable initial deposit
                            </span>
                        </label>
                    </div>
                    <div className="mb-4">
                        <label className="flex items-center space-x-2">
                            <input
                                type="checkbox"
                                name="default_balance"
                                checked={form.default_balance}
                                onChange={(e) => {
                                    const checked = e.target.checked;
                                    setForm({
                                        ...form,
                                        default_balance: checked,
                                        balance: checked ? 0 : 0.1, // 👉 Set to 0.1 when unchecked
                                    });
                                    if (checked) toast.success("Default balance enabled.");
                                }}
                                className="text-blue-600 border-gray-300 rounded"
                            />
                            <span className="text-gray-700">
                                Add default account balance
                            </span>
                        </label>
                        {form.default_balance && (
                            <input
                                type="text"
                                name="balance"
                                value={value}
                                placeholder="$0.00"
                                onChange={handleBalanceChange}
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mt-2"
                            />
                        )}
                        {form.errors?.balance && (
                            <InputError
                                message={form.errors.balance}
                                className="mt-1 text-red-600 mb-4"
                            />
                        )}
                    </div>
                    
                    {
                        hasPermission('client.invite.send')
                            ? 
                                <button
                                    type="submit"
                                    disabled={!form.email.trim() || hasSpinner}
                                    className={`w-full px-4 py-2 border border-transparent rounded-md font-semibold text-sm text-white tracking-widest 
                                        ${!form.email.trim() || hasSpinner ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary hover:bg-gray-700'} 
                                        focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 
                                        transition ease-in-out duration-150 flex justify-center`}
                                >
                                    {hasSpinner ? <LoaderSpinner /> : 'Send Invite'}
                                </button>
                            : 
                                null
                    }
                </form>
            </div>
        </AdminLayout>
    );
}
