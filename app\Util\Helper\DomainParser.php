<?php

namespace App\Util\Helper;

use App\Util\Constant\RegistryExtension;
use App\Util\Constant\RegistryName;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainParser
{
    /***
     * Parse String with comma or space
     *
     * @param String $input String to parse
     * @return Array of parsed strings
     */
    public static function getList(string $input): array
    {
        $input = trim($input);
        $delimiter = str_contains($input, ',') ? ',' : ' ';
        // $result_array = explode($delimiter, $input);
        $result_array = preg_split("/\s*[,\s\n]\s*/", $input);

        return $result_array;
    }

    /***
     * Parse String to get name and extension
     *
     * @param String $input String to parse
     * @return Array of name and extension
     */
    public static function getNameAndExtension(string $input): array
    {
        $delimiter = '.';
        $extension = '';
        $name = trim($input);

        if (str_contains($input, '.')) {
            $result = explode($delimiter, $input);
            $extension = array_pop($result);
            $name = implode($delimiter, $result);
        }

        return [
            'name' => $name,
            'extension' => $extension,
        ];
    }

    /***
     * Parse String to get date
     *
     * @param String $input String to parse: "2025-06-27T09:28:10.000+00:00"
     * @return expiry value: 2006477541000
     */
    public static function getDomainExpiry(string $input): mixed
    {
        $date = [];
        $time = [];

        // parse date by T
        if (str_contains($input, 'T')) {
            $date = explode('T', $input, 2);
            $input = end($date);
            if (str_contains($input, '.')) {
                $time = explode('.', $input, 2);
                $complete = $date[0] . ' ' . $time[0];

                return Carbon::parse($complete)->valueOf();
            }
        }

        return 0;
    }

    /***
     * Parse String to get registry name
     *
     * @param String $input String to parse
     * @return String for registry name
     */
    public static function getRegistryName(string $domain): ?string
    {
        $extension = pathinfo($domain, PATHINFO_EXTENSION);

        switch (strtolower($extension)) {
            case RegistryExtension::ORG:
                return RegistryName::PIR;
            case RegistryExtension::COM:
            case RegistryExtension::NET:
                return RegistryName::VERISIGN;
            default:
                return null;
        }
    }

    public static function getRegistryId(string $input): int
    {
        return DB::client()->table('domains')->where('name', strtolower($input))
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('tlds', 'tlds.extension_id', '=', 'registered_domains.extension_id')
            ->value('tlds.registry_id');
    }

    public static function validateDomain(string $input): array
    {
        $domain = self::getNameAndExtension($input);
        $payload = [
            'domain' => $domain,
            'errors' => [],
            'is_valid' => true,
        ];

        if (empty($domain['name']) || empty($domain['extension'])) {
            $payload['errors'][] = 'There is something wrong with your syntax.';
            $payload['is_valid'] = false;

            return $payload;
        }

        if (! in_array($domain['extension'], RegistryExtension::SUPPORTED)) {
            $payload['errors'][] = 'Extension not supported.';
            $payload['is_valid'] = false;

            return $payload;
        }

        return $payload;
    }

    /**
     * @return ['registry1' => [], 'registry2' => []]
     */
    public static function createRegistryArray(): array
    {
        $result = [];

        foreach (RegistryName::SUPPORTED as $registry) {
            $result[$registry] = [];
        }

        return $result;
    }

    public static function validateDomainNs($name)
    {
        $url = parse_url($name);
        $nameserver = array_key_exists('host', $url) ? $url['host'] : $url['path'];

        if (empty($nameserver)) {
            return $name;
        }

        $domain = preg_replace("/^([a-zA-Z0-9].*\.)?([a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z.]{2,})$/", '$2', $nameserver);

        return $domain;
    }
}
