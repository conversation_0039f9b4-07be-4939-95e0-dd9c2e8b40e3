//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import Modal from '@/Components/Modal'; 
import InputLabel from '../InputLabel';
import TextArea from '../TextArea';

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferModalNoteComponent(
    {
        //! PROPS
        stateSelectedItem,

        //! STATES 
        stateIsModalOpen,
        
        //! EVENTS
        handleEventModalClose = () => alert('close'),
        handleEventModalConfirm = () => alert('confirm')
    }
)
{
    //! PACKAGE
    //... 

    //! VARIABLES
    //...

    //! STATES
    //...

    //! FUNCTIONS
    function handleSelfClose()
    {
        handleEventModalClose(); 
    }

    function handleConfirm()
    {
        handleEventModalConfirm();     
    }

    return (
        <Modal
            show={stateIsModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='xl'
        >
            <div
                className={`
                    flex flex-col justify-around
                    px-10 py-5
                    gap-y-2
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex flex-col gap-2 pb-4'
                >
                    <div
                        className='text-lg text-primary font-bold'
                    >
                        {
                            stateSelectedItem == null 
                                ?
                                    null 
                                :
                                    <span>
                                        {stateSelectedItem.account_name} | {stateSelectedItem.company}
                                    </span>
                        }
                    </div>
                </section>

                <hr />

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-y-6 py-2 max-h-96 overflow-auto'
                >
                    <aside
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="note"
                            value="Note:"
                        />
                        <TextArea
                            value={stateSelectedItem == null ? '' : stateSelectedItem.note ?? ''}
                            maxLength={500}
                            rows={5}
                            name="note"
                            placeholder="Add notes here"
                            className="w-full border rounded px-3 py-2"
                            autoComplete="note"
                            isDisabled={true}
                            readOnly={true}
                            handleChange={(e) => {}}
                        />
                    </aside>
                </section>

                <section
                    className='flex justify-end gap-x-5'
                >
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleSelfClose}
                    >
                        Close
                    </AppButtonComponent>
                    {/* <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleConfirm}
                    >
                        Confirm
                    </AppButtonComponent> */}
                </section>
            </div>
        </Modal>
    );
}
