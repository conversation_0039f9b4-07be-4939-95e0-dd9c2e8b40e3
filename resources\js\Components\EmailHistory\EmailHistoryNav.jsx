import React from 'react';
import NavLink from "@/Components/NavLink";

const EmailHistoryNav = ({ postRouteName }) => {
    return (
        <NavLink
            href={route("email.history")}
            active={route().current("email.history")}
        >
            <span className="flex space-x-4">
                <span className="text-inherit">System Email History</span>
            </span>
        </NavLink>
    );
};

export default EmailHistoryNav;
