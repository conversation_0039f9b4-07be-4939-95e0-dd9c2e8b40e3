<?php

namespace App\Modules\MarketPlace\Constants;

final class AfternicOfferConstants
{
    public const WAITING = 'waiting';
    public const COUNTER = 'counter_offer';
    public const ACCEPTED = 'offer_accepted';
    public const REJECTED = 'offer_rejected';
    public const CLOSED = 'offer_closed';
    public const PAID = 'paid';

    public const All = [
        self::WAITING,
        self::COUNTER,
        self::ACCEPTED,
        self::REJECTED,
        self::CLOSED
    ];
}
