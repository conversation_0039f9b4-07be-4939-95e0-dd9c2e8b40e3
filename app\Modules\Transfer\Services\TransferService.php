<?php

namespace App\Modules\Transfer\Services;

use Illuminate\Support\Facades\DB;

class TransferService
{
    public static function instance(): self
    {
        return new self;
    }

    /**
     * Base query for transfer domains.
     */
    protected function baseQuery()
    {
        return DB::table('transfer_domains')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->select([
                'transfer_domains.id',
                'transfer_domains.registered_domain_id',
                'transfer_domains.error_code',
                'transfer_domains.error_message',
                'transfer_domains.deleted_at',
                'transfer_domains.created_at',
                'transfer_domains.updated_at',
                'domains.name as domain',
                'domains.id as domain_id',
                'users.email as user_email',
                'users.first_name',
                'users.last_name',
            ])
            ->whereNull('transfer_domains.deleted_at');
    }

    /**
     * Get transfer domains with optional filters.
     */
    public function get(array $filters = []): array
    {
        $query = $this->baseQuery();

        if (!empty($filters['email'])) {
            $query->where('users.email', 'like', '%' . $filters['email'] . '%');
        }

        if (!empty($filters['orderby'])) {
            $orderParts = explode(':', $filters['orderby']);
            $column = $orderParts[0];
            $direction = $orderParts[1] ?? 'desc';

            switch ($column) {
                case 'domain':
                    $query->orderBy('domains.name', $direction);
                    break;
                case 'date_created':
                    $query->orderBy('transfer_domains.created_at', $direction);
                    break;
                default:
                    $query->orderBy('transfer_domains.created_at', 'desc');
            }
        } else {
            $query->orderBy('transfer_domains.created_at', 'desc');
        }

        return $query->get()->toArray();
    }
}
