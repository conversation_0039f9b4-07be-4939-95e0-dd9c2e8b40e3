//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import { BsThreeDotsVertical } from 'react-icons/bs';

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppTableRowDropdownActionsComponent(
    {
        //! PROPS
        row,
        rowActions
        
        //! STATES 
        //...
        
        //! EVENTS
        //..,
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    const permittedActions = rowActions.filter(action => action.hasAccess == true && action.shouldDisplay(row) == true); 

    return (
        <div
            className='relative group'
        >
            <button>
                <BsThreeDotsVertical />
            </button>
            <div
                className="flex-col gap-2 absolute invisible group-focus-within:visible right-0 z-50 p-3 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg focus:outline-none"
            >
                {
                    permittedActions.length == 0 
                        ?
                            <div
                                className='text-sm font-medium rounded-md p-2 text-danger'
                            >
                                No Actions Permitted
                            </div>
                        :
                            permittedActions
                                .map(
                                    (action, actionIndex) =>
                                    {
                                        return (
                                            <button
                                                key={actionIndex}
                                                className="flex gap-2 items-center hover:bg-gray-50 hover:text-primary rounded-md p-2 w-full capitalize"
                                                onClick={() => action.handleEventClick(row)}
                                            >
                                                {action.icon}
                                                <span
                                                    className="font-medium text-sm"
                                                >
                                                    {action.label}
                                                </span>
                                            </button> 
                                        )
                                    }
                                )
                                
                }
            </div>
        </div>
    );
}
