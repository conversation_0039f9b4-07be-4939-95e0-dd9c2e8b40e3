import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import { useRef } from "react";
import useOutsideClick from "@/Util/useOutsideClick";

export default function TextFilter({
    fieldKey,
    fieldProp,
    handleFieldUpdateValue,
    offFilter,
    placeholder = "Search email",
}) {
    const { active, value, tempValue } = fieldProp;
    const containerRef = useRef();
    const inputRef = useRef();

    const handleSubmit = () => {
        if (active) {
            offFilter();
        }
    };

    useOutsideClick(containerRef, (event) => {
        if (active && event?.target && !inputRef.current?.contains(event.target)) {
            handleSubmit();
        }
    });
    
    return (
        <DropDownContainer show={active} className="left-full top-0 z-20">
            <div
                key={"filter-search-search"}
                className="flex items-center space-x-2 cursor-pointer px-2 "
                onClick={(e) => { }}
                ref={containerRef}
            >
                <input
                    ref={inputRef}
                    type="text"
                    name="search"
                    placeholder={placeholder}
                    className="mt-1 block w-full border-gray-300 focus:border-gray-200 focus:ring-gray-200 rounded-md shadow-sm placeholder-gray-200"
                    autoComplete={fieldKey}
                    value={tempValue || value[0] || ""}
                    onChange={(e) =>
                        handleFieldUpdateValue(fieldKey, e.target.value)
                    }
                    onKeyDown={(e) => {
                        if (e.code === "Enter") {
                            e.preventDefault();
                            handleSubmit();
                        }
                    }}
                    onBlur={handleSubmit}
                />
            </div>
        </DropDownContainer>
    );
}
