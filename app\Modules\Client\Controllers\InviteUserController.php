<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Requests\ClientResendInviteRequest;
use App\Modules\Client\Requests\InviteUserRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class InviteUserController extends Controller
{
    public function index()
    {
        return Inertia::render('Client/UserInvites');
    }

    public function sendUserInvitation(InviteUserRequest $request)
    {
        if ($request->handleInvite($request->all())) {
            return redirect()->route('client');
        }
    
        $request->failed();
    }

    public function resendUserInvitation( ClientResendInviteRequest $request)
    {
        $request->resendInvitation();
        return redirect()->route('client');
    }
}
