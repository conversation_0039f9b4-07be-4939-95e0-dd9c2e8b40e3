function AddAccountCredit({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div
            className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500 text-sm"
            style={{
                fontFamily:
                    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
            }}
        >
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">{data.greeting}</p>
                <p className="mt-4">
                    We are pleased to inform you that your recent fund deposit
                    has been successfully added to your account. Below are the
                    transaction details:
                </p>
                <ul className="list-disc list-inside mt-4 pl-4">
                    <li>
                        <strong>Date: </strong> {data.date}
                    </li>
                    <li>
                        <strong>Payment Method: </strong> {data.payment_method}
                    </li>
                    <li>
                        <strong>Transaction ID: </strong> {data.transaction_id}
                    </li>
                    <li>
                        <strong>Amount Added:</strong> ${data.amount}
                    </li>
                    <li>
                        <strong>Running Balance:</strong> ${data.balance}
                    </li>
                </ul>
                <p className="mt-4">
                    You can view this transaction in your account dashboard by
                    visiting{" "}
                    <a href={data.redirect_url} className="text-blue-600 underline italic">
                        this link
                    </a>
                </p>
                <p className="mt-4">
                    If you have any questions or need assistance, feel free to
                    contact us at{" "}
                    <a href={data.support_url} className="text-blue-600 underline italic">
                        StrangeDomains.com{" "}
                    </a>
                    . Or call us at{" "}
                    <strong className="text-gray-600 italic">
                        {data.phone}
                    </strong>
                </p>
                <div className="mt-4">
                  <p className="font-bold">Thank you for choosing Strange Domains.</p>
                  <br />
                  <p className="font-bold">Sincerely,</p>
                  <br />
                  <p className="font-bold">Strange Domains</p>
                </div>
            </div>
        </div>
    );
}

export default AddAccountCredit;
