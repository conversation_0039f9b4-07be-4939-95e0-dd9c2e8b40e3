<?php

namespace App\Helpers;

class SecurityHelper
{
    public static function encryptData($data, $secretKey)
    {
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt(json_encode($data), 'AES-256-CBC', $secretKey, 0, $iv);

        return rtrim(strtr(base64_encode($iv . $encrypted), '+/', '-_'), '=');
    }

    public static function decryptData($encryptedData, $secretKey)
    {
        $decoded = base64_decode($encryptedData);
        $iv = substr($decoded, 0, 16);
        $encrypted = substr($decoded, 16);
        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $secretKey, 0, $iv);
        return json_decode($decrypted, true);
    }
}
