//* PACKAGES 
import React, {useState, useEffect} from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from 'react-toastify';

//* ICONS
import { MdEdit, MdClose, MdLink } from 'react-icons/md';
import { FiTrash } from 'react-icons/fi';

//* LAYOUTS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function PartialUserManagementSettingsSectionPermissions(
    {
        //! PROPS
        //...
        
        //! STATES 
        //...
        
        //! EVENTS
        //..,
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! STATES
    const [stateIsActiveModalVerification, setStateIsActiveModalVerification] = useState(false);
    const [stateVerificationAction, setStateVerificationAction]                 = useState(null);

    //! VARIABLES
    const actions = 
    [
        {
            label        : 'sync permissions',
            description  : 'Sync Permissions based on system route names',
            buttonLabel  : 'Sync Now',
            hasAccess    : hasPermission('user-management.settings.sync-permissions'),
            onClickEvent : () => 
            {
                setStateVerificationAction('sync');
                setStateIsActiveModalVerification(true);
            }
        },    
    ]

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleSyncPermissions()
    {
        router.post(
            route('user-management.settings.sync-permissions'),
            {}, 
            {
                onSuccess: () => 
                {
                    toast.success('Permissions Synced');
                }, 
                onFailure: () => 
                {
                    toast.error('Something went wrong');
                }
            }
        ); 
    }

    function handleActionSubmission()
    {
        switch (stateVerificationAction)
        {
            case 'sync': 
                handleSyncPermissions()
                break; 
        }
    }
    
    if (actions.filter(action => action.hasAccess).length == 0)
    {
        return (
            <div
                className='font-semibold capitalize text-danger'
            >
                no actions permitted
            </div>
        )
    }

    return (
        <>
            <AppPromptPasswordVerificationComponent
                show={stateIsActiveModalVerification}
                onSubmitSuccess={handleActionSubmission}
                onClose={() => 
                    {
                        setStateVerificationAction(null);
                        setStateIsActiveModalVerification(false);
                    }
                }
            />
            {
                actions.filter(action => action.hasAccess)
                    .map(
                        (action, index) => 
                        {
                            return (
                                <div
                                    key={index}
                                    className='flex justify-between'
                                >
                                    <div
                                        className='flex flex-col gap-y-4'
                                    >
                                        <div
                                            className='font-semibold capitalize'
                                        >
                                            {action.label}
                                        </div>
                                        <div
                                            className='text-gray-500'
                                        >
                                            {action.description}
                                        </div>
                                    </div>
                                    <div>
                                        <AppButtonComponent
                                            isDisabled={false}
                                            handleEventClick={action.onClickEvent}
                                        >
                                            {action.buttonLabel}
                                        </AppButtonComponent>
                                    </div>
                                </div> 
                            )
                        }
                    )
            }
        </>       
    );
}
