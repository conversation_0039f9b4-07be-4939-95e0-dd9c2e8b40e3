//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { FiMoreVertical } from "react-icons/fi";
import { TbTrash } from 'react-icons/tb';
import { CiUnlock, CiLock } from 'react-icons/ci';

//* COMPONENTS
import Checkbox from "@/Components/Checkbox";
import DeleteConfirmationModal from "@/Components/Notification/DeleteConfirmationModal";
import DropDownContainer from "@/Components/DropDownContainer";
import AppTableRowDropdownActionsComponent from '@/Components/App/AppTableRowDropdownActionsComponent';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NotificationItem(
    {
        notification,
        onSelect,
        isSelected,
        onStatusUpdate
    }
)
{
    //! PACKAGE
    const menuRef = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const {
        id            = '',
        title         = '',
        status        = '',
        users         = { count: 0, list: [], is_all_users: false },
        start_date    = '',
        type          = '',
        schedule_type = ''
    } = notification || {};

    //! STATES
    const [showMenu, setShowMenu] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(menuRef, () => setShowMenu(false));

    const getStatusStyle = (status) => {
        switch (status.toLowerCase()) {
            case 'pending':
                return 'bg-cyan-500'; 
            case 'active':
                return 'bg-green-500'; // Green
            case 'disabled':
                return 'bg-gray-500'; // Gray
            case 'expired':
                return 'bg-red-500'; // Red
            default:
                return 'bg-gray-400';
        }
    };

    // Format the date to match MM/DD/YYYY format
    const formatDate = (dateString) => {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
            });
        } catch (e) {
            return dateString;
        }
    };

    const rowActions = 
    [
        {
            label           : 'enable',
            hasAccess       : hasPermission('notification.management.update-status'),
            shouldDisplay   : status !== 'expired',
            handleEventClick: () =>
            {
                setShowMenu(false);
                onStatusUpdate(id, 'pending');
            } 
        },
        {
            label           : 'disable',
            hasAccess       : hasPermission('notification.management.update-status'),
            shouldDisplay   : status !== 'expired',
            handleEventClick: () =>
            {
                setShowMenu(false);
                onStatusUpdate(id, 'disabled');
            } 
        }, 
        {
            label           : 'delete',
            hasAccess       : hasPermission('notification.management.delete'),
            shouldDisplay   : true,
            handleEventClick: () =>
            {
                setShowMenu(false);
                setShowDeleteModal(true);
            } 
        }, 
    ];
    
    const permittedActions = rowActions.filter(rowAction => rowAction.hasAccess && rowAction.shouldDisplay);

    return (
        <tr className="bg-white hover:bg-gray-50">
            <td className=" whitespace-nowrap">
                <Checkbox 
                    checked={isSelected}
                    handleChange={(e) => onSelect(id, e.target.checked)}
                    className="ml-0 mr-2"
                />
                <span>{title}</span>
            </td>
            <td className="">
                <span className={`px-4 py-1 text-xs rounded-full text-white inline-block ${getStatusStyle(status)}`}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                </span>
            </td>
            <td className=" text-blue-600 hover:underline">
                <Link href={route('notification.management.users', id)}>
                    {users?.count || 0}
                </Link>
            </td>
            <td>
                {formatDate(start_date)}
            </td>
            <td>
                {type}
            </td>
            <td>
                {schedule_type.charAt(0).toUpperCase() + schedule_type.slice(1)}
            </td>
            <td
                className='font-normal text-sm'
            >
                <span ref={menuRef} className="relative inline-block">
                    <button
                        className="p-1 hover:bg-gray-100 rounded"
                        onClick={() => setShowMenu(!showMenu)}
                    >
                        <FiMoreVertical className="w-5 h-5" />
                    </button>
                    <DropDownContainer 
                        show={showMenu}
                    >
                        {
                            permittedActions.length == 0
                                ?
                                    <div
                                        className='text-sm font-medium rounded-md p-2 text-danger'
                                    >
                                        No Actions Permitted
                                    </div>
                                :
                                    permittedActions.map(
                                        (rowAction, rowActionIndex) =>
                                        {
                                            return (
                                                <button
                                                    key={rowActionIndex}
                                                    className="hover:bg-gray-100 px-5 py-1 w-full text-left capitalize"
                                                    onClick={rowAction.handleEventClick}
                                                >
                                                    {rowAction.label}
                                                </button>
                                            );
                                        }
                                    )
                        }
                    </DropDownContainer>
                </span>
            </td>

            <DeleteConfirmationModal 
                show={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onConfirm={() => {
                    setShowDeleteModal(false);
                    router.post(route('notification.management.delete'), {
                        ids: [id]
                    }, {
                        preserveScroll: true,
                        onSuccess: () => toast.success('Notification Deleted Successfully'),
                        onError: () => toast.error('Failed to Delete Notification')
                    });
                }}
                itemCount={1}
            />
        </tr>
    );
}
