<?php

namespace App\Modules\Job\Requests;

use App\Modules\Job\Constants\ConnectionSource;
use App\Modules\Job\Constants\JobConnection;
use App\Modules\Job\Resources\JobCollection;
use App\Modules\Job\Services\ShowJobService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class ShowJobForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'source' => [Rule::in(ConnectionSource::all())],
        ];
    }

    public function getData()
    {
        if (!$this->has('source')) return ['jobs' => []];

        return ShowJobService::getdata($this->source);
    }
}
