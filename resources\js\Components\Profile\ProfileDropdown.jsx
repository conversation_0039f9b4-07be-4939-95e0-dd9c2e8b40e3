import React from 'react'
import { Link, router, usePage } from "@inertiajs/react";
import Dropdown from "@/Components/Dropdown";
import {
    MdOutlineAccountCircle,
} from "react-icons/md";

export const ProfileDropdown = () => {
    const user = usePage().props.auth.user;

    const handleLogout = (e) => {
        e.preventDefault(); // Prevent the default behavior of the logout link

        console.log("Clearing session storage..."); // For debugging
        sessionStorage.clear(); // Clear session storage

        // Use Inertia's POST method to trigger the logout process
        //router.post(route("category.warn"), {})
        router.post(route("logout", {}), {
            onSuccess: () => toast.success("Logout successfully."),
        });
    };

    return (
        <div className="hidden sm:flex sm:items-center sm:ml-6">
            <div className="ml-3 relative">
                <Dropdown>
                    <Dropdown.Trigger>
                        <Link
                            as="button"
                            title="Profile"
                            onClick={(e) => (e.preventDefault())}
                        >
                            <div className='flex items-center cursor-pointer hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150' >
                                <MdOutlineAccountCircle className='rounded-full text-3xl p-1' />
                                <span className="text-white text-sm ">{user.name}</span>
                            </div>

                        </Link>
                    </Dropdown.Trigger>

                    <Dropdown.Content width='w-48'>
                        <Dropdown.Link
                            href={route("profile.edit")}
                            method="get"
                            as="button"
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            Profile
                        </Dropdown.Link>
                        {/* <Dropdown.Link
                            href={route("logout")}
                            method="post"
                            as="button"
                            onClick={handleLogout}
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            Log Out
                        </Dropdown.Link> */}
                        <Link
                            onClick={handleLogout}
                            className="block w-full px-4 py-2 text-left text-sm leading-5 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out text-gray-700"
                        >
                            Log Out
                        </Link>
                    </Dropdown.Content>
                </Dropdown>
            </div>
        </div>
    )
}
