import { useRef, useState } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import Checkbox from "@/Components/Checkbox";
import { MdMoreVert } from "react-icons/md";
import { getEventValue } from "@/Util/TargetInputEvent";
import { router } from "@inertiajs/react";
import clipboardCopy from "clipboard-copy";
import "react-toastify/dist/ReactToastify.css";
import { toast } from "react-toastify";
import ShowMoreLess from "../ShowMoreLess";
import getRecentTime from "@/Util/getRecentTime";
import convertToDatetime from "@/Util/convertToDatetime";

export default function Item({ item, isSelected, onCheckboxChange, dateTimeFormat }) {
    const [show, setShow] = useState(false);
    const ref = useRef();
    const responseStatus = { accept: "ACCEPT", denied: "DENIED" };

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, item.status, getEventValue(e));
    };

    const handleResponse = (status) => {
        toast.info((status === 'ACCEPT' ? "Accepting" : "Declining") + " Request");
        setShow(false);
        router.patch(route("request.response"), {
            ids: [item.id],
            status: status,
        });
    };

    const handleDeleteResponse = () => {
        toast.info("Deleting Request");
        setShow(false);
        router.delete(route("request.delete"), {
            data: [item.id],
        });
    };

    const handleCopyUrl = () => {
        setShow(false);
        clipboardCopy(item.url)
            .then(() => {
                toast.success("URL Copied to Clipboard!");
            })
            .catch((error) => {
                toast.error("Failed to Copy URL. Please Try Again.");
                console.error('Failed To Copy URL: ', error);
            });
    };

    const allCapsToCapitalization = (string) => {
        if(string === 'ACCEPT') return 'Accepted'
        if(string === 'DENIED') return 'Declined'
        return string.charAt(0) + string.slice(1).toLowerCase();
    };
    
    const formatDateTime = (format, item) => {
        switch(format){
            case 1:
                return new Date(item + 'Z').toDateString();
            case 2:
                return getRecentTime(item, true);
            case 3:
                return convertToDatetime(item, true);
            case 4:
                return item + ' UTC';
            default:
                return new Date(item + 'Z').toDateString();
        };
    }

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    {item.deleted_at === null && (
                        <Checkbox
                            name="request"
                            value="request"
                            checked={isSelected}
                            handleChange={handleCheckboxChange}
                        />
                    )}
                    <span className=" font-semibold cursor-pointer">
                        {item.email}
                    </span>
                </label>
            </td>
            <td>
                <ShowMoreLess
                    condition={item.message.length > 15}
                    item={item.message}
                />
            </td>
            <td>
                <span>{item.ip}</span>
            </td>
            <td>
                <span className=" lowercase">{item.type}</span>
            </td>
            <td>
                <div>
                    <span>{allCapsToCapitalization(item.status)}</span>
                </div>
            </td>
            <td>
                {item.deleted_at === null ? (
                    <span>{formatDateTime(dateTimeFormat, item.created_at)}</span>
                ) : (
                    <span>{formatDateTime(dateTimeFormat, item.deleted_at)}</span>
                )}
            </td>

            <td>
                {item.deleted_at === null && (
                    <span ref={ref} className="relative">
                        <button
                            className="flex items-center"
                            onClick={() => setShow(!show)}
                        >
                            <MdMoreVert className="text-2xl rounded-full hover:bg-gray-200" />
                        </button>
                        <DropDownContainer show={show}>
                            {item.status.localeCompare("PENDING") == 0 && (
                                <>
                                    <button
                                        className="hover:bg-gray-100 px-5 py-1 "
                                        onClick={() =>
                                            handleResponse(
                                                responseStatus.accept
                                            )
                                        }
                                    >
                                        Accept
                                    </button>
                                    <button
                                        className="hover:bg-gray-100 px-5 py-1 "
                                        onClick={() =>
                                            handleResponse(
                                                responseStatus.denied
                                            )
                                        }
                                    >
                                        Decline
                                    </button>
                                </>
                            )}
                            {(item.status.localeCompare("ACCEPT") == 0 && item.type.localeCompare("REGISTRATION_ACCESS") == 0) && item.user_email === null && (
                                <button
                                    className="hover:bg-gray-100 px-5 py-1 "
                                    onClick={handleCopyUrl}
                                >
                                    Get Link
                                </button>
                            )}
                            <button
                                className="hover:bg-gray-100 px-5 py-1 "
                                onClick={handleDeleteResponse}
                            >
                                Delete
                            </button>
                        </DropDownContainer>
                    </span>
                )}
            </td>
        </tr>
    );
}
