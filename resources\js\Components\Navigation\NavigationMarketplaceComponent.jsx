//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    MdExpandMore,
    MdExpandLess,
    MdOutlineCorporateFare,
} from "react-icons/md";
import { FaBalanceScaleLeft } from "react-icons/fa";
import { BiTransfer } from "react-icons/bi";
import { AiOutlineAudit } from "react-icons/ai";
import { CiGlobe } from "react-icons/ci";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationMarketplaceComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        domains      : route().current("domains") || currentRoute.includes('domains'),
        commissions  : route().current("commissions") || currentRoute.includes('commissions'),
        audits       : route().current("audits") || currentRoute.includes('audits'),
        marketManuals: route().current("market_manuals") || currentRoute.includes('market_manuals'),
        offers       : route().current("offers") || currentRoute.includes('offers'),                 };

    const links = 
    [
        {
            routeName: 'domains',
            hasAccess: hasPermission('domains'),
            isActive : routes.domains,
            icon     : <CiGlobe className="text-2xl"/>,
            label    : 'domains'
        }, 
        {
            routeName: 'commissions',
            hasAccess: hasPermission('commissions'),
            isActive : routes.commissions,
            icon     : <MdOutlineCorporateFare className="text-2xl"/>,
            label    : 'commissions'
        }, 
        {
            routeName: 'audits',
            hasAccess: hasPermission('audits'),
            isActive : routes.audits,
            icon     : <AiOutlineAudit className="text-2xl"/>,
            label    : 'audits'
        }, 
        {
            routeName: 'market_manuals',
            hasAccess: hasPermission('market_manuals'),
            isActive : routes.marketManuals,
            icon     : <BiTransfer className="text-2xl"/>,
            label    : 'manual transfer'
        }, 
        {
            routeName: 'offers',
            hasAccess: hasPermission('offers'),
            isActive : routes.offers,
            icon     : <FaBalanceScaleLeft className="text-2xl"/>,
            label    : 'market offers'
        }, 
    ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () =>
    {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span
                    className=" text-inherit"
                >
                    Marketplace
                </span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
