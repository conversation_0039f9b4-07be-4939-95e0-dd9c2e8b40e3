//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { CiSearch } from 'react-icons/ci';
import { MdKeyboardBackspace } from 'react-icons/md';

//* COMPONENTS
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AdminLayout from '@/Layouts/AdminLayout'
import InputError from '@/Components/InputError';

//* PARTIALS
import PartialUserManagementCategoryFormSetPermissions from './Partials/PartialUserManagementCategoryFormSetPermissions';

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementCategoryUpdate(props)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    const [stateInputName, setStateInputName]                               = useState(props.item.name);
    const [stateSearch, setStateSearch]                                     = useState('');
    const [statePermissions, setStatePermissions]                           = useState(props.permissions);
    const [stateInputSelectedPermissions, setStateInputSelectedPermissions] = useState(props.assignedPermissions.map(a => a.id));
    const [stateErrorMessageName, setStateErrorMessageName]                 = useState(null);
    const [stateErrorMessagePermissions, setStateErrorMessagePermissions]   = useState(null);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleSubmit()
    {
        setStateErrorMessageName(null)
        setStateErrorMessagePermissions(null)
            
        router.post(
            route("user-management.category.update", {id : props.item.id}),
            {
                name       : stateInputName,
                permissions: stateInputSelectedPermissions
            },
            {
                onError: (error) =>
                {
                    setStateErrorMessageName(error.name ?? null)
                    setStateErrorMessagePermissions(error.permissions ?? null)
                }
            }
        );
    }

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] flex flex-col rounded-lg">
                <div className="mb-[10px] flex items-center text-lg font-semibold">
                    <Link
                        href={route("user-management.category")}
                        replace={true}
                        className=" hover:!shadow-none flex pl-0"
                    >
                        <MdKeyboardBackspace className="text-3xl rounded-full p-1 cursor-pointer hover:text-gray-700 text-gray-700" />
                        <span className="hover:text-gray-700 text-gray-700 pt-[1px] pl-1"> Back To Categories </span>
                    </Link>
                </div>

                <div className="header">
                    <div className='text-3xl font-semibold mb-4'>Update Category</div>
                </div>

                <PartialUserManagementCategoryFormSetPermissions
                    statePermissions={statePermissions}
                    setStatePermissions={setStatePermissions}
                    stateInputName={stateInputName} 
                    setStateInputName={setStateInputName}
                    stateSearch={stateSearch} 
                    setStateSearch={setStateSearch}
                    stateInputSelectedPermissions={stateInputSelectedPermissions}
                    setStateInputSelectedPermissions={setStateInputSelectedPermissions}
                    stateErrorMessageName={stateErrorMessageName}
                    setStateErrorMessageName={setStateErrorMessageName}
                    stateErrorMessagePermissions={stateErrorMessagePermissions}
                    setStateErrorMessagePermissions={stateErrorMessagePermissions}
                    handleEventSubmit={handleSubmit}
                />
            </div>
        </AdminLayout>
    )
}
