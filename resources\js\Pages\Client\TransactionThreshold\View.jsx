import AdminLayout from "@/Layouts/AdminLayout";
import { Link, useForm } from "@inertiajs/react";
import {
    MdKeyboardBackspace,
    MdRestartAlt,
    MdOutlineSettings
} from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import Item from "@/Components/Client/TransactionThreshold/Item";

export default function View({ transactions, email, userId }) {
    const { data, setData, patch, processing, errors, reset } =
        useForm({
            userId: userId,
            email: email,
            payload: transactions.map((transaction) => ({
                transactionId: transaction.transaction_id,
                name: transaction.transaction_name,
                userLimit: transaction.user_limit,
                customLimit: transaction.custom_limit,
                adjustment: 0
            })),
        });

    const updateAdjustment = (transactionId, input) => {
        const updatedPayload = data.payload.map((item) =>
            item.transactionId === transactionId
                ? { ...item, adjustment: input }
                : item
        );

        setData("payload", updatedPayload);
    };

    const hasAdjustment = data.payload.some(transaction => transaction.adjustment !== 0);

    const handleUpdateAll = (e) => {
        e.preventDefault()
        patch(route("client.setting.transaction.threshold-updateAll"), {
            userId: data.userId,
            payload: data.payload,
            email: data.email
        }, {
            onError: (errors) => toast.error(errors.message, { autoClose: 8000 }),
            onSuccess: () => {
                toast.success("Updated all transaction limits successfully");
                reset();
            },
        });
    }

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1100px] mt-16 flex flex-col space-y-4 px-8">
                <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                    <Link
                        href={route("client.transaction.threshold")}
                    >
                        <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                    </Link>
                    <span className="font-semibold">
                        Edit User Transaction Custom Limit - {email}
                    </span>
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th className="py-2">
                                    <span className="flex items-center pl-2 space-x-2">Transaction</span>
                                </th>
                                <th className="py-2">
                                    <span>User Limit</span>
                                </th>
                                <th className="py-2">
                                    <span>Custom Limit</span>
                                </th>
                                <th className="py-2">
                                    <span>Adjustments (-/+)</span>
                                </th>
                                <th className="py-2">
                                    <span>Total Limit</span>
                                </th>
                                <th className="flex justify-center py-2">
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {data.payload.map((item, index) => (
                                <Item
                                    item={item}
                                    userId={userId}
                                    email={email}
                                    updateAdjustment={updateAdjustment}
                                    key={"tti-" + index}
                                />
                            ))}
                        </tbody>
                    </table>
                    <div>
                        <span className="flex justify-end space-x-1">
                            <button
                                className={`border border-gray-300 rounded-md px-4 py-2 text-sm ${hasAdjustment ? "block px-4 py-1 bg-primary border border-transparent rounded-md text-white hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150" : "opacity-25"}`}
                                disabled={!hasAdjustment || processing}
                                onClick={handleUpdateAll}
                            >
                                Update All
                            </button>
                            <button
                                className={`flex border items-center border-gray-300 rounded-md px-4 py-2 text-sm text-gray-700 ${hasAdjustment ? "focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150" : "opacity-25"}`}
                                disabled={!hasAdjustment || processing}
                                onClick={() => { reset() }}
                            >
                                Reset All
                                <MdRestartAlt className='text-2xl pl-1' />
                            </button>
                        </span>
                    </div>
                </div>
            </div>
        </AdminLayout >
    );
}
