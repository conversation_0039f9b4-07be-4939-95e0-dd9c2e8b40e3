<?php

namespace App\Modules\Guest\Requests;

use App\Modules\Guest\Constants\RequestStatus;
use App\Modules\Guest\Services\AdminResponseService;
use Illuminate\Foundation\Http\FormRequest;

class AdminDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'data.*' => ['required', 'array'],
        ];
    }

    public function delete()
    {
        $status = RequestStatus::DELETED;
        AdminResponseService::updateStatusAll($this->all(), $status);
        AdminResponseService::queueSoftDelete($this->all());
    }
}
