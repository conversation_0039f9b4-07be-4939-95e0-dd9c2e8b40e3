<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('afternic_sales', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('afternic_sale_audit_id');
            
            $table->bigInteger('afternic_commission_id');
            $table->foreign('afternic_sale_audit_id')->references('id')->on('afternic_sale_audits');
            $table->string('status')->default('Pending');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('afternic_sales');
    }
};
