//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import Modal from '@/Components/Modal'; 

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementCategoryDeleteModalComponent(
    {
        //! PROPS
        type          = "single",
        selectedItem  = null,

        //! STATES 
        stateIsModalOpen,
        
        //! EVENTS
        handleEventModalClose = () => alert('close'),
        handleEventConfirm    = () => alert('confirm')
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES

    //...

    //! FUNCTIONS
    function handleSelfClose()
    {
        handleEventModalClose(); 
    }

    function handleConfirm()
    {
        handleEventConfirm();
        handleEventModalClose();
    }

    return (
        <Modal
            show={stateIsModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='xl'
        >
            <div
                className={`
                    flex flex-col justify-around
                    px-10 py-5
                    gap-y-2
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex flex-col gap-2 pb-4'
                >
                    <div
                        className='text-lg text-primary font-bold'
                    >
                        {
                            type == 'single'
                                ?
                                    selectedItem == null ? 'No Roles Selected' : `${selectedItem.role} | Delete`
                                :
                                    'Roles | Delete'
                        }
                    </div>
                </section>

                <hr />

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-y-6 py-2 max-h-96 overflow-auto'
                >
                    <aside
                        className='flex gap-4'
                    >
                        {
                            type == 'single'
                                ?
                                    selectedItem == null
                                        ?
                                            'No Role Selected'
                                        :
                                            <div
                                                className='text-md'
                                            >
                                                This role will be deleted. Please confirm this action.
                                            </div>

                                :
                                    <div
                                        className='text-md'
                                    >
                                        These roles will be deleted. Please confirm this action.
                                    </div>
                        }
                    </aside>
                </section>

                <section
                    className='flex justify-end gap-x-5'
                >
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleSelfClose}
                    >
                        Cancel
                    </AppButtonComponent>
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleConfirm}
                    >
                        Confirm
                    </AppButtonComponent>
                </section>
            </div>
        </Modal>
    );
}
