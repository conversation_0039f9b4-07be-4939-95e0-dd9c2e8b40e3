import React from "react";

export default function Checkbox({
    name,
    value,
    handleChange,
    checked = false,
    disabled = false,
    className
}) {
    return (
        <input
            type="checkbox"
            checked={checked}
            name={name}
            value={value}
            className={`rounded border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500 ${className}`}
            onChange={(e) => handleChange(e)}
            disabled={disabled}
        />
    );
}
