import React, { useState, useEffect } from "react";
import { IoMdArrowBack } from "react-icons/io";
import AdminLayout from "@/Layouts/AdminLayout";
import { usePage, router } from "@inertiajs/react";
import Filter from "@/Components/EmailHistory/Filter";
import EmailTable from "@/Components/EmailHistory/Item/EmailTable";
import EmailDetails from "@/Components/EmailHistory/Item/EmailDetails";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import SearchInput from "@/Components/EmailHistory/SearchInput";
import { MdFilterList, MdOutlineFilterAlt } from "react-icons/md";

export default function Index() {
    const {
        emails = [],
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
        links,
        searchTerm: initialSearchTerm = "",
        limit: initialLimit = 20, // Default value from props or URL
    } = usePage().props;

    const [history, setHistory] = useState(null);
    const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
    const [limit, setLimit] = useState(initialLimit);
    const [isLoading, setIsLoading] = useState(false);

    // Sync state with URL params on page load or change
    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const urlSearchTerm = urlParams.get("searchTerm") || "";
        const urlLimit = urlParams.get("limit") || 20;

        setSearchTerm(urlSearchTerm);
        setLimit(urlLimit);
    }, []);

    useEffect(() => {
        // Listen for Inertia page finish events to control loading state
        const handleStart = () => setIsLoading(true);
        const handleFinish = () => setIsLoading(false);

        document.addEventListener('inertia:start', handleStart);
        document.addEventListener('inertia:finish', handleFinish);

        return () => {
            document.removeEventListener('inertia:start', handleStart);
            document.removeEventListener('inertia:finish', handleFinish);
        };
    }, []);

    const handleEmailClick = (email) => {
        setHistory(email);
    };

    const handleBackClick = () => {
        setHistory(null);
    };

    const handleSearchChange = (searchParams) => {
        setIsLoading(true);
        const lowerCaseParams = Object.fromEntries(
            Object.entries(searchParams).map(([key, value]) => [
                key,
                value.toLowerCase(),
            ])
        );
        setSearchTerm(lowerCaseParams);

        // Update URL and reload with filters and limit
        const updatedParams = { ...lowerCaseParams, limit };
        router.get(route("email.history"), updatedParams, {
            preserveState: true,
        });
    };

    const handlePageChange = (url) => {
        setIsLoading(true);
        const updatedParams = { ...searchTerm, limit };
        router.get(url, updatedParams, { preserveState: true });
    };

    const handleLimitChange = (e) => {
        const newLimit = e.target.value;
        setLimit(newLimit);
        setIsLoading(true);

        // Update URL with the new limit value and keep searchTerm intact
        const updatedParams = { ...route().params, limit: newLimit, page: 1 };
        router.get(route("email.history"), updatedParams, {
            preserveState: true,
        });
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col justify-between">
                {history ? (
                    <EmailDetails
                        history={history}
                        onBackClick={handleBackClick}
                        links={links}
                    />
                ) : (
                    <>
                        <h1 className="text-4xl font-bold">
                            System Email Notifications History
                        </h1>
                        <p className="text-gray-600">
                            The history of all system-generated email
                            notifications, including their status and details.
                        </p>
                        <div
                            className="flex justify-start"
                            style={{ position: "relative", top: "25px" }}
                        >
                            <label className="mr-2 text-sm pt-1 text-gray-600">
                                Show
                            </label>
                            <select
                                value={limit}
                                onChange={handleLimitChange}
                                className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                            >
                                {[20, 25, 30, 40, 50, 100].map((val) => (
                                    <option key={val} value={val}>
                                        {val}
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="flex justify-between items-center mt-4 pt-4 pb-4">
                            <div className="flex items-center space-x-2">
                                <label className="flex items-center">
                                    <MdOutlineFilterAlt />
                                    <span className="ml-2 text-sm text-gray-600">
                                        Filter:
                                    </span>
                                </label>
                                <Filter onSearchChange={handleSearchChange} />
                            </div>

                            <SearchInput onSearchChange={handleSearchChange} />
                        </div>

                        <div className="relative">
                            <EmailTable
                                emails={emails}
                                onEmailClick={handleEmailClick}
                                hasSpinner={isLoading}
                            />
                        </div>
                        <div className="mt-4">
                            <CursorPaginate
                                onFirstPage={onFirstPage}
                                onLastPage={onLastPage}
                                nextPageUrl={nextPageUrl}
                                previousPageUrl={previousPageUrl}
                                itemCount={itemCount}
                                total={total}
                                itemName="item"
                                onPageChange={handlePageChange}
                            />
                        </div>
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
