<?php

namespace App\Modules\BillingClient\Contracts;

use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Modules\BillingClient\Services\Interfaces\AccountPaymentSummary;
use App\Modules\BillingClient\Services\Interfaces\DomainPaymentSummary;
use App\Modules\BillingClient\Services\Interfaces\MarketPlacePaymentSummary;
use App\Modules\BillingClient\Services\Interfaces\MultiCheckoutPaymentSummary;
use Exception;

class PaymentSummaryPicker implements PaymentSummaryPickerInterface
{
    public function getType(string $type)
    {
        switch ($type) {
            case PaymentSummaryType::PAYMENT_INVOICE:
            case PaymentSummaryType::PAYMENT_REIMBURSEMENT:
                return new DomainPaymentSummary;
            case PaymentSummaryType::MARKETPLACE_INVOICE:
            case PaymentSummaryType::MARKETPLACE_REIMBURSEMENT:
                return new MarketPlacePaymentSummary;
            case PaymentSummaryType::ACCOUNT_BALANCE:
                return new AccountPaymentSummary;
            case PaymentSummaryType::MULTI_CHECKOUT_INVOICE:
                return new MultiCheckoutPaymentSummary;
            default:
                throw new Exception('Payment not supported.');
        }
    }
}
