<?php

use App\Modules\PendingDelete\Controllers\PendingDeleteController;
use App\Modules\RequestDelete\Controllers\DeleteRequestController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('pending-delete')->group(function () {
    Route::get('/', [PendingDeleteController::class, 'index'])->name('domain.pending-delete.view');
    Route::get('/request', [DeleteRequestController::class, 'index'])->name('domain.delete-request.view');
    Route::post('/request', [DeleteRequestController::class, 'store'])->name('domain.support-feedback.store');
    Route::post('/request/approve', [DeleteRequestController::class, 'approve_delete'])->name('domain.delete-request.approve');
    Route::post('/request/reject', [DeleteRequestController::class, 'reject_delete'])->name('domain.delete-request.reject');
    Route::post('/request/cancel', [DeleteRequestController::class, 'cancel_delete'])->name('domain.delete-request.cancel');
    Route::post('/request/delete', [DeleteRequestController::class, 'create_delete'])->name('domain.delete-request.delete');
    Route::post('/delete/summary', [PendingDeleteController::class, 'deleteSummary'])->name('domain.pending-delete.summary');
    Route::delete('/delete', [PendingDeleteController::class, 'delete'])->name('domain.pending-delete.delete');
});
