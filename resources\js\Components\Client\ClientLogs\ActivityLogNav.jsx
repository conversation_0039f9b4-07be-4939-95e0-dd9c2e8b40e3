import React from 'react';
import NavLink from "@/Components/NavLink";

const ActivityLogNav = ({ postRouteName }) => {
    const currentRoute = route().current();
    const activeRoutes = [
        "client.logs.security",
        "client.logs.domain",
        "client.logs.system",
        "client.logs.account"
    ];

    const isActive = activeRoutes.some(routePattern => 
        currentRoute && currentRoute.startsWith(routePattern)
    );

    return (
        <NavLink
            href={route("client.logs.security.all")}
            active={isActive}
        >
            <span className="flex space-x-4">
                <span className="text-inherit">Activity Logs</span>
            </span>
        </NavLink>
    );
};

export default ActivityLogNav;
