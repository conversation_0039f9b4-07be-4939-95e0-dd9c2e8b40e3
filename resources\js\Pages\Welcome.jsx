import { Link } from "@inertiajs/react";

export default function Welcome(props) {
    const currentYear = new Date().getFullYear();
    return (
        <div className="bg-gray-100 min-h-screen">
            <div className="flex justify-end p-6">
                <Link
                    href={route("login")}
                    className=" hover:bg-gray-200 text-sm text-gray-600 hover:text-gray-900 rounded-sm px-4 py-2 focus:outline-none "
                >
                    <span>Login</span>
                </Link>
            </div>
            <div className="flex justify-center flex-col p-10 md:flex-row">
                <div className="flex flex-col bg-[url('/assets/images/landing-page/blob.svg')] bg-no-repeat bg-left bg-contain items-left py-3 space-y-2 min-h-[20rem] pt-20">
                    <span className="text-primary text-xl lg:text-3xl font-semibold">
                        Strange Domains.
                    </span>
                    <span className="font-bold  text-4xl lg:text-7xl">
                        <span>Coming</span>
                        <span className="pl-4 text-primary">Soon!</span>
                    </span>
                    <button className="text-white bg-primary w-max px-6 py-2 rounded-full">
                        Contact Us
                    </button>
                </div>
                <div>
                    <img
                        className="h-full w-auto"
                        src="/assets/images/landing-page/world-map.svg"
                        alt="background"
                    />
                </div>
            </div>
            <div
                className="flex flex-col bg-gray-700 py-16 md:flex-row "
                style={{
                    clipPath: `polygon(0 6%, 100% 0, 100% 94%, 0 100%)`,
                }}
            >
                <div className="space-y-4  text-white px-10 text-justify md:w-1/2 lg:w-3/4">
                    <span className="text-primary text-xl font-semibold">
                        About <span className="text-white">Us</span>
                    </span>
                    <p>
                        We help individuals and business with their goals. Lorem
                        ipsum dolor sit amet, consectetur adipiscing elit, sed
                        do eiusmod tempor incididunt ut labore et dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud
                        exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo consequat. Duis aute irure dolor in
                        reprehenderit in voluptate velit esse cillum dolore eu
                        fugiat nulla pariatur. Excepteur sint occaecat cupidatat
                        non proident, sunt in culpa qui officia deserunt mollit
                        anim id est laborum.
                    </p>
                    <p>
                        We help individuals and business with their goals. Lorem
                        ipsum dolor sit amet, consectetur adipiscing elit, sed
                        do eiusmod tempor incididunt ut labore et dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud
                        exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo consequat. Duis aute irure dolor in
                        reprehenderit in voluptate velit esse cillum dolore eu
                        fugiat nulla pariatur. Excepteur sint occaecat cupidatat
                        non proident, sunt in culpa qui officia deserunt mollit
                        anim id est laborum.
                    </p>
                    <p>
                        We help individuals and business with their goals. Lorem
                        ipsum dolor sit amet, consectetur adipiscing elit, sed
                        do eiusmod tempor incididunt ut labore et dolore magna
                        aliqua. Ut enim ad minim veniam, quis nostrud
                        exercitation ullamco laboris nisi ut aliquip ex ea
                        commodo consequat. Duis aute irure dolor in
                        reprehenderit in voluptate velit esse cillum dolore eu
                        fugiat nulla pariatur. Excepteur sint occaecat cupidatat
                        non proident, sunt in culpa qui officia deserunt mollit
                        anim id est laborum.
                    </p>
                </div>
                <div className="md:w-1/2 lg:w-1/3 px-10">
                    <img
                        className="h-full w-auto"
                        src="/assets/images/landing-page/happy-announcement.svg"
                        alt="background"
                    />
                </div>
            </div>
            <div className="flex flex-col p-10 space-y-20 md:items-center">
                <span className="text-primary text-xl font-semibold">
                    Services <span className="text-gray-800">Offered</span>
                </span>
                <div className="flex flex-col p-10 space-y-20 md:grid md:grid-cols-3 md:space-y-0 md:space-x-4">
                    <div className="border rounded-md border-gray-200 p-5 bg-white drop-shadow-sm">
                        <img
                            className=" h-24 w-auto absolute right-0 -top-16"
                            src="/assets/images/landing-page/transfer.svg"
                            alt="background"
                        />
                        <span className=" font-semibold text-lg text-black">
                            Transfer
                        </span>
                        <p className="pt-2 text-justify">
                            Quickly and easily transfer your existing domains to
                            StrangeDomains with our automated, fast and
                            risk-free system. Transferring your domain happens
                            mostly in the background, giving you more time to
                            work on your next tasks.
                        </p>
                    </div>
                    <div className="border rounded-md border-gray-200 p-5 bg-white drop-shadow-sm">
                        <img
                            className=" h-24 w-auto absolute right-0 -top-16"
                            src="/assets/images/landing-page/search-buy-domain.svg"
                            alt="background"
                        />
                        <span className=" font-semibold text-lg text-black">
                            Search & Buy Domain
                        </span>
                        <p className="pt-2 text-justify">
                            Register your new domain quickly and create your
                            online identity. With the help of Strangedomains,
                            you don’t need any technical skills in registering
                            your desired domain. We got you!
                        </p>
                    </div>
                    <div className="border rounded-md border-gray-200 p-5 bg-white drop-shadow-sm">
                        <img
                            className=" h-24 w-auto absolute right-0 -top-16"
                            src="/assets/images/landing-page/push.svg"
                            alt="background"
                        />
                        <span className=" font-semibold text-lg text-black">
                            Push Domain
                        </span>
                        <p className="pt-2 text-justify">
                            Transfer your domain from your account to another
                            StrangeDomains account in just a few seconds. This
                            is free just for your since we value every
                            customer's penny.
                        </p>
                    </div>
                </div>
            </div>
            <div>
                <div className="bg-gray-700 p-10">
                    <div className="flex flex-row items-center">
                        <img
                            className=" h-auto w-16 "
                            src="/assets/images/logo/logo-white.png"
                            alt="background"
                        />

                        <span className="text-white">StrangeDomains</span>
                    </div>
                    <div className="md:pt-4 md:grid md:grid-cols-3 space-y-10 md:space-y-0">
                        <div>
                            <p className="flex flex-col text-white text-sm">
                                <span className=" uppercase text-white border-b border-white pb-1 mb-4 w-fit">
                                    contact
                                </span>
                                701 5th Avenue
                                <br />
                                New Kensington PA
                                <br />
                                15068
                                <br />
                                <EMAIL>
                            </p>
                        </div>
                        <div>
                            <p className="flex flex-col text-white text-sm">
                                <span className=" uppercase text-white border-b border-white pb-1 mb-4 w-fit">
                                    DOMAINS
                                </span>
                                Register
                                <br />
                                Transfer
                                <br />
                                Manage
                            </p>
                        </div>
                        <div>
                            <p className="flex flex-col text-white text-sm">
                                <span className=" uppercase text-white border-b border-white pb-1 mb-4 w-fit">
                                    About
                                </span>
                                We help individuals and business with their
                                goals.
                            </p>
                        </div>
                    </div>
                </div>

                <div className="bg-gray-900 p-10 space-y-2">
                    <div className="flex text-gray-50 space-x-3">
                        <a href="#" className="border-r pr-3">
                            Privacy Policy
                        </a>
                        <a href="#">Terms and Conditions</a>
                    </div>
                    <div className="text-gray-100 text-sm">
                    <p>© StrangeDomains { currentYear }. All Rights Reserved.</p>

                    </div>
                </div>
            </div>
        </div>
    );
}
