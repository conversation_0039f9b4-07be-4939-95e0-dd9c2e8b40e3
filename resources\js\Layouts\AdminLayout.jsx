//* PACKAGES
import { useState, useEffect } from "react";
import { usePage } from "@inertiajs/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdMenu, } from "react-icons/md";

//* COMPONENTS
import BalanceFlagItem from "@/Components/Epp/Account/BalanceFlagItem";
import NavigationActivityLogComponent from "@/Components/Navigation/NavigationActivityLogComponent";
import NavigationAdminLogComponent from "@/Components/Navigation/NavigationAdminLogComponent";
import NavigationEmailHistoryComponent from "@/Components/Navigation/NavigationEmailHistoryComponent";
import NavigationUserManagementComponent from "@/Components/Navigation/NavigationUserManagementComponent";
import NavigationBillingComponent from "@/Components/Navigation/NavigationBillingComponent";
import NavigationClientComponent from "@/Components/Navigation/NavigationClientComponent";
import NavigationDomainComponent from "@/Components/Navigation/NavigationDomainComponent";
import NavigationJobQueueComponent from "@/Components/Navigation/NavigationJobQueueComponent";
import NavigationRegistryComponent from "@/Components/Navigation/NavigationRegistryComponent";
import NavigationSettingsComponent from "@/Components/Navigation/NavigationSettingsComponent";
import NavigationSystemCreditsComponent from "@/Components/Navigation/NavigationSystemCreditsComponent";
import NavigationSystemLogComponent from "@/Components/Navigation/NavigationSystemLogComponent";
import NavigationMarketplaceComponent from "@/Components/Navigation/NavigationMarketplaceComponent";
import NavLink from "@/Components/NavLink";
import { NotificationDropdown } from "../Components/Notification/NotificationDropdown";
import { ProfileDropdown } from "../Components/Profile/ProfileDropdown";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//! OLD NAVIGATION COMPONENTS
import ActivityLogNav from "@/Components/Client/ClientLogs/ActivityLogNav";
import EmailHistoryNav from "@/Components/EmailHistory/EmailHistoryNav";
import BillingNav from "../Components/Billing/BillingNav";
import ClientNav from "../Components/Client/ClientNav";
import DomainsNav from "@/Components/Client/Domains/DomainsNav";
import MarketPlaceNav from "@/Components/MarketPlace/MarketPlaceNav";
import QueueJobNav from "@/Components/Job/QueueJobNav";
import RegistryNav from "@/Components/Epp/RegistryNav";
import SettingNav from "@/Components/Setting/SettingNav";
import SystemLogNav from "@/Components/SystemLogs/SystemLogNav";
import SystemCreditNav from "@/Components/Client/SystemCreditsNav";

export default function AdminLayout(
    {
        children,
        postRouteName = "",
        hideNav = false,
        selected = "",
    }
) {
    //! PACKAGE
    const registryWarning = usePage().props.registryWarning;
    const registryFlag = usePage().props.registryFlag;
    const { flash } = usePage().props;

    //! VARIABLES
    const navigationComponents =
        [
            <NavigationDomainComponent key={'domain'} postRouteName={postRouteName} />,
            <NavigationClientComponent key={'client'} postRouteName={postRouteName} />,
            <NavigationJobQueueComponent key={'job'} postRouteName={postRouteName} />,
            <NavigationRegistryComponent key={'registry'} postRouteName={postRouteName} />,
            <NavigationBillingComponent key={'billing'} postRouteName={postRouteName} />,
            <NavigationEmailHistoryComponent key={'emailHistory'} postRouteName={postRouteName} />,
            <NavigationActivityLogComponent key={'activityLogs'} postRouteName={postRouteName} />,
            <NavigationAdminLogComponent key={'adminLogs'} postRouteName={postRouteName} />,
            <NavigationSystemLogComponent key={'systemLogs'} postRouteName={postRouteName} />,
            <NavigationSettingsComponent key={'settings'} postRouteName={postRouteName} />,
            <NavigationUserManagementComponent key={'userManagement'} postRouteName={postRouteName} />,
            <NavigationSystemCreditsComponent key={'systemCredits'} postRouteName={postRouteName} />,
            <NavigationMarketplaceComponent key={'marketplace'} postRouteName={postRouteName} />
        ]

    //! STATES
    const [showNav, setShowNav] = useState(hideNav ? false : true);

    //! FUNCTIONS
    //...

    //! USE EFFECTS
    useEffect(() => {
        flash.route_message ? toast.error(flash.route_message) : null;
    }, [flash.route_message]);

    useEffect(
        () => {
            flash.successMessage ? toast.success(flash.successMessage) : null;
        },
        [flash.successMessage]
    );

    return (
        <div
            className="bg-white"
        >
            <nav
                className="bg-primary flex items-center justify-between py-4 px-8 text-white border-b border-gray-400 shadow-md text-3xl"
            >
                <button
                    onClick={() => {
                        setShowNav(!showNav);
                    }}
                >
                    <MdMenu className=" hover:bg-black hover:bg-opacity-20  rounded-full text-3xl p-1 transition duration-150" />
                </button>

                <div
                    className="flex items-center space-x-6 cursor-pointer"
                >
                    <NotificationDropdown />
                    <ProfileDropdown />
                </div>
            </nav>
            <main
                className="flex justify-between min-h-[calc(100vh-63px)]"
            >
                {/* <aside className="flex-none w-max"> */}
                <ToastContainer autoClose={1000} />
                <aside
                    className={`flex-none bg-white border-r border-gray-200 ease-in-out duration-300 z-10 min-h-[calc(100vh-63px)] hover:shadow-none shadow-lg overflow-hidden ${showNav ? "w-80" : "w-0"}`}
                >
                    <div
                        className="flex flex-col  pt-6 space-y-2 text-gray-700  font-semibold  whitespace-nowrap"
                    >
                        <NavLink
                            href={route("dashboard")}
                            active={route().current("dashboard")}
                        >
                            <span className=" text-inherit ">Dashboard</span>
                        </NavLink>
                        {
                            navigationComponents.map(navigation => navigation)
                        }
                    </div>
                </aside>
                <aside
                    className="container mx-auto pt-8 px-8 pb-16"
                >
                    <BalanceFlagItem
                        keyId={"rf"}
                        registryProps={registryFlag}
                        intentType={"danger"}
                    />
                    <BalanceFlagItem
                        keyId={"rw"}
                        registryProps={registryWarning}
                        intentType={"warning"}
                    />
                    {children}
                </aside>
            </main>
        </div>
    );
}
