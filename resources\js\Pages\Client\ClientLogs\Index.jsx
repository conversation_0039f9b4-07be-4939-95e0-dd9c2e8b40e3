import React from "react";
import AdminLayout from "@/Layouts/AdminLayout";
import {
    <PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdOutlinePersonOutline,
    MdExitToApp,
    MdSecurity,
    MdPersonAdd,
    MdOutlineCheckCircle,
    MdOutlineContactPage,
} from "react-icons/md";
import { HiOutlinePencilSquare } from "react-icons/hi2";
import { TbCategoryPlus } from "react-icons/tb";
import { Link } from "@inertiajs/react";
import Filter from "@/Components/Client/ClientLogs/Filter";
import CursorPaginate from "@/Components/Util/CursorPaginate";

export default function ClientLogs({ logs, statuses, pagination }) {
    const routeName = "client.logs";

    const typeIcons = {
        "SIGN IN": <MdOutlineCheckCircle />,
        "SIGN OUT": <MdExitToApp />,
        "REGISTER": <MdPersonAdd />,
        "PROFILE UPDATE": <HiOutlinePencilSquare />,
        "SECURITY UPDATE": <MdSecurity />,
        "CATEGORY UPDATE": <TbCategoryPlus />,
        "CONTACT UPDATE": <MdOutlineContactPage />,
    };

    const getStatusLabel = (type) => {
        const upperType = type.toUpperCase();
        const label = statuses[upperType] || type;
        const icon = typeIcons[upperType] || <MdOutlinePersonOutline />;
        return { label, icon };
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col justify-between">
                <h1 className="text-4xl font-bold pl-3">
                    Client Activity Logs
                </h1>
                <p className="text-gray-600 pl-3">
                    Track and monitor client interactions with real-time
                    activity logs.
                </p>
                <div className="flex justify-between items-center mt-4 pt-4 pb-4 pl-3">
                    <div className="flex items-center space-x-2">
                        <MdFilterList className="text-xl" />
                        <span>Filter:</span>
                        <Filter
                            transactionTypes={statuses}
                            routeName={routeName}
                        />
                    </div>
                </div>
                <div className="mt-4">
                    <table className="min-w-full bg-white">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="py-2 px-4 text-left">User</th>
                                <th className="py-2 px-4 text-left">Email</th>
                                <th className="py-2 px-4 text-left">
                                    Last Activity
                                </th>
                                <th className="py-2 px-4 text-left">
                                    Last Active
                                </th>
                                <th className="py-2 px-4"></th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {Array.isArray(logs) ? (
                                logs.map((log, index) => {
                                    const { label, icon } = getStatusLabel(
                                        log.type
                                    );
                                    return (
                                        <tr key={index} className="border-b">
                                            <td className="w-3xs py-2 px-4">
                                                <div className="text-2xl flex items-center justify-center w-8 h-8 rounded-full border">
                                                    <MdOutlinePersonOutline />
                                                </div>
                                            </td>
                                            <td className="w-3xs py-2 px-4">
                                                {log.email}
                                            </td>
                                            <td className="w-3xs py-2 px-4 flex items-center space-x-2">
                                                <span className="text-lg pt-2">
                                                    {icon}
                                                </span>
                                                <span className="pt-2">
                                                    {label}
                                                </span>
                                            </td>
                                            <td className="w-3xs py-2 px-4">
                                                {log.created_at
                                                    ? new Intl.DateTimeFormat(
                                                          "en-US"
                                                      ).format(
                                                          new Date(
                                                              log.created_at
                                                          )
                                                      )
                                                    : "Unknown"}
                                            </td>
                                            <td className=" w-1/6 py-2 px-4">
                                                <Link
                                                    href={route(
                                                        "client.logs.security.all"
                                                    )}
                                                    className="block px-0 text-center py-2 bg-primary border border-transparent rounded-md font-semibold text-sm 
                                                                text-white tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 
                                                                focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition
                                                                ease-in-out duration-150"
                                                >
                                                    View All Activities
                                                </Link>
                                            </td>
                                        </tr>
                                    );
                                })
                            ) : (
                                <tr>
                                    <td
                                        colSpan="5"
                                        className="text-center py-4"
                                    >
                                        No logs available.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>

                <div className="mt-6">
                    <CursorPaginate
                        onFirstPage={pagination.onFirstPage}
                        onLastPage={pagination.onLastPage}
                        nextPageUrl={pagination.nextPageUrl}
                        previousPageUrl={pagination.previousPageUrl}
                        itemCount={pagination.itemCount}
                        total={pagination.total}
                        itemName="item"
                    />
                </div>
            </div>
        </AdminLayout>
    );
}
