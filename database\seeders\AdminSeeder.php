<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (strcmp(env('APP_ENV'), 'local') != 0) {
            return;
        }

        DB::table('admins')->insert([
            [
                'name'           => 'admin 1',
                'email'          => 'a@a.a',
                'password'       => Hash::make('a'),
                //'is_super_admin' => true,
                'status'         => 'ACTIVE'
            ],
            [
                'name'           => 'admin 2',
                'email'          => 'b@b.b',
                'password'       => Hash::make('a'),
                //'is_super_admin' => true,
                'status'         => 'ACTIVE'
            ],
        ]);
    }
}
