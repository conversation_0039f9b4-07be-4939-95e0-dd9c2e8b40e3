<?php

namespace App\Modules\MarketPlace\Controllers;

use Inertia\Inertia;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\ManualTransferRequest;
use Illuminate\Support\Facades\DB;

class MarketManualTransferController extends Controller
{
    public function index()
    {
        $data = DB::table('public.market_place_domains AS  mpd')
            ->select('u.first_name', 'u.last_name', 'mpd.order_id', 'd.name as domain', 'mpd.updated_at', 'mpd.epp_error')
            ->join('public.registered_domains as rd', 'mpd.registered_domain_id', '=', 'rd.id')
            ->join('public.domains as d', 'rd.domain_id', '=', 'd.id')
            ->join('public.users as u', 'mpd.user_id', '=', 'u.id')
            ->where('mpd.epp_error', '!=', 'null')
            ->get();

        return Inertia::render('MarketPlace/ManualTransfer', ['data' => 'hi from audits', 'data' => $data]);
    }

    public function store(ManualTransferRequest $request) : void
    {
        $request->storeAuth();
    }
}
