//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import Modal from '@/Components/Modal'; 

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementRoleViewPermissionsModalComponent(
    {
        //! PROPS
        selectedItem = null,         

        //! STATES 
        stateIsModalOpen,
        
        //! EVENTS
        handleEventModalClose
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    //...

    //! FUNCTIONS
    function handleSelfClose()
    {
        handleEventModalClose(); 
    }

    return (
        <Modal
            show={stateIsModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='3xl'
        >
            <div
                className={`
                    flex flex-col justify-around
                    px-10 py-5
                    gap-y-2
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex flex-col gap-2 pb-4'
                >
                    <div
                        className='text-lg text-primary font-bold'
                    >
                        {selectedItem == null ? 'No Category Selected' : `${selectedItem.name} | Permissions`}
                    </div>
                </section>

                <hr />

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-y-6 py-2 max-h-96 overflow-auto'
                >
                    <aside
                        className='flex gap-4'
                    >
                        {
                            selectedItem == null
                                ?
                                    'No Category Selected'
                                :
                                    selectedItem.permissions.length == 0
                                        ?
                                            'No Permissions Found' 
                                        :
                                            <div
                                                className='grid grid-cols-2 gap-4 w-full'
                                            >
                                                {
                                                    selectedItem.permissions.map(
                                                        (item, index) => 
                                                        {
                                                            return (
                                                                <div
                                                                    key={index}
                                                                    className='font-medium text-sm text-gray-700'
                                                                >
                                                                    {item.name}
                                                                </div>
                                                            ); 
                                                        }
                                                    )
                                                }
                                        
                                            </div>
                        }
                    </aside>
                </section>

                <section
                    className='flex justify-end gap-x-5'
                >
                    <AppButtonComponent
                        type='button'
                        className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                        handleEventClick={handleSelfClose}
                    >
                        Close
                    </AppButtonComponent>
                </section>
            </div>
        </Modal>
    );
}
