import React from "react";
import { IoMdClose } from "react-icons/io";
import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import PrimaryButton from "@/Components/PrimaryButton";

export default function Payload({ data, onClose }) {
    if (!data) return null;
    let parsedPayload = null;
    parsedPayload = JSON.parse(data.payload);


    const renderPayloadContent = () => {
        if (parsedPayload) {
            return (
                <div className="overflow-y-auto max-h-96 border border-gray-200 rounded-md">
                    <pre className="text-sm whitespace-pre-wrap bg-gray-50 p-4 rounded-md">
                        {JSON.stringify(parsedPayload, null, 2)}
                    </pre>
                </div>
            );
        }
        return (
            <div className="text-gray-500 italic p-4 bg-gray-50 rounded-md">
                No structured payload data available
            </div>
        );
    };
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl">
                <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
                    <h3 className="text-xl font-bold text-gray-800">Domain Payload</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 focus:outline-none"
                    >
                        <IoMdClose className="w-6 h-6" />
                    </button>
                </div>
                
                <div className="p-6">
                    <div className="mb-6">
                        <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="col-span-2 md:col-span-1">
                                <div className="bg-gray-50 p-4 rounded-md">
                                    <h4 className="font-semibold text-gray-700 mb-2">Domain Information</h4>
                                    <div className="space-y-2">
                                        <div>
                                            <span className="text-gray-500">Domain:</span>{" "}
                                            <span className="font-medium">
                                                {data.domain_name || "Unknown"}
                                            </span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Type:</span>{" "}
                                            <span className="font-medium">
                                                {data.type || "Unknown"}
                                            </span>
                                        </div>
                                        <div>
                                            <span className="text-gray-500">Time:</span>{" "}
                                            <span className="font-medium">
                                                {data.created_at || "Unknown"}
                                            </span>
                                        </div>
                                        <div className="flex items-center">
                                            <span className="text-gray-500 mr-2">Status:</span>{" "}
                                            {data.status === "failed" ? (
                                                <span className="flex items-center text-red-500">
                                                    <FaTimesCircle className="mr-1" /> Failed
                                                </span>
                                            ) : (
                                                <span className="flex items-center text-green-500">
                                                    <FaCheckCircle className="mr-1" /> Success
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-span-2 md:col-span-1">
                                <div className="bg-gray-50 p-4 rounded-md h-full">
                                    <h4 className="font-semibold text-gray-700 mb-2">Message</h4>
                                    <div className="text-gray-700">{data.message || "No message available"}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 className="font-semibold text-gray-700 mb-2">Payload Data</h4>
                        {renderPayloadContent()}
                    </div>
                </div>
                
                <div className="px-6 py-2 flex justify-end">
                    <PrimaryButton
                        onClick={onClose}
                    >
                        Close
                    </PrimaryButton>
                </div>
            </div>
        </div>
    );
}
