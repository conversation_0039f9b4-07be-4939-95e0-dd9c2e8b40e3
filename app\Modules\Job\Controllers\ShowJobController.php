<?php

namespace App\Modules\Job\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Job\Requests\ShowJobDataForm;
use App\Modules\Job\Requests\ShowJobForm;
use Inertia\Inertia;

class ShowJobController extends Controller
{
    public function index(ShowJobForm $request)
    {
        return Inertia::render('Job/Jobs', $request->getData());
    }

    public function view(ShowJobDataForm $request)
    {
        return Inertia::render('Job/Job', $request->show());
    }

    public function getData(ShowJobDataForm $request)
    {
        $data = $request->getJobData();
        return response()->json($data);
    }
}
