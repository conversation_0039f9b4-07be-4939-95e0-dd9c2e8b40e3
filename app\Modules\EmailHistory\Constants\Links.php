<?php

namespace App\Modules\EmailHistory\Constants;

final class Links
{
    public const TERMS_AND_CONDITIONS = '/terms';

    public const REFUND_POLICY = '/refund-policy';

    public const PAYMENT_REFUND_VIEW = '/payment/summary/view';

    public const DOMAIN_EXPIRY_LIST = '/domain?orderby=expiry%3Aasc&status=All';

    public const REPORT_ABUSE_VIEW = '/report-abuse';

    public const REPORT_ABUSE_FORM = '/report-abuse-form';

    public const CONTACT_US_PAGE = '/contact';

    public const ACCOUNT_BALANCE_INDEX = '/account-balance';

    public static function getAll(string $url)
    {
        return [
            'TERMS_AND_CONDITIONS' => $url.self::TERMS_AND_CONDITIONS,
            'REFUND_POLICY' => $url.self::REFUND_POLICY,
            'PAYMENT_REFUND_VIEW' => $url.self::PAYMENT_REFUND_VIEW,
            'DOMAIN_EXPIRY_LIST' => $url.self::DOMAIN_EXPIRY_LIST,
            'REPORT_ABUSE_VIEW' => $url.self::REPORT_ABUSE_VIEW,
            'REPORT_ABUSE_FORM' => $url.self::REPORT_ABUSE_FORM,
            'CONTACT_US_PAGE' => $url.self::CONTACT_US_PAGE, 
            'ACCOUNT_BALANCE_INDEX' => $url.self::ACCOUNT_BALANCE_INDEX,
        ];
    }
}
