//* PACKAGES
import React, {useState, useEffect} from 'react'
import { <PERSON>, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { ImSortAlphaAsc, ImSortAlphaDesc } from "react-icons/im";

//* COMPONENTS
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function EmailTable(
    {
        emails,
        onEmailClick,
        hasSpinner
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const SORT_TYPE = {
        NAME_ASC: "NAME_ASC",
        NAME_DESC: "NAME_DESC",
    };

    const paramStatus = route().params.status;

    const { orderby } = route().params?.orderby
        ? route().params
        : { orderby: SORT_TYPE.CREATE_DATE_ASC };

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const orderToggle = (sort) => {
        const payload = {
            orderby: sort,
            ...(paramStatus && { status: paramStatus }),
        };

        router.get(route("email.history"), payload);
    };

    return (
        <div className="relative">
            <table className="min-w-full bg-white text-sm">
                <thead className="bg-gray-50">
                    <tr>
                        <th className="py-2 text-left flex 2">
                            <label className="w-28">Recipient Name</label>
                            <button
                                onClick={() =>
                                    orderToggle(
                                        orderby === SORT_TYPE.NAME_ASC
                                            ? SORT_TYPE.NAME_DESC
                                            : SORT_TYPE.NAME_ASC
                                    )
                                }
                                disabled={emails.length === 0 || hasSpinner}
                                className="flex items-center"
                            >
                                {orderby === SORT_TYPE.NAME_ASC ? (
                                    <ImSortAlphaAsc />
                                ) : (
                                    <ImSortAlphaDesc />
                                )}
                            </button>
                        </th>
                        <th className="py-2  text-left">Recipient Email</th>
                        <th className="py-2  text-left">Email Subject</th>
                        <th className="py-2  text-left">Date & Time Sent</th>
                        <th className="py-2  text-left">Email Type</th>
                    </tr>
                </thead>
                <tbody className="text-sm">
                    {hasSpinner ? (
                        <tr>
                            <td colSpan={5}>
                                <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg">
                                    <LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' />
                                    <br />
                                    <span className="relative top-9 left-72 ml-20">Loading Data...</span>
                                </div>
                            </td>
                        </tr>
                    ) : (
                        emails.map((email, index) => (
                            <tr key={index}>
                                <td className="py-2 pt-4 pb-4">
                                    {email.user_id === null ? "System" : email.name}
                                </td>
                                <td className="py-2 ">{email.recipient_email}</td>
                                <td className="text-blue-500 py-2">
                                    <button
                                        onClick={() => onEmailClick(email)}
                                        className="hover:underline text-left"
                                    >
                                        {email.subject}
                                    </button>
                                </td>
                                <td className="py-2">
                                    {new Intl.DateTimeFormat("en-US", {
                                        month: "numeric",
                                        day: "numeric",
                                        year: "numeric",
                                    }).format(new Date(email.created_at))}
                                    &nbsp;
                                    {new Intl.DateTimeFormat("en-US", {
                                        hour: "numeric",
                                        minute: "numeric",
                                        hour12: true,
                                    }).format(new Date(email.created_at))}
                                </td>
                                <td className="py-2 ">{email.email_type}</td>
                            </tr>
                        ))
                    )}
                </tbody>
            </table>
        </div>
    );
}
