import AdminLayout from "@/Layouts/AdminLayout";
import { Link, router } from "@inertiajs/react";
import {
    MdKeyboardBackspace,
    MdOutlineSettings
} from "react-icons/md";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import FeeItem from "../../../Components/Client/ExtensionFee/Item";

export default function View({ fees, name = "User", userId, extension_type }) {
    const isEmpty = (fees !== undefined) && Object.keys(fees).length === 0;
    const extensions = fees && Object.keys(fees);

    const paramExtension = route().params.extension;

    // console.log(fees);

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1100px] mt-20 flex flex-col space-y-10 px-8">
                <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                    <Link
                        href={route("client.extension.fee")}
                    >
                        <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                    </Link>
                    <span className="font-semibold">
                        Edit Custom User Extension Fees - {name}
                    </span>
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    {(!isEmpty) ? (extensions.map((ext, index) => {
                        return (
                            <Link
                                key={"lext-" + index}
                                as="button"
                                href={route("client.extension.fee-view", {
                                    id: userId,
                                    name: name,
                                    extension: ext,
                                })}
                            >
                                <div
                                    className={`hover:bg-gray-100 hover:text-link   px-5 py-1 rounded-sm ${paramExtension == ext &&
                                        "bg-gray-100 text-link"
                                        } `}
                                >
                                    <span className=" text-inherit lowercase">
                                        {ext}
                                    </span>
                                </div>
                            </Link>
                        );
                    })) : (
                        <div key={'d-empty'} className="flex flex-wrap space-y-2 md:divide-x-2 justify-between">
                            <div className="flex-none md:w-1/2 px-2">
                            </div>
                            <div className="flex flex-col space-y-2 md:pl-8 flex-auto">
                                <div>
                                    <span className="text-lg text-gray-700">
                                        No custom fees created. Please click Create button to start.
                                    </span>
                                </div>
                            </div >

                        </div>
                    )}
                </div>
                {(!isEmpty) && (<div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <span className="flex items-center pl-2 space-x-2">Type</span>
                                </th>
                                <th>
                                    <span>Extension Fee</span>
                                </th>
                                <th>
                                    <span>Adjustments (-/+)</span>
                                </th>
                                <th>
                                    <span>Total Fee</span>
                                </th>
                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {paramExtension && Object.keys(fees[paramExtension]).map((key, index) => (
                                <FeeItem
                                    item={fees[paramExtension][key]}
                                    key={"efi-" + index}
                                    extension_type={extension_type}
                                />
                            ))}
                        </tbody>
                    </table>
                </div>)}
            </div>
        </AdminLayout >
    );
}
