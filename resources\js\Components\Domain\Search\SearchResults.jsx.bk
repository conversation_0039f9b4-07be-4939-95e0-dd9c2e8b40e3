import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import { MdAddShoppingCart, MdOutlineReportOff } from "react-icons/md";
import { useState, useEffect } from "react";
import CartCounterState from "@/State/CartCounterState";
import SearchSearchNoDomainFound from "./SearchNoDomainFound";
import SearchDomainNow from "./SearchDomainNow";

export default function SearchResults({ searchResult, domainsList }) {
    const { setCartCounter } = CartCounterState();
    const [cart, setCart] = useState([]);
    const [selected, setSelected] = useState([]);
    const [selectAll, setSelectAll] = useState(false);

    useEffect(() => {
        setSelected([]);
        setSelectAll(false);
        setCart([]);
    }, [domainsList]);

    //determine the availability of the search domain
    const domainLabel = (domain) => {
        if (!domain.available) return <span className="">Not Available</span>;
        if (cart.includes(domain.name)) return <span>Added to cart</span>;

        return (
            <button onClick={() => onhandleAddToCart(domain.name)} type="button">
                <MdAddShoppingCart className=" text-xl hover:text-primary hover:cursor-pointer " />
            </button>
        );
    };

    const onHandleSelectItem = (value) => {
        let temp = [...selected];

        if (temp.includes(value)) {
            temp = temp.filter((e) => e != value);
        } else {
            temp.push(value);
        }

        setSelected(temp);

        console.log("cart"); console.log(cart);
    };

    const onHandleSelectAllItem = () => {
        let isSelected = !selectAll;
        setSelectAll(isSelected);
        if (isSelected) {
            setSelected(domainsList);
        } else {
            setSelected([]);
        }
    };

    const storeItemToCart = async (payload) => {
        // await axios
        //     .post(route("cart.store"), payload)
        //     .then((response) => {
        //         console.log(response);
        //     })
        //     .catch((error) => {
        //         console.log(error.response.data);
        //     });
    };

    const onHandleBulkStoreItemToCart = () => {
        // let allSelectedDomains = domainsList.filter(
        //     //remove not available | has been added to cart | only selected items
        //     (e) => {
        //         console.log(domainsList[e])
        //         domainsList[e] && !cart.includes(e) && selected.includes(e)}
        // );

        let allSelectedDomains = domainsList.filter((e) => (
            selected.includes(e)
        ));

        setCart([...cart, ...allSelectedDomains]);
        setCartCounter((number) => number + allSelectedDomains.length);
        allSelectedDomains = allSelectedDomains.map((e) => {
            return {
                name: e,
                extension: e.replace(/^[^\.]*\./, ""), //remove other string before "."
            };
        });

        console.log(cart);
        console.log("allSelectedDomains");
        console.log(allSelectedDomains);
        storeItemToCart(allSelectedDomains);
    };

    const onhandleAddToCart = (name) => {
        storeItemToCart([
            {
                name: name,
                extension: name.replace(/^[^\.]*\./, ""), //remove other string before "."
            },
        ]);

        setCart([...cart, name]);
        setCartCounter((number) => number + 1);

        onHandleSelectItem(name);
        
    };

    if (searchResult == undefined) return <SearchDomainNow />;
    else if (searchResult.length == 0)
        return <SearchSearchNoDomainFound />;
    else
        return (
            <form className="mx-auto container max-w-[900px] flex flex-col">
                <div>
                    <div className="flex flex-col my-4 space-y-4">
                        <label className="text-md text-primary border-b border-gray-200 text-center">
                            Search result
                        </label>
                        <div className="flex justify-end">
                            <PrimaryButton
                                processing={selected.length > 0 ? false : true}
                                onClick={onHandleBulkStoreItemToCart}
                                type="button"
                            >
                                Add Selected to cart
                            </PrimaryButton>
                        </div>
                    </div>
                    <div className=" space-y-2 border border-gray-100 rounded-lg flex flex-col relative">
                        <div className="py-2 px-4 bg-gray-100 ">
                            <label className="flex items-center ">
                                <Checkbox
                                    handleChange={onHandleSelectAllItem}
                                    checked={selectAll}
                                />
                                <span className="ml-2 text-sm">Select all</span>
                            </label>
                        </div>

                        {searchResult.map((domain, index) => (
                            <div
                                key={"sri-" + index}
                                className={
                                    "flex px-4 py-4 justify-between  hover:bg-gray-100 rounded-md text-gray-600  transition-all duration-500 ease-in-out" +
                                    `${
                                        cart.includes(domain.name) &&
                                        " hide-transition "
                                    }` +
                                    `${!domain.available && " opacity-40 "}`
                                }
                            >
                                <label className="flex items-center">
                                    {domain.available ? (
                                        <Checkbox
                                            name={domain.name}
                                            checked={(cart.includes(domain.name)) || selected.includes(domain.name)}
                                            handleChange={() =>
                                                onHandleSelectItem(domain.name)
                                            }
                                            disabled={
                                                (cart.includes(domain.name)) || false
                                            }
                                        />
                                    ) : (
                                        <MdOutlineReportOff />
                                    )}
                                    <span className="ml-2 text-sm">{domain.name}</span>
                                </label>
                                {domainLabel(domain)}
                            </div>
                        ))}
                    </div>
                </div>
            </form>
        );
}
