<?php

namespace App\Modules\CustomLogger\Services;

class AuthLogger implements CustomLoggerInterface
{
    private $implementation;

    private LogLevelControl $loglevelControl;

    private static ?AuthLogger $instance = null;

    private function __construct(CustomLoggerInterface $implementation)
    {
        $this->loglevelControl = new LogLevelControl();
        $this->implementation = $implementation;
    }

    public function info($message)
    {
        $this->loglevelControl->isAllowed('info') && $this->implementation->info($message);
    }

    public function error($message)
    {
        $this->loglevelControl->isAllowed('error') && $this->implementation->error($message);
    }

    public static function instance(CustomLoggerInterface $implementation): AuthLogger
    {
        if (self::$instance === null) {
            self::$instance = new self($implementation);
        }

        return self::$instance;
    }
}
