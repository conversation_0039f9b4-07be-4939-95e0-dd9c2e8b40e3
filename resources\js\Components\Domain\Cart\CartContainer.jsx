import React, { useState, useEffect } from "react";
import Checkbox from "@/Components/Checkbox";
import { MdKeyboardBackspace } from "react-icons/md";
import CartItems from "@/Components/Domain/Cart/CartItems";
import CartCounterState from "@/State/CartCounterState";
import PrimaryLink from "@/Components/PrimaryLink";
import { useForm, usePage, Link } from "@inertiajs/react";
import { getEventValue } from "@/Util/TargetInputEvent";
import { partial } from "lodash";


export default function CartContainer({ data, domainsList, noPrimaryContacts }) {
    const annualPrice = 8; //temporary

    const user = usePage().props.auth.user;
    const [isPrivacyAll, setIsPrivacyAll] = useState(false)
    const [isAutoRenewAll, setIsAutoRenewAll] = useState(false)
    const [partialTotal, setPartialTotal] = useState(0)

    const [selectedPrivacy, setSelectedPrivacy] = useState([]);
    const [selectedAutoRenew, setSelectedAutoRenew] = useState([]);
    const { setCartCounter } = CartCounterState();

    const { data: cart,
        setData,
        get,
        patch,
        post,
        delete: destroy,
        processing,
        errors,
        reset } = useForm({
            id: "",
            user_id: user.id,
            tld_id: "",
            name: "",
            year_length: "",
            privacy: false,
            auto_renew: false,
            column: "",
            value: ""
        });

    useEffect(() => {
        onPartialTotal(data);
        getSelectedPrivacy(data);
        getSelectedAutoRenew(data);
        getCartCount();
    }, [data]);

    const getCartCount = async () => {
        await axios
            .get(route("mycart.count"))
            .then((response) => {
                setCartCounter(response.data);
                // console.log(response);
            })
            .catch((error) => {
                // console.log(error.response.data);
            });
    };

    const getSelectedPrivacy = (domains) => {
        if (domains != null) {
            let temp = [];
            domains.map((domain, index) => {
                if (domain.privacy) {
                    temp = temp.filter((e) => e != domain.id);
                    temp.push(domain.id);
                }
            });

            setSelectedPrivacy(temp);
        }
    }

    const getSelectedAutoRenew = (domains) => {
        if (domains != null) {
            let temp = [];
            domains.map((domain, index) => {
                if (domain.auto_renew) {
                    temp = temp.filter((e) => e != domain.id);
                    temp.push(domain.id);
                }
            });

            setSelectedAutoRenew(temp);
        }
    }

    const createUpdateArray = (selectedIds, column, value) => {
        cart.id = selectedIds;
        cart.column = column;
        cart.value = value;
        patch(route("mycart.update"), { preserveScroll: true });
    };

    const onHandleSelectAllItem = (event) => {
        // console.log(event.target.name);
        // console.log(getEventValue(event));

        if (event.target.name == 'privacy') {
            (getEventValue(event)) ? setSelectedPrivacy(domainsList) : setSelectedPrivacy([]);
            setIsPrivacyAll(getEventValue(event));
            cart.privacy = getEventValue(event);
        }

        if (event.target.name == 'auto_renew') {
            (getEventValue(event)) ? setSelectedAutoRenew(domainsList) : setSelectedAutoRenew([]);
            setIsAutoRenewAll(getEventValue(event));
            cart.auto_renew = getEventValue(event);
        }

        createUpdateArray(domainsList, event.target.name, getEventValue(event));
    }

    // included ids for checkbox
    const changeSelected = (domains, value, event) => {
        let temp = [...domains];

        if (temp.includes(value)) {
            temp = temp.filter((e) => e != value);
        } else {
            temp.push(value);
        }

        if (event == 'privacy') {
            setSelectedPrivacy(temp);
        }

        if (event == 'auto_renew') {
            setSelectedAutoRenew(temp);
        }
    };

    const onHandleChangeItem = (domain, column, value) => {
        cart.id = domain.id;
        cart[column] = value;
        cart.column = column;
        cart.value = value;
        patch(route("mycart.update"), { preserveScroll: true });

        // check partial total
        onPartialTotal(data);

        // change state of checkbox
        if (column == 'privacy') {
            changeSelected(selectedPrivacy, domain.id, column);
        }

        if (column == 'auto_renew') {
            changeSelected(selectedAutoRenew, domain.id, column);
        }

    }

    const onDeleteItem = (id) => {
        if (confirm("Are you sure you want to delete this domain from cart?")) {
            cart.id = id;
            destroy(route("mycart.delete"), { preserveScroll: true });

            getCartCount();
        }
    }

    const onPartialTotal = (data) => {
        var total = 0;
        data.map((e, index) => {
            total += e.year_length * annualPrice;
        })

        setPartialTotal(total);
    }

    return (
        <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
            <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                <Link
                    href={route("domain.search")}
                >
                    <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                </Link>
                <span className="text-inherit">
                    My Cart
                </span>
                <span className="text-gray-500">{data.length} items</span>
            </div>
            <div className="flex space-x-5">
                <label className="text-gray-700 text-sm">Group toggle</label>
                <label className="flex items-center">
                    <Checkbox
                        name="privacy"
                        value="privacy"
                        checked={isPrivacyAll}
                        handleChange={onHandleSelectAllItem}
                    />
                    <span className="ml-2 text-sm text-gray-600">
                        Privacy protection
                    </span>
                </label>
                <label className="flex items-center">
                    <Checkbox
                        name="auto_renew"
                        value="auto_renew"
                        checked={isAutoRenewAll}
                        handleChange={onHandleSelectAllItem}
                    />
                    <span className="ml-2 text-sm text-gray-600">
                        Auto Renew
                    </span>
                </label>
            </div>
            <div className="flex flex-col space-y-8 pt-8">
                {noPrimaryContacts.length > 0 && (
                    <div className="flex flex-col">
                        <span className="text-danger">
                        * Missing primary contact for domain registration
                        </span>
                        <ul>
                            {noPrimaryContacts.map(item => (
                                <li key={item.domain}>
                                <span>
                                    <span className="font-semibold pr-1 uppercase">
                                    {item.registry}
                                    </span>
                                    primary contact is needed for
                                    <span className="font-semibold pl-1 uppercase">
                                    {`"${item.domain}" `}
                                    </span>
                                    extension
                                </span>
                                </li>
                            ))}
                        </ul>
                        <Link
                            href={route("contact")}
                            className="underline text-sm text-gray-600 hover:text-gray-900 focus:outline-none mt-4 w-fit"
                        >
                            Go to Contacts
                        </Link>
                    </div>
                )}


                {data.map((e, index) => {
                    return (
                        <CartItems
                            key={"cl-" + e.id}
                            domain={e}
                            index={index}
                            handleChangeItem={onHandleChangeItem}
                            onDeleteItem={onDeleteItem}
                            selectedPrivacy={selectedPrivacy.includes(e.id) || e.privacy}
                            selectedAutoRenew={selectedAutoRenew.includes(e.id) || e.auto_renew}
                        />
                    );
                })}
                <div className="flex items-center justify-between space-y-2 text-lg text-gray-700 font-semibold">
                    <span className=" text-inherit">Partial Total</span>
                    <span className=" text-inherit">{partialTotal}</span>
                </div>
                {noPrimaryContacts.length == 0  && (
                    <div className="flex items-center justify-end flex-col space-y-4">
                        <PrimaryLink
                            href={route("domain.checkout")}
                            className="w-full"
                        >
                            Checkout
                        </PrimaryLink>
                        <span className="text-sm text-gray-600 text-center">
                            By clicking "Check out" you confirm that you have
                            read the StrangeDomain refund policy.
                        </span>
                    </div>
                )}
            </div>
        </div>
    );
}
