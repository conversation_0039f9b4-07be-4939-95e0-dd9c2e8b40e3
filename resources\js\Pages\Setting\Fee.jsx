import FeeItem from "@/Components/Setting/FeeItem";
import AdminLayout from "@/Layouts/AdminLayout";

export default function Fee({ fees }) {
    // console.log(fees)
    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-10 px-8">
                <div className="flex flex-wrap space-y-2 md:divide-x-2 justify-between">
                    <div className="flex-none md:w-1/2 px-2">
                        <span className="text-lg text-gray-700">Base Fees</span>
                    </div>
                    <div className="flex flex-col space-y-2 md:pl-8 flex-auto">
                        {Object.keys(fees).map((key) => {
                            if (!key.startsWith("PENALTY")) return <FeeItem key={"fi-" + key} item={fees[key]} />;
                            return null;
                        })}
                    </div>
                </div>
                <div className="flex flex-wrap space-y-2 md:divide-x-2 justify-between">
                    <div className="flex-none md:w-1/2 px-2">
                        <span className="text-lg text-gray-700">Penalties</span>
                    </div>
                    <div className="flex flex-col space-y-2 md:pl-8 flex-auto">
                        {Object.keys(fees).map((key) => {
                            if (key.startsWith("PENALTY")) return <FeeItem key={"fi-" + key} item={fees[key]} />;
                            return null;
                        })}

                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
