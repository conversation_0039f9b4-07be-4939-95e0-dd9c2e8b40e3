<?php

namespace App\Modules\DomainRedemption\Requests;

use App\Modules\DomainRedemption\Services\DatabaseQueryService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => [
                'string',
                Rule::in([
                    "domain:desc",
                    "domain:asc",
                    "dateDeleted:desc",
                    "dateDeleted:asc",
                ])
            ],
            'user' => ['string, exists:users,email'],
            'domain' => ['string', 'max:255'],
            'email' => ['string', 'max:255'],
            'deletedBy' => ['string', 'max:255'],
            'limit' => ['integer', 'min:1', 'max:100']
        ];
    }

    public function show()
    {
        return DatabaseQueryService::instance()->get($this);
    }
}
