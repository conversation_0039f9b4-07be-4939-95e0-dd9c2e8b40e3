<?php

namespace App\Util\Helper;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class HttpResponse
{
    public static function filled(Response $response, array $default = []): array
    {

        if ($response->status() == 404) {
            return $default;
        }

        return self::validate($response);

    }

    public static function validate(Response $response, array $addon = []): array
    {
        $hasError = self::getError($response);

        count($hasError) && throw ValidationException::withMessages(array_merge($hasError, $addon));

        return array_merge($response->json(), $addon);
    }

    public static function getError(Response $response)
    {
        if ($response->clientError()) {
            $errorBag = $response->json();

            return Arr::has($errorBag, ['type', 'errors']) && strcmp($errorBag['type'], 'ConstraintViolationException') == 0
                ? array_merge($errorBag['errors'], $errorBag['message'])
                : ['message' => $errorBag['message']];
        }

        return [];
    }
}
