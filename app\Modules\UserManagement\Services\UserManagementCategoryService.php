<?php

namespace App\Modules\UserManagement\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\UserManagement\Services\UserManagementPermissionService; 
use App\Traits\CursorPaginate;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Query\Builder;

use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class UserManagementCategoryService
{
    use CursorPaginate;

    /**
     * Fetch Items 
     * 
     * @param array $data
     */
    public function fetchItems(array $data)
    {
        $allItems = DB::table('category')->pluck('id')->toArray();

        $builder = DB::table('category')
            ->join('access_category', 'access_category.category_id', '=', 'category.id')
            ->join('admins', 'category.admin_id', "=", 'admins.id');

        $builder = $builder->when(
            isset($data['category']), 
            function (Builder $query) use ($data) 
            {
                $query->where('category.name', 'ilike', $data['category'] . '%');
            }
        );

        $builder = $builder->groupBy('category.id', 'admins.name');

        $builder = $builder->when(
            isset($data['orderBy']), 
            function (Builder $query) use ($data) 
                {
                    $orderby = explode(':', $data['orderBy']);

                    switch ($orderby[0]) 
                    {
                        case 'category':
                            $query->orderBy('category.name', $orderby[1]);
                            break;
                        case 'createdBy':
                            $query->orderBy('admins.name', $orderby[1]);
                            break;
                        case 'lastUpdated':
                            $query->orderBy('category.updated_at', $orderby[1]);
                            break;
                        case 'permissions':                            
                            $query->orderBy(DB::raw('COUNT(access_category.category_id)'), $orderby[1]); 
                            break;
                    }
                }
            )
            ->when(
            !isset($data['category']),
            function (Builder $query) use ($data) 
                {
                    $query->orderBy('category.created_at', 'desc');
                }
            );

        $builder = $builder->select(
                'category.id', 
                'category.name as category', 
                'category.updated_at as lastUpdated', 
                DB::raw('COUNT(access_category.category_id) AS access'), 
                'admins.name AS createdBy'
            )
            ->paginate($data['showItems'] ?? 10 )
            ->withQueryString();

        return CursorPaginate::cursor(
            $builder, 
            $this->paramToURI($data),
            compact('allItems')
        );
    }

    private static function paramToURI($data)
    {
        $param = [];

        if (isset($data['category'])) 
        {
            $param[] = 'category=' . $data['category'];
        }

        if (isset($data['orderBy'])) 
        {
            $param[] = 'orderBy=' . $data['orderBy'];
        }

        return $param;
    }
    /**
     * Fetch Item
     * 
     * @param int $id
     */
    public function fetchItem($id)
    {
        $permissions = (new UserManagementPermissionService())->fetchItems();

        $assignedPermissions = DB::table('access_category AS ac')
            ->join('access AS a', 'ac.access_id', 'a.id')
            ->select('a.name', 'a.id')
            ->where('ac.category_id', $id)
            ->get();

        $category = DB::table('category')
            ->where('id', $id)
            ->firstOrFail();
    
        return [
            'item'                => $category,
            'permissions'         => $permissions,
            'assignedPermissions' => $assignedPermissions,
        ]; 
    }

    /**
     * Fetch Item Permissions 
     * 
     * @param int $id
     */
    public function fetchItemPermissions(int $id)
    {
        $category = DB::table('category')
            ->where('id', $id)
            ->first();

        $permissions =  DB::table('access_category AS ac')
            ->join('access as a', 'ac.access_id', 'a.id')
            ->select('a.name')
            ->where('category_id', $id)
            ->get();

        return [
            'name'        => $category->name,
            'permissions' => $permissions
        ];
    }

    /**
     * Create Item
     * 
     * @param array $data 
     */
    public function createItem(array $data)
    {
        $permissions = [];
        $now         = now();
        $userId      = Auth::user()->id;
        $id          = DB::table('category')->insertGetId(['name' => $data['name'], 'admin_id' => $userId, 'created_at' => $now, 'updated_at' => $now]);

        foreach ($data['permissions'] as $permission) 
        {
            $permissions[] = ['access_id' => $permission, 'category_id' => $id, 'created_at' => $now, 'updated_at' => $now];
        }

        DB::table('access_category')->insert($permissions);

        app(AuthLogger::class)->info("category {$data['name']} with ID Number {$id} created");

        $permissionCount = count($data['permissions'] ?? []);
        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::USER_MANAGEMENT,
            "Category created: {$data['name']} with ID {$id} by " . auth()->user()->email . " - Permissions: {$permissionCount}"
        ));
    }

    /**
     * Update Item
     * 
     * @param array $data
     * @param int   $id 
     */
    public function updateItem(array $data, int $id)
    {
        $permissions = [];
        $now         = now();

        DB::table('access_category')->where('category_id', $id)->delete();
        DB::table('category')->where('id', $id)->update(['name' => $data['name'], 'updated_at' => $now]);

        foreach ($data['permissions'] as $permission) 
        {
            $permissions[] = ['access_id' => $permission, 'category_id' => $id, 'created_at' => $now, 'updated_at' => $now];
        }

        DB::table('access_category')->insert($permissions);

        app(AuthLogger::class)->info("category {$data['name']} with ID Number {$id} updated");

        $permissionCount = count($data['permissions'] ?? []);
        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::USER_MANAGEMENT,
            "Category updated: {$data['name']} with ID {$id} by " . auth()->user()->email . " - Permissions: {$permissionCount}"
        ));
    }

    /**
     * Delete Item
     * 
     * @param int $id
     */
    public function deleteItem(int $id)
    {
        $category = DB::table('category')
            ->where('id', '=', $id)
            ->firstOrFail();

        DB::table('category')
            ->where('id', '=', $id)
            ->delete();

        app(AuthLogger::class)->info("category {$category->name} with ID Number {$category->id} deleted");

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::USER_MANAGEMENT,
            "Category deleted: {$category->name} with ID {$category->id} by " . auth()->user()->email
        ));
    }

    /**
     * Bulk Delete Items
     *
     * @param array $data
     */
    public function bulkDeleteItems(array $data)
    {
        $categoryNames = [];

        foreach ($data['categories'] as $categoryId)
        {
            $category = DB::table('category')->where('id', $categoryId)->firstOrFail();
            $categoryNames[] = $category->name;

            DB::table('access_category')
                ->where('category_id', $category->id)
                ->delete();

            DB::table('category')
                ->where('id', '=', $category->id)
                ->delete();

            app(AuthLogger::class)->info("category {$category->name} with ID Number {$category->id} deleted");
        }

        $categoryCount = count($data['categories']);
        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::USER_MANAGEMENT,
            "Categories deleted: {$categoryCount} categories (" . implode(', ', $categoryNames) . ") by " . auth()->user()->email
        ));
    }

    /**
     * Fetch Categories 
     */
    public function fetchCategories()
    {
        return DB::table('category')
            ->select('id', 'name')
            ->get();
    }
}
