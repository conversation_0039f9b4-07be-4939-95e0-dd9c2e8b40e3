import { usePage } from '@inertiajs/react';
import React, { useState } from 'react'
import { useEffect } from 'react';
import { toast } from 'react-toastify';

export default function OfferHistoryPopup({modal, showModal, domain, getStatus, offers, setOffers}) {

    const options = usePage().props.options;

    const [status, setStatus] = useState();
    const [history, setHistory] = useState([]);
    const [feedback, setFeedback] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [counterOffer, setCounterOffer] = useState(0);
    
    const submit = () => {
        setIsLoading(true);

        axios.post(route('offers.update'), { 
            id: domain.id,
            status: status,
            feedback: feedback,
            offer: domain.offer_price,
            counter_offer: parseInt(counterOffer)
        }).finally(() => {
            setFeedback("");
            setCounterOffer(0);

            showModal(false);
            setIsLoading(false);
            toast.success('Success')

            setOffers(offers.map((item) => item.id === domain.id ? { ...item, offer_status: status } : item));
        })
    }

    const getPrice = (domain) => {
        if(domain.offer_status == 'counter_offer') return domain.counter_offer_price
        else return domain.offer_price
    }

    useEffect(() => {
        if(modal) {
            axios.post(route('offers.history'), { id: domain.id })
            .then((data) => {
                setHistory(data.data)
            })
        } else {
            setHistory([])
        }
    }, [modal])

    useEffect(() => {
        if(modal) {
            setFeedback(" ");
            setCounterOffer(domain.counter_offer_price);
            setStatus(domain.offer_status);
        }
    }, [modal])

    useEffect(() => {
        return setCounterOffer(domain.counter_offer_price)
    }, [status])
    
    return (
        <div className={` ${modal ? '' : 'hidden'} fixed z-10 overflow-y-auto top-0 w-full left-0`} id="modal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
                    <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                    <div className="inline-block px-4 align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                        <div className="bg-white pb-4 sm:pt-6 sm:pb-4">
                            <label className="text-lg text-gray-800">Offer History - <span className="text-primary font-bold">{domain.domain_name}</span></label>
                                {
                                    history ? <div className='flex flex-col'>
                                        <div className='grid grid-cols-5 pt-8 capitalize'>
                                            <span>Date</span>
                                            <span className='-ml-14'>Status</span>
                                            <span className='-ml-20'>Initial Price</span>
                                            <span className='-ml-36'>Buy Now Price</span>
                                            <span className='-ml-28'>Feedback</span>
                                        </div>
                                        <div className='max-h-[150px] border-y py-2 my-2 h-full overflow-y-scroll overflow-x-hidden'>
                                            {
                                                history.map((a) => {
                                                    return <div key={a.id} className='grid grid-cols-5 pb-4 capitalize'>
                                                        <span>{(new Date(a.created_at).toLocaleString()).split(',')[0]}</span>
                                                        <span className='-ml-14'>{getStatus(a.offer_status)}</span>
                                                        <span className='-ml-20'>${a.offer_price}</span>
                                                        <span className='-ml-36'>${a.counter_offer_price}</span>
                                                        <span className='-ml-28'>{a.feedback ? a.feedback : 'NA'}</span>
                                                    </div>
                                                })
                                            }
                                        </div>
                                    </div> : <></>
                                }
                        </div>
                        <div className='flex flex-col w-full'>
                            <label className="w-full text-gray-800">Update Status:</label>
                            <div className="flex items-center w-full pt-1">
                                <select value={status} onChange={(e) => {setStatus(e.target.value)} } className='rounded-md capitalize w-full focus:ring-0 border-gray-300 focus:border-gray-500' name="" id="">
                                    {
                                        options.map((a, i) => {
                                            return <option value={a} key={i}>{a.replaceAll('_', ' ')}</option>
                                        })
                                    }
                                </select>
                            </div>
                            {
                                status == 'counter_offer' ? <>
                                    <label className="w-full text-gray-800 pt-3">Counter Offer Price:</label>
                                    <div className="flex items-center w-full pt-1">
                                        <input value={counterOffer} onChange={(e) => { setCounterOffer(e.target.value) }} className='rounded-md w-full focus:ring-0 border-gray-300 focus:border-gray-500' type="number" min="100" step="1" />
                                    </div>
                                </> : <></>
                            }
                            <label className="w-full text-gray-800 pt-3">Send Feedback</label>
                            <div className="flex items-center w-full pt-1">
                                <textarea value={feedback} onChange={(e) => { setFeedback(e.target.value) }} className="rounded-md w-full focus:ring-0 border-gray-300 focus:border-gray-500 resize-none" rows={2}></textarea>
                            </div>
                        </div>
                        <div className="py-4 text-right">
                            <button type="button" disabled={isLoading} onClick={() => { showModal(false); }} className="cursor-pointer py-2 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-700 mr-2" ><i className="fas fa-times"></i> Close </button>
                            <button type="button" disabled={isLoading} onClick={() => { submit() }} className="cursor-pointer disabled:bg-gray-500 py-2 px-4 bg-primary text-white rounded-md font-medium hover:bg-blue-500 transition duration-500"><i className="fas fa-plus"></i> Update </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
/**

*/