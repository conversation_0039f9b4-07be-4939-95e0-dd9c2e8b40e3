import React from "react";
import { Link } from "@inertiajs/react";
import AdminLayout from "@/Layouts/AdminLayout";

export default function ConfirmationMessage({
    message,
    message2,
    postRouteName = "domain",
    redirect = [],
    setup = null,
}) {
    return (
        <AdminLayout postRouteName={postRouteName} setup={setup} >
            <div>
                <div className="mx-auto container max-w-[900px] text-center pt-14 flex justify-center items-center flex-wrap">
                    <div className="w-auto h-[10rem]">
                        <img
                            className="h-full w-auto"
                            src="/assets/images/order_confirmed.svg"
                            alt="background"
                        />
                    </div>

                    <div className="flex flex-col items-start pl-4">
                        <span className=" text-xl font-semibold">{message}</span>
                        {message2 && <span className=" text-l">{message2}</span>}
                    </div>
                </div>
                <div className="w-full text-center flex flex-col items-center pt-12 space-y-4">
                    {redirect.map((item, index) => (
                        <Link
                            key={index}
                            href={item.route}
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none"
                        >
                            {item.label}
                        </Link>
                    ))}
                </div>
            </div>
        </AdminLayout>
    );
}