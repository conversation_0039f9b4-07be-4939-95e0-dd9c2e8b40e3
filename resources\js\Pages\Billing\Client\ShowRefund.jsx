
import convertToTitleCase from "@/Util/convertToTitleCase";
import { MdKeyboardBackspace } from "react-icons/md";
import { Link } from "@inertiajs/react";
import RefundItem from "../../../Components/Billing/Client/RefundItem";
import AdminLayout from "../../../Layouts/AdminLayout";
import EmptyList from "../../../Components/Billing/EmptyList";

export default function ShowRefund({ item }) {
    // console.log(item);

    const STATUS_SUCCESS = 'Succeeded';
    const STATUS_REFUNDED = 'Refunded';

    // console.log(item);

    const setDate = date => {
        return new Date(date + 'Z').toDateString() + ' ' + new Date(date + 'Z').toLocaleTimeString();
    }

    if (!item || item.length == 0) {
        return (
            <AdminLayout hideNav={true}>
                <div className="mx-auto container max-w-[900px] mt-20 flex flex-col">
                    <div className="flex flex-col space-y-12 divide-y pb-10">
                        <div className=" space-y-2">
                            <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                                <a href="#" onClick={() => window.history.back()}>
                                    <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                                </a>
                                <span className="font-semibold">
                                    Back
                                </span>
                            </div>
                            <EmptyList
                                message={"You don't have any transactions."} />
                        </div>
                    </div>
                </div>
            </AdminLayout>
        );
    }

    return (
        <AdminLayout hideNav={true}>
            <div className="mx-auto container max-w-[900px] mt-20 flex flex-col">
                <div className="flex flex-col space-y-12 divide-y pb-5">
                    <div className=" space-y-2">
                        <div className="flex items-center space-x-4 text-gray-700 text-md font-semibold">
                            <a href="#" onClick={() => window.history.back()}>
                                <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                            </a>
                            <span className="font-semibold">
                                Payment Details
                            </span>
                            <span className="text-gray-500">1 item</span>
                        </div>
                    </div>
                </div>
                <span>{setDate(item.reimbursement.created_at)}</span>
                <div className="flex flex-col space-y-8 pt-8">
                    <RefundItem
                        key={"pi-" + item.reimbursement.id}
                        index={item.reimbursement.id}
                        item={item.reimbursement}
                        status={item.status}
                    />
                </div>
                <div className="flex items-center justify-between space-y-8 pt-8 text-gray-600 border-b border-gray-200 pb-2 mb-4 ">
                </div>
                <div className="flex flex-col text-lg text-gray-700 font-semibold">
                    <div className="flex items-center justify-between">
                        <span className=" text-inherit">Current Balance</span>
                        <span className=" text-inherit">${item.reimbursement.current_balance}</span>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className=" text-inherit">Refund</span>
                        <span className=" text-inherit">${item.reimbursement.total_amount}</span>
                    </div>
                    <div className="flex items-center justify-between">
                        <span className=" text-inherit">Total Balance</span>
                        <span className=" text-inherit">${parseFloat(item.reimbursement.current_balance) - parseFloat(item.reimbursement.total_amount)}</span>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
