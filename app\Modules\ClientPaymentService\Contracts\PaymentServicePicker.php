<?php

namespace App\Modules\ClientPaymentService\Contracts;

use App\Modules\ClientPaymentService\Constants\PaymentServiceType;
use App\Modules\ClientPaymentService\Services\Interfaces\AccountCreditPaymentService;
use App\Modules\ClientPaymentService\Services\Interfaces\AccountDebitPaymentService;
use App\Modules\ClientPaymentService\Services\Interfaces\BankTransferPaymentService;
use App\Modules\ClientPaymentService\Services\Interfaces\StripePaymentService;
use App\Modules\ClientPaymentService\Services\Interfaces\SystemCreditPaymentService;
use Exception;

class PaymentServicePicker implements PaymentServicePickerInterface
{
    public function getType(string $type)
    {
        switch ($type) {
            case PaymentServiceType::STRIPE:
                return new StripePaymentService;
            case PaymentServiceType::BANK_TRANSFER:
                return new BankTransferPaymentService;
            case PaymentServiceType::SYSTEM_CREDIT:
                return new SystemCreditPaymentService;
            case PaymentServiceType::ACCOUNT_CREDIT: // checkout - pay with account credit
                return new AccountCreditPaymentService;
            case PaymentServiceType::ACCOUNT_DEBIT: // add to account balance
                return new AccountDebitPaymentService;
            default:
                throw new Exception('Payment not supported.');
        }
    }
}
