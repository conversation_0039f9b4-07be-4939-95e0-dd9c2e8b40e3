import { MdAppRegistration, MdAssignmentReturn } from "react-icons/md";

const STATUS_REFUND = 'REFUNDED';
const STATUS_REFUND_REQUESTED = 'Refund Requested';

const convertNameFormat = (str) => {
    str = str.toLowerCase();
    const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
    return words.join(' ');
}

export default function RefundItem({ item, status }) {
    return (
        <div className={`" space-y-2" }`}>
            <div className="flex items-center justify-between text-gray-600 border-b border-gray-200 pb-2 mb-4">
                <label className=" text-2xl">{item.name}</label>
                <span>
                    $ {item.total_amount}
                </span>
            </div>
            <div
                className="flex items-center justify-between hover:bg-gray-50 relative"
            >
                <div className="flex items-center">
                    <MdAppRegistration className=" text-lg" />
                    <span className="ml-2 text-md text-gray-600">
                        {/* {item.node_type} */}
                        {(item.node_type) && convertNameFormat(item.node_type)}
                    </span>
                    <span className="ml-2 text-md text-gray-600">
                        {status && ((status === STATUS_REFUND) ? STATUS_REFUND_REQUESTED : convertNameFormat(status))}
                    </span>
                </div>
                {(status == 'FAILED') &&
                    <div className="flex items-center">
                        <MdAssignmentReturn className="text-lg text-danger" />
                        <span className="ml-2 text-md text-danger">
                            Reimbursement {item.reimbursement_status && convertNameFormat(item.reimbursement_status)}
                        </span>
                    </div>
                }
            </div>
        </div>
    );
}
