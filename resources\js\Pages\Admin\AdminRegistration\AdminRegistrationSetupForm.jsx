//* PACKAGES
import React, {useState, useEffect} from 'react'
import { toast } from 'react-toastify';
import axios from 'axios';
import { Head, Link, useForm, router } from "@inertiajs/react";

//* ICONS
import { AiFillEyeInvisible, AiO<PERSON>lineEye } from "react-icons/ai";

//* COMPONENTS
import AppPasswordStrengthIndicatorComponent from '@/Components/App/AppPasswordStrengthIndicatorComponent';
import GuestLayout from "@/Layouts/GuestLayout";
import PrimaryButton from "@/Components/PrimaryButton";
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AdminRegistrationSetupForm(props)
{
    //! PACKAGE
    const form = useForm(
        {
            email                : props.data.email,
            name                 : props.data.name,
            password             : "",
            password_confirmation: "",
        }
    );
    
    //! VARIABLES
    //...

    //! STATES
    const [showInputPassword, setShowInputPassword]               = useState(false);
    const [showInputConfirmPassword, setShowInputConfirmPassword] = useState(false);

    //! FUNCTIONS
    function handleSubmit(e)
    {
        e.preventDefault();

        form.clearErrors(); 

        form.post(
            route('admin-registration.register', { token: props.data.token }),
        );
    }

    return (
        <GuestLayout
            widthsm={'sm:max-w-lg'}
        >
            <Head title="Admin Account Setup" />

            <form
                className='flex flex-col gap-8 mt-5'
                onSubmit={handleSubmit}
            >
                <section
                    className="block text-center"
                >
                    <label className="text-gray-700 text-2xl">Admin Account Setup</label>
                </section>

                <section
                    className='flex flex-col gap-5'
                >
                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="email"
                            value="Email"
                        />
                        
                        <TextInput
                            type="text"
                            name="email"
                            value={form.data.email}
                            placeholder="<EMAIL>"
                            className="w-full"
                            disabled={true}
                            handleChange={(e) => form.setData('email', e.target.value)}
                        />

                        <InputError message={form.errors.email} />
                    </div>
                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="name"
                            value="Name"
                        />
                        
                        <TextInput
                            type="text"
                            name="name"
                            value={form.data.name}
                            placeholder="John Doe"
                            className="w-full"
                            disabled={false}
                            handleChange={(e) => form.setData('name', e.target.value)}
                        />

                        <InputError message={form.errors.name} />
                    </div>
                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="password"
                            value="Password"
                        />
                        
                        <div
                            className='relative'
                        >
                            <TextInput
                                type={showInputPassword ? "text" : "password"}
                                name="password"
                                value={form.data.password}
                                placeholder="P@ssword25"
                                className="w-full"
                                handleChange={(e) => form.setData('password', e.target.value)}
                            />
                            <div
                                className="absolute right-0 top-1/2 -translate-y-1/2 pr-5"
                            >
                                <i
                                    onClick={() => setShowInputPassword(!showInputPassword)}
                                    className="text-xl cursor-pointer hover:text-primary duration-100 ease-in"
                                >
                                    {showInputPassword ? (
                                        <AiFillEyeInvisible />
                                    ) : (
                                        <AiOutlineEye />
                                    )}
                                </i>
                            </div>
                        </div>

                        {
                            form.errors.password != null
                                ?
                                    <InputError message={form.errors.password} />
                                :
                                (
                                    <span className='text-xs text-gray-400 [&::first-letter]:uppercase'>
                                        Password must contain at least 8 characters, including 1 digit, 1 uppercase letter, 1 lowercase letter & 1 special character.
                                    </span>                                               
                                )
                        }
                        {
                            form.data.password.length > 0 
                                ?
                                    <AppPasswordStrengthIndicatorComponent
                                        password={form.data.password}
                                    />
                                :
                                    null 
                        }
                    </div>
                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="confirmPassword"
                            value="Confirm Password"
                        />
                        <div
                            className='relative'
                        >
                            <TextInput
                                type={showInputConfirmPassword ? "text" : "password"}
                                name="confirm_password"
                                value={form.data.password_confirmation}
                                placeholder="P@ssword25"
                                className="w-full"
                                handleChange={(e) => form.setData('password_confirmation', e.target.value)}
                            />
                            <div
                                className="absolute right-0 top-1/2 -translate-y-1/2 pr-5"
                            >
                                <i
                                    onClick={() => setShowInputConfirmPassword(!showInputConfirmPassword)}
                                    className="text-xl cursor-pointer hover:text-primary duration-100 ease-in"
                                >
                                    {showInputConfirmPassword ? (
                                        <AiFillEyeInvisible />
                                    ) : (
                                        <AiOutlineEye />
                                    )}
                                </i>
                            </div>
                        </div>
                    </div>
                </section>

                <section>
                    <PrimaryButton
                        className="w-full"
                        processing={form.processing}
                    >
                        Register
                    </PrimaryButton>
                </section>
            </form>
        </GuestLayout>
    );
}
 