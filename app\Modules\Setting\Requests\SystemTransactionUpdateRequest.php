<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Constants\TransactionSettings;
use App\Modules\Setting\Services\TransactionThresholdService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;

class SystemTransactionUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'transaction_id' => ['required', 'integer'],
            'name' => ['required', 'string'],
            'system_limit' => ['required', 'integer'],
            'user_limit' => ['required', 'integer'],
            'length' => ['required', 'integer'],
            'notify_subscriber' => ['required', 'boolean'],
            'allow_action' => ['required', 'boolean'],
        ];
    }

    public function update()
    {
        $data = TransactionThresholdService::instance()->getUpdatePayload($this->transaction_id);
        $updatePayload = array_diff_assoc($this->all(), $data);
        $transactionPayload = Arr::only($updatePayload, TransactionSettings::TRANSACTION_FIELDS);
        $triggerPayload = Arr::only($updatePayload, TransactionSettings::TRIGGER_FIELDS);

        TransactionThresholdService::instance()->updateTransactionSettings(
            $this->transaction_id,
            $transactionPayload,
            $triggerPayload
        );
    }
}
