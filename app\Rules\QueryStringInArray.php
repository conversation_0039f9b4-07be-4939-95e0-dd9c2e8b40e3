<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class QueryStringInArray implements ValidationRule
{
    protected $values;

    public function __construct($values)
    {
        $this->values = explode(',', $values);
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $queryValues = explode(',', $value);
        $result = array_diff($queryValues, $this->values);
        if (count($result) > 0) {
            $fail('Invalid query.');
        }
    }
}
