<?php

namespace App\Modules\Domain\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Helper\DomainParser;
use Illuminate\Support\Facades\DB;
use App\Modules\Domain\Services\ClientHoldPayload;
use App\Modules\Client\Constants\DomainStatus;

class DomainEppClientHoldService
{
    private static ?self $instance = null;

    private function __construct()
    {
       
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function updateEppStatus(int $domainId, string $domainName, string $action): void
    {
        try {
            $domain = DB::client()->table('domains')
                ->where('id', $domainId)
                ->first();

            if (!$domain) {
                app(AuthLogger::class)->error("Domain not found: {$domainName} (ID: {$domainId})");
                return;
            }

            $eppInfo = EppDomainService::instance()->callEppDomainInfo($domainName);
            if ($eppInfo['status'] === 'error') {
                app(AuthLogger::class)->error("Failed to get EPP info for domain {$domainName}: " . ($eppInfo['message'] ?? 'Unknown error'));
                return;
            }

            try {
                $payload = ClientHoldPayload::instance()
                    ->initialize($domain, $eppInfo, $action)
                    ->get();

                $updateResult = EppDomainService::instance()->updateEppDomain($payload);
                if ($updateResult['status'] === 'error') {
                    app(AuthLogger::class)->error("Failed to update EPP for domain {$domainName}: " . ($updateResult['message'] ?? 'Unknown error'));
                    NotificationService::sendFailedClientHoldNotif($domainName);
                    return;
                }

                DomainClientHoldServices::instance()->updateDomainStatus($domain, DomainStatus::ACTIVE);
                $this->updateLocalStatus($domain, $action === 'client_hold');
                app(AuthLogger::class)->info("Successfully updated domain {$domainName}");

            } catch (\Exception $e) {
                app(AuthLogger::class)->error("Failed to update EPP for domain {$domainName}: " . $e->getMessage());
                NotificationService::sendFailedClientHoldNotif($domainName);
            }

        } catch (\Exception $e) {
            app(AuthLogger::class)->error("Failed to process domain {$domainName}: " . $e->getMessage());
        }
    }

    private function updateLocalStatus(object $domain, bool $isAdding): void
    {
        try {
            $currentStatus = json_decode($domain->client_status ?? '[]', true);
            
            if ($isAdding) {
                $currentStatus[] = EppDomainStatus::CLIENT_HOLD;
                $currentStatus = array_unique($currentStatus);
            } else {
                $currentStatus = array_values(array_filter($currentStatus, function($status) {
                    return $status !== EppDomainStatus::CLIENT_HOLD;
                }));
            }

            DB::client()->table('domains')
                ->where('id', $domain->id)
                ->update([
                    'client_status' => json_encode(array_values($currentStatus))
                ]);
        } catch (\Exception $e) {
            app(AuthLogger::class)->error("Failed to update local status for domain ID {$domain->id}: " . $e->getMessage());
            throw $e;
        }
    }
 
} 