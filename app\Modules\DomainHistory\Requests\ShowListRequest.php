<?php

namespace App\Modules\DomainHistory\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    protected $redirect = 'bad-request';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return $this->rulesOnFilter();
    }

    private function rulesOnFilter(): array
    {
        $path = $this->getPathInfo();

        if (str_contains($path, 'domain-history')) {
            return [
                'type' => ['string', 'nullable'],
                'email' => ['string', 'nullable'],
                'date' => ['string', 'nullable'],
                'search' => ['string', 'nullable', 'max:255'],
            ];
        }

        return [];
    }

    /**
     * Fetch domain history data.
     *
     * @return mixed
     */
    
    public function getFilters(): array
    {
        return $this->only(['email', 'type', 'date', 'search']);
    }

    // public function getCursor()
    // {
    //     return $this->input('cursor');
    // }

    public function validateSearch($search)
    {
        return !is_null($search) ? $search : null;
    }
}
