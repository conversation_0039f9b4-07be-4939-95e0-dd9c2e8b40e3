<?php

namespace App\Modules\BillingClient\Services\Interfaces;

use App\Exceptions\FailedRequestException;
use App\Modules\BillingClient\Contracts\PaymentSummaryInterface;
use App\Modules\BillingClient\Services\MultiPaymentInvoiceService;
use App\Modules\CustomLogger\Services\UserLoggerTrait;

class MultiCheckoutPaymentSummary implements PaymentSummaryInterface
{
    use UserLoggerTrait;

    public function getPaymentbyId(int $id, int $userId)
    {
        return MultiPaymentInvoiceService::instance()->getDataBySummaryId($id, $userId);
    }

    public function getRefundbyId(int $id, int $userId)
    {
        throw new FailedRequestException(400, 'Refund not supported.', 'Error');
    }
}
