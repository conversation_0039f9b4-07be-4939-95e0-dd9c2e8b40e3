<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\AdminCredit\Requests\SystemCreditStoreRequest;
use App\Modules\AdminCredit\Services\AdminCreditService;
use App\Modules\Client\Requests\ClientDeleteRequest;
use App\Modules\Client\Requests\ClientDetailRequest;
use App\Modules\Client\Requests\ClientUpdateStatusRequest;
use App\Modules\Client\Requests\ShowListRequest;
use Inertia\Inertia;

class ClientController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('Client/Index', $request->show());
    }

    public function update(ClientUpdateStatusRequest $request)
    {
        $request->updateStatus();

        return redirect()->route('client');
    }

    public function destroy(ClientDeleteRequest $request)
    {
        $request->softDelete();

        return redirect()->route('client');
    }

    public function show(ClientDetailRequest $request)
    {
        $data = $request->getClientDetails();

        return Inertia::render('Client/Detail', $data);
    }

    public function systemCredits(ClientDeleteRequest $request)
    {
        $data = AdminCreditService::instance()->getHistory();

        return Inertia::render('AdminCredit/SystemCredits', $data);
    }

    public function showChangeBalance()
    {
        $balance = AdminCreditService::instance()->balance();

        return Inertia::render('AdminCredit/SystemCreditChangeBalance', ['balance' => $balance]);
    }

    public function store(SystemCreditStoreRequest $request)
    {
        $request->store();

        return redirect()->route('system.credits');
    }
}
