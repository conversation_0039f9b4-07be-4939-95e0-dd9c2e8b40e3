<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('schedule_notifications');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('schedule_notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('title');
            $table->text('message');
            $table->string('link_name');
            $table->string('redirect_url');
            $table->string('type');
            $table->string('status');
            $table->string('schedule_type');
            $table->time('time');
            $table->date('start_date');
            $table->integer('min_registration_period')->nullable();
            $table->integer('max_registration_period')->nullable();
            $table->timestamp('expiration')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }
};
