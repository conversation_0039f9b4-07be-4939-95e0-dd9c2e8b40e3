<?php

namespace App\Modules\UserManagement\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth; 

class UserManagementCategoryCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'          => ['required', 'string', 'unique:category,name', 'min:3', 'max:30'],
            'permissions'   => ['required', 'array', 'min:1'],
            'permissions.*' => ['integer']
        ];
    }

    public function messages()
    {
        return [
            'permissions.required' => 'Please select at least one permission.',
        ];
    }
}
