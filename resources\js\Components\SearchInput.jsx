import React, { forwardRef, useEffect, useRef } from "react";

export default forwardRef(function TextInput(
    {
        type = "text",
        name,
        value,
        className,
        autoComplete,
        required,
        isFocused,
        handleChange,
        placeholder,
    },
    ref
) {
    const input = ref ? ref : useRef();

    useEffect(() => {
        if (isFocused) {
            input.current.focus();
        }
    }, []);

    return (
        <input
            placeholder={placeholder}
            type={type}
            name={name}
            value={value}
            className={
                `border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-full shadow-sm placeholder-gray-200 pl-6 ` +
                className
            }
            ref={input}
            autoComplete={autoComplete}
            required={required}
            onChange={(e) => handleChange(e)}
        />
    );
});
