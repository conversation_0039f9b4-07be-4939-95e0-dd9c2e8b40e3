import AdminLayout from "@/Layouts/AdminLayout";
import "react-toastify/dist/ReactToastify.css";
import { MdKeyboardBackspace } from "react-icons/md";
import EmptyResult from "@/Components/Util/EmptyResult";

export default function View({ poll, type }) {
    const payload = JSON.parse(JSON.stringify(poll));
    const { source } = route().params;

    const displayObject = (object, callback) => {
        return Object.keys(object).map((key) => {
            let value = object[key];
            // console.log(key, typeof value, value);

            if (value != null && typeof value === "object") {
                return displayObject(value, callback);
            } else {
                return callback(key, value);
            }
        });
    };

    const displayObjectContent = (key, value) => {
        return (
            <div key={key} className=" space-x-4">
                <label className="">{key}:</label>
                <span>{value || "-"}</span>
            </div>
        );
    };

    const convertTypeFormat = (str) => {
        str = str.toLowerCase();
        const words = str.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        return words.join(' ');
    }

    const capitalizeFirstLetter = (str) => {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    };

    // console.log(typeof (payload));
    console.log(payload);

    if (_.isEmpty(payload))
        return (
            <AdminLayout postRouteName={'epp.poll.view'}>
                <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col ">
                    <div className="text-gray-600 text-xl  py-5 space-x-2 inline-flex items-center">
                        <a href="#" onClick={() => window.history.back()}>
                            <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                        </a>
                        <label className="font-semibold">Back</label>
                    </div>
                    <div className="flex flex-col space-y-4">
                        <EmptyResult message="No data available." />
                    </div>
                </div>
            </AdminLayout>
        );

    return (
        <AdminLayout postRouteName={'epp.poll.view'}>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col ">
                <div className="text-gray-600 text-xl  py-5 space-x-2 inline-flex items-center">
                    <a href="#" onClick={() => window.history.back()}>
                        <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                    </a>
                    <label className="font-semibold">{source} Server ID:</label>
                    <span> {payload.id} </span>
                </div>
                <div className="flex flex-col space-y-4">
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            ID
                        </label>
                        <div>{payload.id}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Name
                        </label>
                        {/* <div>{(payload.summary.name != null) ? payload.summary.name.toLowerCase() : ''}</div> */}
                        <div>{('summary' in payload) ? payload.summary.name.toLowerCase() : ''}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Type
                        </label>
                        <div>{convertTypeFormat(type)}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Status
                        </label>
                        <div>{('status' in payload) ? capitalizeFirstLetter(payload.status) : ''}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Request Client
                        </label>
                        <div>{(type == 'DOMAIN_TRANSFER') ? ('summary' in payload) ? payload.summary.requestClient : '': ''}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Action Client
                        </label>
                        <div>{(type == 'DOMAIN_TRANSFER') ? ('summary' in payload) ? payload.summary.actionClient : '': ''}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Expiration at
                        </label>
                        <div>
                            {(type == 'DOMAIN_TRANSFER') ? ('summary' in payload) ? new Date(payload.summary.expirationDate).toLocaleString() : '': ''}</div>
                    </div>
                    <div className="flex hover:bg-gray-100">
                        <label className="flex-none w-1/3 font-semibold">
                            Created at
                        </label>
                        <div>
                            {(type == 'DOMAIN_TRANSFER') ? ('summary' in payload) ? new Date(payload.summary.actionDate).toLocaleString() : '':''}</div>
                    </div>
                    <div className="flex flex-col  space-y-2">
                        <label className=" font-semibold">Body</label>
                        <div className="bg-gray-200 p-4 rounded-sm space-y-2 overflow-x-auto max-h-96 text-sm">
                            {(type == 'DOMAIN_TRANSFER') ? <>{"{"}
                                <div className="pl-6">
                                    {displayObject(payload, displayObjectContent)}
                                </div>
                                {"}"}</> :
                                <div className="pl-6">
                                    {JSON.stringify(poll)}
                                </div>
                            }


                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
