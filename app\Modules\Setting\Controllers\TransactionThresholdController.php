<?php

namespace App\Modules\Setting\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Requests\ShowListRequest;
use App\Modules\Setting\Constants\ExtensionFeeType;
use App\Modules\Setting\Requests\ExtensionFeeDeleteRequest;
use App\Modules\Setting\Requests\ExtensionFeeUpdateRequest;
use App\Modules\Setting\Requests\ShowUserCustomLimitRequest;
use App\Modules\Setting\Requests\SystemTransactionUpdateAllRequest;
use App\Modules\Setting\Requests\SystemTransactionUpdateRequest;
use App\Modules\Setting\Services\ExtensionFeeService;
use App\Modules\Setting\Services\TransactionThresholdService;
use App\Util\Constant\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class TransactionThresholdController extends Controller
{
    public function get_default()
    {
        $query = TransactionThresholdService::instance()->transactionDataQuery();
        $transactions = $query->orderBy('transactions.name')->get()->keyBy('name')->toArray();

        return Inertia::render('Setting/SystemThreshold/Default', [
            'transactions' => $transactions,
        ]);
    }

    public function update(SystemTransactionUpdateRequest $request)
    {
        $request->update();

        return response()->json([
            'success' => true,
            'message' => 'Transaction settings updated successfully.'
        ]);
    }

    public function updateAll(SystemTransactionUpdateAllRequest $request)
    {
        $request->update();

        return redirect()->back();
    }

    public function get_users(ShowListRequest $request)
    {
        $data = $request->showUsers();

        return Inertia::render('Client/TransactionThreshold/Index', $data);
    }

    public function get_user_custom(ShowUserCustomLimitRequest $request)
    {
        $data = $request->getData();

        return Inertia::render('Client/TransactionThreshold/View', $data);
    }

    public function updateCustomLimit(Request $request)
    {
        DB::client()->table('user_transactions')
            ->where('user_id', $request->userId)
            ->where('transaction_id', $request->transactionId)
            ->increment('custom_limit', $request->adjustment);

        return Inertia::location(route('client.transaction.threshold-view', [
            'id' => $request->userId,
            'email' => $request->email,
        ]));
    }

    public function updateMultipleCustomLimits(Request $request)
    {
        $toUpdate = array_filter($request->payload, function ($item) {
            return (int) $item['adjustment'] !== 0;
        });

        // $query = DB::client()->table('user_transactions')
        //     ->where('user_id', $request->userId);

        foreach ($toUpdate as $item) {
            DB::client()->table('user_transactions')
                ->where('user_id', $request->userId)
                ->where('transaction_id', $item['transactionId'])
                ->increment('custom_limit', $item['adjustment']);
        }

        return Inertia::location(route('client.transaction.threshold-view', [
            'id' => $request->userId,
            'email' => $request->email,
        ]));
    }
}
