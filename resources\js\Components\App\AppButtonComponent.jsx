//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppButtonComponent(
    {
        children,
        type             = 'button',
        isDisabled       = false,
        handleEventClick = () => alert('clicked')
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    //...

    //! FUNCTIONS
    //...

    return (
        <button
            type={type}
            className={
                `flex items-center gap-2 rounded-md px-4 py-2 text-white duration-100 ease-in
                ${isDisabled 
                    ? 'bg-primary/50 cursor-not-allowed opacity-70' 
                    : 'bg-primary hover:bg-opacity-90 cursor-pointer'
                }`
            }
            disabled={isDisabled}
            onClick={handleEventClick}
        >
            {children}
        </button>
    );
}
