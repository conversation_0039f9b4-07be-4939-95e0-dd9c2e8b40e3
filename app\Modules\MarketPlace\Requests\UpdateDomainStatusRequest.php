<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Services\MarketPlaceService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class UpdateDomainStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer',
            'status' => 'required|string'
        ];
    }

    public function update() : void
    {
        MarketPlaceService::instance()->setDomainStatus($this->id, $this->status);
    }
}
