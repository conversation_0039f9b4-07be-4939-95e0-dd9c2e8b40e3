import Checkbox from "@/Components/Checkbox";
import CommissionItem from "@/Components/Setting/CommissionItem";
import SecondaryButton from "@/Components/SecondaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
// import { useState } from "react";
import {
    MdArrowBackIos,
    MdArrowForwardIos,
    MdOutlineSettings,
    MdOutlineSortByAlpha,
} from "react-icons/md";
import { Link, router } from "@inertiajs/react";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { getEventValue } from "@/Util/TargetInputEvent";
import CursorPaginate from "@/Components/Util/CursorPaginate";
export default function Commission({
    commissions,
    fees,
    errors,
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    useEffect(() => {
        if (errors.message != undefined) {
            toast.warn(errors.message, { autoClose: 2000 });
        }
    });
    // console.log(fees);
    const paramStatus = route().params.status;
    const [onProgress, setOnProgress] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectedAll, setSelectedAll] = useState(false);
    const handleBulkStatusUpdate = (status) => {
        setOnProgress(true);
        toast.info("Updating Status.");
        const updateSelectedItems = [...selectedItems];
        setSelectedItems([]);
        router.patch(route("setting.commission-update-status"), {
            id: updateSelectedItems,
            value: status,
        });
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectedAll(checked);

        setSelectedItems(
            checked ? Object.values(commissions).map((item) => item.id) : []
        );
    };

    const handleItemCheckboxChange = (itemId, checked) => {
        if (!checked && selectedAll) setSelectedAll(false);

        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 justify-end">
                    <SecondaryButton
                        onClick={() =>
                            router.get(route("setting.commission-create"))
                        }
                        method="get"
                    >
                        New
                    </SecondaryButton>
                    <SecondaryButton
                        processing={!onProgress && selectedItems.length == 0}
                        onClick={() => handleBulkStatusUpdate(true)}
                    >
                        Enable
                    </SecondaryButton>
                    <SecondaryButton
                        processing={!onProgress && selectedItems.length == 0}
                        onClick={() => handleBulkStatusUpdate(false)}
                    >
                        Disable
                    </SecondaryButton>
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    <Link as="button" href={route("setting.commission")}>
                        <div
                            className={`hover:bg-gray-100 hover:text-link   px-5 py-1 rounded-sm ${paramStatus == undefined &&
                                "bg-gray-100 text-link"
                                } `}
                        >
                            <span className=" text-inherit">all</span>
                        </div>
                    </Link>
                    {Object.values(fees).map((e) => {
                        return (
                            <Link
                                key={e.id}
                                as="button"
                                href={route("setting.commission", {
                                    status: e.id,
                                })}
                            >
                                <div
                                    className={`hover:bg-gray-100 hover:text-link   px-5 py-1 rounded-sm ${paramStatus == e.id &&
                                        "bg-gray-100 text-link"
                                        } `}
                                >
                                    <span className=" text-inherit lowercase">
                                        {e.type}
                                    </span>
                                </div>
                            </Link>
                        );
                    })}
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center pl-2 space-x-2">
                                        <Checkbox
                                            checked={selectedAll}
                                            handleChange={(e) =>
                                                handleSelectAllChange(e)
                                            }
                                        />
                                        <span className="">Minimum domain</span>
                                    </label>
                                </th>
                                <th>
                                    <span>type</span>
                                </th>
                                <th>
                                    <span>% rate</span>
                                </th>
                                <th>
                                    <span>fix rate</span>
                                </th>
                                <th>
                                    <span>enable</span>
                                </th>
                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {Object.values(items).map((item) => (
                                <CommissionItem
                                    item={item}
                                    key={"ci-" + item.id}
                                    isSelected={selectedItems.includes(item.id)}
                                    onCheckboxChange={handleItemCheckboxChange}
                                />
                            ))}
                        </tbody>
                    </table>
                </div>
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                />
            </div>
        </AdminLayout>
    );
}
