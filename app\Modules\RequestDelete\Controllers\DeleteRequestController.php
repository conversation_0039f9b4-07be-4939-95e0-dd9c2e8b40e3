<?php

namespace App\Modules\RequestDelete\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\RequestDelete\Requests\ApproveDeleteRequest;
use App\Modules\RequestDelete\Requests\CancelDeleteRequest;
use App\Modules\RequestDelete\Requests\CreateDeleteRequest;
use App\Modules\RequestDelete\Requests\RejectDeleteRequest;
use App\Modules\RequestDelete\Requests\ShowListRequest;
use Inertia\Inertia;

class DeleteRequestController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('RequestDelete/Index', $request->show());
    }

    public function store(ShowListRequest $request)
    {
        return $request->feedbackSave();
    }

    public function approve_delete(ApproveDeleteRequest $request)
    {
        return $request->approve();
    }

    public function reject_delete(RejectDeleteRequest $request)
    {
        return $request->reject();
    }

    public function cancel_delete(CancelDeleteRequest $request)
    {
        return $request->cancel();
    }

    public function create_delete(CreateDeleteRequest $request)
    {
        return $request->create_deleteRequest();
    }
}