import AdminLayout from "@/Layouts/AdminLayout";
import { Link, router } from "@inertiajs/react";
import { MdOutlineSortByAlpha, MdOutlineDateRange, MdKeyboardBackspace, MdOutlineSettings, MdMoreVert } from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import { toast } from "react-toastify";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useState } from "react";
import LoaderSpinner from "@/Components/LoaderSpinner";
import EmptyList from "@/Components/Billing/EmptyList";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import Checkbox from "@/Components/Checkbox";
import { useRef } from "react";
import { getEventValue } from "@/Util/TargetInputEvent";
import SecondaryButton from "@/Components/SecondaryButton";
import Item from "@/Components/Job/Item";

export default function Job({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    queueCount,
    stats,
    date,
    itemName = "item",
}) {
    const { connection, source, tab } = route().params;
    const [hasSpinner, setSpinner] = useState(false);
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    console.log(items);
    const config = {
        queue: { active: "queue" === tab },
        failed: { active: "failed" === tab }
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);
        setSelectedItems(checked ? items.map((item) => item.id) : []);
    };

    const handleItemCheckboxChange = (itemId, checked) => {
        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });

        let temp = [...selectedItems];

        if (temp.includes(itemId)) {
            temp = temp.filter((e) => e != itemId);
        } else {
            temp.push(itemId);
        }

        temp.length == items.length ? setSelectAll(true) : setSelectAll(false);
    };

    const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

    const handleForget = async (option) => {
        toast.info("Forgetting Failed Jobs.");

        if ("all".localeCompare(option) == 0)
            router.post(route("job.failed-flush"), {
                source: source,
            });
        else
            router.post(route("job.failed-forget"), {
                source: source,
                option: option,
            });

        await sleep(500);
        setSelectedItems([]);
        setSelectAll(false);
    };

    const handleRetry = async (option) => {
        toast.info("Retrying Failed Jobs.");
        router.post(route("job.failed-retry"), {
            source: source,
            option: option,
        });

        await sleep(500);
        setSelectedItems([]);
        setSelectAll(false);
    };

    const getSelectedItemUuid = () => {
        return Object.values(items)
            .filter((item) => selectedItems.includes(item.id))
            .map((item) => item.uuid)
            .join(" ");
    };

    const handleDateChange = (dateInput) => {
        router.get(route("job.view"), { connection, source, date: dateInput, tab });
    }

    const getUUID = (payload) => {
        return JSON.parse(payload)?.uuid
    }

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1000px] mt-10 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                    <a href={route("job", { source })} >
                        <MdKeyboardBackspace className="text-3xl hover:bg-black hover:bg-opacity-20 rounded-full p-1 transition duration-150 cursor-pointer" />
                    </a>
                    <label className="text-gray-600 text-3xl font-semibold py-5">
                        {connection}
                    </label>
                </div>
                <div className="text-gray-600 text-xl font-semibold py-5 inline-flex">
                    Active stats: (
                    <div className=" text-primary px-1 inline-flex items-center">
                        <DatePicker
                            selected={new Date(date)}
                            onChange={handleDateChange}
                            className=" border-none w-28 p-0 text-center cursor-pointer"
                        />
                        <MdOutlineDateRange />
                    </div>
                    )
                </div>
                {hasSpinner ? (
                    <div className="flex flex-col items-center justify-center space-x-4 min-h-[200px]">
                        <LoaderSpinner h="h-12" w="w-12" />
                        <span className="mt-4 text-gray-600">Loading Data...</span>
                    </div>) : (
                    <>
                        <div className="flex flex-col text-default">

                            <div className="flex space-x-4 justify-between">
                                <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                    <label className="text-xl font-semibold text-gray-600">
                                        ON QUEUE
                                    </label>
                                    <span className=" text-7xl">
                                        {queueCount}
                                    </span>
                                </div>
                                <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                    <label className="text-xl font-semibold text-gray-600">
                                        DISPATCH
                                    </label>
                                    <span className=" text-7xl">
                                        {stats?.dispatch || "0"}
                                    </span>
                                </div>
                                <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                    <label className="text-xl font-semibold text-gray-600">
                                        SUCCESS
                                    </label>
                                    <span className=" text-7xl">
                                        {stats?.success || "0"}
                                    </span>
                                </div>
                                <div className="flex flex-col justify-start space-y-6 w-full px-10 py-8 bg-[url('/assets/images/wave2.svg')] bg-no-repeat bg-center">
                                    <label className="text-xl font-semibold text-gray-600">
                                        FAILED
                                    </label>
                                    <span className=" text-7xl">
                                        {stats?.failed || "0"}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div className="flex poll-center flex-wrap cursor-pointer border-b text-default">
                            <a
                                href={route("job.view", { connection, source, date, tab: 'queue' })}
                                className={"text-center px-5 py-1 " +
                                    `${config.queue.active ? "bg-gray-100 text-gray-700 font-bold" : "hover:bg-gray-100 hover:text-link"}`}
                            >
                                <span className="text-inherit">Queued Jobs</span>
                            </a>

                            <a
                                href={route("job.view", { connection, source, date, tab: 'failed' })}
                                className={"text-center px-5 py-1 " +
                                    `${config.failed.active ? "bg-gray-100 text-gray-700 font-bold" : "hover:bg-gray-100 hover:text-link"}`}
                            >
                                <span className="text-inherit">Failed Jobs</span>
                            </a>
                        </div>
                        {config.queue.active ? (
                            items.length > 0 ? (
                                <table className="min-w-full text-left border-spacing-y-2 border-separate mb-4">
                                    <thead className="bg-gray-50 text-sm">
                                        <tr>
                                            <th className="py-2 px-2">UUID</th>
                                            <th>
                                                <label className="flex items-center space-x-2">
                                                    <span>Queue</span>
                                                    {/* <MdOutlineSortByAlpha /> */}
                                                </label>
                                            </th>
                                            <th>Attempts</th>
                                            <th>Queued At</th>
                                            <th>Available At</th>
                                        </tr>
                                    </thead>
                                    <tbody className="text-sm">
                                        {items.map((item) => (
                                            <tr key={`queue-${item.id}`} className="hover:bg-gray-100">
                                                <td className="px-2 py-1">{getUUID(item.payload)}</td>
                                                <td>{item.queue}</td>
                                                <td>{item.attempts}</td>
                                                <td>{new Date(item.created_at * 1000).toLocaleString()}</td>
                                                <td>{new Date(item.available_at * 1000).toLocaleString()}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            ) : (
                                <EmptyList message="There are currently no jobs in the queue." />
                            )
                        ) : config.failed.active ? (
                            items.length > 0 ? (
                                <>
                                    <div className="flex items-center space-x-4 justify-end">
                                        <SecondaryButton
                                            processing={source == undefined}
                                            onClick={() => handleForget("all")}
                                        >
                                            Forget all
                                        </SecondaryButton>
                                        <SecondaryButton
                                            processing={source == undefined}
                                            onClick={() => handleRetry("all")}
                                        >
                                            Retry all
                                        </SecondaryButton>
                                        <SecondaryButton
                                            processing={
                                                source == undefined || selectedItems.length == 0
                                            }
                                            onClick={() => handleForget(getSelectedItemUuid())}
                                        >
                                            Forget selected
                                        </SecondaryButton>
                                        <SecondaryButton
                                            processing={
                                                source == undefined || selectedItems.length == 0
                                            }
                                            onClick={() => handleRetry(getSelectedItemUuid())}
                                        >
                                            Retry selected
                                        </SecondaryButton>
                                    </div>

                                    <table className="min-w-full text-left border-spacing-y-2 border-separate mb-4">
                                        <thead className="bg-gray-50 text-sm">
                                            <tr>
                                                <th className="py-2 px-2">
                                                    <label className="flex items-center pl-2 space-x-2">
                                                        <Checkbox
                                                            name="select_all"
                                                            value="select_all"
                                                            checked={selectAll}
                                                            handleChange={
                                                                handleSelectAllChange
                                                            }
                                                        />
                                                        <span>UUID</span>
                                                        <MdOutlineSortByAlpha />
                                                    </label>
                                                </th>
                                                <th><span>Connection</span></th>
                                                <th><span>Queue</span></th>
                                                <th><span>Failed At</span></th>
                                                <th><span className="text-xl"><MdOutlineSettings /></span></th>
                                            </tr>

                                        </thead>
                                        <tbody className="text-sm">
                                            {items.map(item => (
                                                <Item
                                                    item={item}
                                                    source={source}
                                                    connection={connection}
                                                    key={"fj-" + item.id}
                                                    isSelected={selectedItems.includes(item.id)}
                                                    onCheckboxChange={handleItemCheckboxChange}
                                                    handleRetry={handleRetry}
                                                    handleForget={handleForget}
                                                />
                                            ))}
                                        </tbody>
                                    </table>
                                </>
                            ) : (
                                <EmptyList message="There are no failed jobs at the moment." />
                            )
                        ) : (
                            <EmptyList message="No data to show." />
                        )}

                        <CursorPaginate
                            onFirstPage={onFirstPage}
                            onLastPage={onLastPage}
                            nextPageUrl={nextPageUrl}
                            previousPageUrl={previousPageUrl}
                            itemCount={itemCount}
                            total={total}
                            itemName={itemName}
                        />

                    </>
                )}
            </div>
        </AdminLayout>
    );
}
