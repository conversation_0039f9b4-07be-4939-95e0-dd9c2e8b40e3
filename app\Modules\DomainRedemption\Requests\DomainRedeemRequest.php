<?php

namespace App\Modules\DomainRedemption\Requests;

use App\Modules\DomainRedemption\Services\DomainRedemptionService;
use Illuminate\Foundation\Http\FormRequest;

class DomainRedeemRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ids' => ['required', 'array']
        ];
    }

    public function delete(): void
    {
        DomainRedemptionService::instance()->delete($this->ids);
    }
}
