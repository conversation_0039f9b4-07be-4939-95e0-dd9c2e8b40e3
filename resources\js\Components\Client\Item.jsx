//* PACKAGES
import React, {useState, useEffec, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import {
    MdMoreVert,
    MdOutlinePersonOutline,
    MdRadioButtonChecked,
    MdOutlineLockPerson,
    MdAttribution,
    MdVerifiedUser,
    MdHourglassEmpty,
    MdVerified,
} from "react-icons/md";

//* COMPONENTS
import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import ShowMoreLess from "../ShowMoreLess";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
import getRecentTime from "@/Util/getRecentTime";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Item(
    {
        item,
        isSelected,
        onCheckboxChange
    }
)
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS    
    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, item.is_active, getEventValue(e));
    };

    const handleResponse = (status) => {
        setShow(false);
        toast.info("Updating Client Status.");
        router.patch(route("client.update-status"), {
            ids: [item.id],
            status: status,
            is_invited: item.is_invited,
        });
    };

    const handleDelete = () => {
        setShow(false);
        toast.success("Client Has Been Successfully Deleted.");
        router.delete(route("client.delete"), {
            data: [item.id],
        });
    };
    const handleResend = () => {
        setShow(false);
        toast.success("Client Invite Resent Successfully.");
        router.get(route("client.invite.resend"), {
            userid: item.id,
        });
    };
    const handleDomains = () => {
        setShow(false);
        router.get(route("client.domains", { id: item.id }), {
            status: "Active",
        });
    };

    const handDeviceSession = () => {
        router.get(route("client.sessions", { id: item.id }), {
            status: "Active",
        });
    };

    const lastActiveStatus = (lastActiveAt, updatedAt) => {
        let date = lastActiveAt === null ? null : lastActiveAt;
        if (date === null)
            return (
                <span className="font-semibold text-red-500">
                    Not Yet Active
                </span>
            );
        return (
            <span className="font-semibold text-green-500">
                {getRecentTime(date, true)}
            </span>
        );
    };

    const handleLog = () => {
        router.get(
            route("client.logs.security", { user_id: item.id }),
            {},
            {
                preserveState: true,
                preserveScroll: true,
            }
        );
    };

    const handleViewDetails = () => {
        setShow(false);
        router.get(route("client.details", { id: item.id }));
    };

    const actionsDropdown = () =>
    {
        const actions =
        [
            {
                hasAccess       : hasPermission('client.logs.security'),
                shouldDisplay   : item.contact_setup,
                label           : 'view log',
                handleEventClick: handleLog,
            }, 
            {
                hasAccess       : hasPermission('client.logs.domain'),
                shouldDisplay   : item.contact_setup,
                label           : 'view details',
                handleEventClick: handleViewDetails,
            },
            {
                hasAccess       : hasPermission('client.domains'),
                shouldDisplay   : item.contact_setup,
                label           : 'domains',
                handleEventClick: handleDomains,
            }, 
            {
                hasAccess       : hasPermission('client.sessions'),
                shouldDisplay   : item.contact_setup,
                label           : 'devices & sessions',
                handleEventClick: handDeviceSession,
            }, 
            {
                hasAccess       : hasPermission('client.update-status'),
                shouldDisplay   : item.contact_setup,
                label           : item.is_active ? "disable" : "enable",
                handleEventClick: handleResponse,
            }, 
            {
                hasAccess       : hasPermission('client.delete'),
                shouldDisplay   : item.contact_setup,
                label           : 'delete',
                handleEventClick: handleDelete,
            }, 
            {
                hasAccess       : hasPermission('client.invite.resend'),
                shouldDisplay   : item.contact_setup && !item.is_active && item.is_invited,
                label           : 'resemd',
                handleEventClick: handleResend,
            }, 
        ];
        
        const permittedActions = actions.filter(action => action.hasAccess && action.shouldDisplay);
    
        return (
            <DropDownContainer
                show={show}
            >
                {
                    permittedActions.length == 0 
                        ?
                            <div
                                className="px-5 py-1 text-danger flex justify-start font-medium"
                            >
                                No Actions Permitted
                            </div>
                        :
                                permittedActions.map(
                                    (action, actionIndex) => 
                                    {
                                        return (
                                            <button
                                                key={actionIndex}
                                                className="px-5 py-1 hover:bg-gray-100 flex justify-start capitalize"
                                                onClick={action.handleEventClick}
                                            >
                                                {action.label}
                                            </button>
                                        )
                                    }
                            )
                }
            </DropDownContainer>
        );
    };

    useOutsideClick(ref, () => {
        setShow(false);
    });

    return (
        <tr className="hover:bg-gray-100 ">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <Checkbox
                        name="user"
                        value="user"
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                        // disabled={!item.is_active && item.is_invited}
                    />
                    <div className=" text-2xl border p-1 rounded-full">
                        <MdOutlinePersonOutline />
                    </div>
                    <span className=" font-semibold cursor-pointer">
                        {item.name}
                    </span>
                </label>
            </td>
            <td>
                <span>{item.email}</span>
            </td>
            <td>{lastActiveStatus(item.last_active_at, item.updated_at)}</td>
            <td>
                {item.is_active && item.contact_setup ? (
                    <div className="text-green-500 inline-flex space-x-1 items-center">
                        <MdVerifiedUser />
                        <span>Enabled</span>
                    </div>
                ) : item.contact_setup && !item.is_active ? (
                    <div className="text-gray-400 inline-flex space-x-1 items-center">
                        <MdOutlineLockPerson />
                        <span>Disabled</span>
                    </div>
                ) : !item.is_active && item.is_invited ? (
                    <div className="text-yellow-500 inline-flex space-x-1 items-center">
                        <MdHourglassEmpty />

                        <span>Pending Invite</span>
                    </div>
                ) : !item.contact_setup ? (
                    <div className="text-red-500 inline-flex space-x-1 items-center">
                        <MdVerified />
                        <span>Verification</span>
                    </div>
                ) : (
                    <div></div>
                )}
            </td>
            <td>
                <ShowMoreLess
                    condition={item.ip ? item.ip.length > 15 : false}
                    item={item.ip}
                    doSplit={true}
                    itemUniqueKey={`${item.id}-IP-`}
                    separator=","
                />
            </td>
            <td>
                <span>{item.domain_count}</span>
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    {actionsDropdown()}
                </span>
            </td>
        </tr>
    );
}
