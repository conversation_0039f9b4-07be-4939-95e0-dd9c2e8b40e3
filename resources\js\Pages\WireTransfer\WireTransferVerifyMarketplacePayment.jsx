//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdKeyboardBackspace } from "react-icons/md";

//* COMPONENTS
import AdminLayout from "../../Layouts/AdminLayout";
import WireTransferPaymentInvoiceComponent from '@/Components/WireTransfer/WireTransferPaymentInvoiceComponent';
import WireTransferVerificationFormComponent from '@/Components/WireTransfer/WireTransferVerificationFormComponent';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { convertDateUtil, convertDateToTimeUtil } from '@/Util/DateTimeHelperUtils';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferVerifyMarketplacePayment(
    {
        payment, 
        invoiceOrders, 
        purpose,
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <AdminLayout
            hideNav={true}
            postRouteName={'billing.wire.transfer.verify-edit'}
        >
            <div
                className="
                    mx-auto container max-w-[1100px] mt-20
                    flex flex-col gap-8
                "
            >
                <div
                    className="flex payments-center gap-2"
                >
                    <a href="#" onClick={() => window.history.back()}>
                        <MdKeyboardBackspace className="text-2xl" />
                    </a>
                    <h1 className="text-xl">
                        Bank Transfer Verification
                    </h1>
                </div>

                <div
                    className='flex flex-col lg:flex-row lg:items-start gap-8'
                >
                    {/* INVOICE */}
                    <WireTransferPaymentInvoiceComponent
                        payment={payment} 
                        invoiceOrders={invoiceOrders}
                        classNames={'w-full lg:basis-3/5'}
                    />

                    {/* FORM */}
                    <WireTransferVerificationFormComponent
                        payment={payment}
                        classNames={'w-full lg:basis-2/5'}
                    />
                </div>
            </div>
        </AdminLayout>
    );
}
