<?php

namespace App\Modules\ClientPaymentService\Services\Interfaces;

use App\Modules\ClientPaymentService\Constants\PaymentServiceType;
use App\Modules\ClientPaymentService\Contracts\PaymentServiceInterface;
use Illuminate\Support\Facades\DB;

class SystemCreditPaymentService implements PaymentServiceInterface
{
    public function getPaymentService(object $paymentService)
    {
        $data = DB::client()->table('payment_services')
            ->join('system_credits', 'system_credits.id', '=', 'payment_services.system_credit_id')
            ->join('payment_summaries', 'payment_summaries.payment_service_id', '=', 'payment_services.id')
            ->where('payment_services.id', $paymentService->id)
            ->where('payment_services.user_id', $paymentService->user_id)
            ->where('system_credits.id', $paymentService->system_credit_id)
            ->select(
                'payment_services.*',
                'system_credits.type',
                'system_credits.running_balance',
                'system_credits.amount',
                'system_credits.note',
                'payment_summaries.paid_amount as paid_amount',
                'payment_summaries.total_amount as total_amount',
                'payment_summaries.name as summary_name',
                'payment_summaries.type as summary_type',
            )->get()->first();

        $data->payment_service_type = PaymentServiceType::SYSTEM_CREDIT;

        // dd($data);

        return $data;
    }
}
