<?php

namespace App\Modules\AdminHistory\Requests;

use App\Modules\AdminHistory\Services\AdminHistoryService;
use Illuminate\Foundation\Http\FormRequest;

class AdminHistoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['nullable', 'string'],
            'date' => ['nullable', 'string', 'in:today,yesterday,last 7 days,last 30 days'],
        ];
    }

    public function show()
    {
        return AdminHistoryService::instance()->prepareAdminLogView($this);
    }
}
