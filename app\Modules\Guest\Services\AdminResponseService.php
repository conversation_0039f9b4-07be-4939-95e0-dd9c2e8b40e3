<?php

namespace App\Modules\Guest\Services;

use App\Exceptions\FailedRequestException;
use App\Mail\RequestConfirmation;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Guest\Constants\RequestStatus;
use App\Modules\Guest\Constants\RequestType;
use App\Modules\Guest\Jobs\UpdateGuestRequest;
use App\Util\Constant\QueueConnection;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;

class AdminResponseService
{
    public static function getRequest($id)
    {
        return DB::client()->table('guest_requests')
            ->where('id', $id)->first();
    }

    private static function getExpirationDate($url)
    {
        $urlParts = parse_url($url);
        parse_str($urlParts['query'], $expiry);
        $expireTimestamp = (int) $expiry['expires'];

        return Carbon::createFromTimestamp($expireTimestamp)->toDateTimeString().' UTC';
    }

    public static function update($id, $status)
    {
        $guestRequest = self::getRequest($id);

        if ($guestRequest == null) {
            throw ValidationException::withMessages(['message' => 'guest request id not found']);
        }

        $error = false;

        if ($status == RequestStatus::DELETED) {
            $status = RequestStatus::DELETED.'-'.$guestRequest->status;
        }

        switch ($status.$guestRequest->type) {
            case RequestStatus::ACCEPT.RequestType::REGISTRATION_ACCESS:
                self::whitelistIp($guestRequest->ip);

                $expirationDate = self::getExpirationDate($guestRequest->url);

                Mail::to($guestRequest->email)->send(new RequestConfirmation([
                    'subject' => 'Registration Access Approved',
                    'greeting' => 'Greetings!',
                    'body' => 'Your registration access request has been verified, please click the link below to complete your registration.',
                    'url' => $guestRequest->url,
                    'text' => 'Link will expire on: '.$expirationDate.'.',
                    'sender' => config('mail.from.sd_name'),
                ]));

                break;
            case RequestStatus::ACCEPT.RequestType::NEW_IP:
                self::setUserIp($guestRequest->email, $guestRequest->ip);

                Mail::to($guestRequest->email)->send(new RequestConfirmation([
                    'subject' => 'New IP Approved',
                    'greeting' => 'Greetings!',
                    'body' => 'New IP:'.$guestRequest->ip.' has been added under account '.$guestRequest->email.'. Click the link below to login.',
                    'url' => $guestRequest->url,
                    'text' => '',
                    'sender' => config('mail.from.sd_name'),
                ]));

                break;
            case RequestStatus::DENIED.RequestType::REGISTRATION_ACCESS:
                Mail::to($guestRequest->email)->send(new RequestConfirmation([
                    'subject' => 'Registration Access Denied',
                    'greeting' => 'Greetings!',
                    'body' => 'We regret to tell you that your registration access request for '.$guestRequest->email.' has been denied.',
                    'url' => '',
                    'text' => '',
                    'sender' => config('mail.from.sd_name'),
                ]));

                break;
            case RequestStatus::DENIED.RequestType::NEW_IP:
                Mail::to($guestRequest->email)->send(new RequestConfirmation([
                    'subject' => 'New IP Denied',
                    'greeting' => 'Greetings!',
                    'body' => 'We regret to tell you that your request to add the IP: '.$guestRequest->ip.' to access your account '.$guestRequest->email.' has been denied.',
                    'url' => '',
                    'text' => '',
                    'sender' => config('mail.from.sd_name'),
                ]));

                break;
            case RequestStatus::DELETED.'-'.RequestStatus::PENDING.RequestType::REGISTRATION_ACCESS:
                self::changeStatus($id, ['status' => RequestStatus::DENIED, 'url' => '']);
                Mail::to($guestRequest->email)->send(new RequestConfirmation([
                    'subject' => 'Registration Access Denied',
                    'greeting' => 'Greetings!',
                    'body' => 'We regret to tell you that your registration access request for '.$guestRequest->email.' has been denied.',
                    'url' => '',
                    'text' => '',
                    'sender' => config('mail.from.sd_name'),
                ]));
                break;
            case RequestStatus::DELETED.'-'.RequestStatus::PENDING.RequestType::NEW_IP:
                self::changeStatus($id, ['status' => RequestStatus::DENIED, 'url' => '']);
                Mail::to($guestRequest->email)->send(new RequestConfirmation([
                    'subject' => 'New IP Denied',
                    'greeting' => 'Greetings!',
                    'body' => 'We regret to tell you that your request to add the IP: '.$guestRequest->ip.' to access your account '.$guestRequest->email.' has been denied.',
                    'url' => '',
                    'text' => '',
                    'sender' => config('mail.from.sd_name'),
                ]));
                break;
            case RequestStatus::DELETED.'-'.RequestStatus::ACCEPT.RequestType::REGISTRATION_ACCESS:
            case RequestStatus::DELETED.'-'.RequestStatus::DENIED.RequestType::REGISTRATION_ACCESS:
            case RequestStatus::DELETED.'-'.RequestStatus::ACCEPT.RequestType::NEW_IP:
            case RequestStatus::DELETED.'-'.RequestStatus::DENIED.RequestType::NEW_IP:
                $error = false;
                break;

            default:
                $error = true;
                break;
        }

        if ($error) {
            throw ValidationException::withMessages(['message' => 'invalid status']);
        } else {
            $log_id = is_int($id) ? $id : implode(',', $id);
            app(AuthLogger::class)->info('MAIL REQUEST status of '.$log_id.' to '.$guestRequest->email);
        }
    }

    public static function queueUpdate(array $ids, $status)
    {
        foreach ($ids as $id) {
            UpdateGuestRequest::dispatch($id, $status)
                ->onConnection(QueueConnection::GUEST_REQUEST)->onQueue($status);
        }
    }

    public static function updateStatusAll(array $ids, $status)
    {
        $error = false;
        switch ($status) {
            case RequestStatus::ACCEPT:
                self::changeStatus($ids, ['status' => RequestStatus::ACCEPT]);
                break;
            case RequestStatus::DENIED:
                self::changeStatus($ids, ['status' => RequestStatus::DENIED, 'url' => '']);
                break;
            case RequestStatus::DELETED:
                self::changeStatus($ids, ['deleted_at' => now()]);
                break;
            default:
                $error = true;
                break;
        }
        if ($error) {
            throw ValidationException::withMessages(['message' => 'invalid status']);
        }
        app(AuthLogger::class)->info('update REQUEST status of '.implode(',', $ids).' to '.$status);
    }

    public static function queueSoftDelete(array $ids)
    {
        foreach ($ids as $id) {
            UpdateGuestRequest::dispatch($id, RequestStatus::DELETED)
                ->onConnection(QueueConnection::GUEST_REQUEST)->onQueue(RequestStatus::DELETED);
        }
    }

    public static function softDelete(array $ids)
    {
        $emailSent = 0;
        $emailSentLimit = 5;

        foreach ($ids as $id) {
            $guestRequest = self::getRequest($id);
            if ($guestRequest == null) {
                throw ValidationException::withMessages(['message' => 'guest request id not found']);
            }

            switch ($guestRequest->status.$guestRequest->type) {
                case RequestStatus::PENDING.RequestType::REGISTRATION_ACCESS:
                    if (++$emailSent > $emailSentLimit) {
                        throw new FailedRequestException(200, 'You can only send a maximum of 5 consecutive accept or deny responses via email.', 'Only 5 emails were sent.');
                    }

                    self::changeStatus($id, ['status' => RequestStatus::DENIED, 'url' => '', 'deleted_at' => now()]);

                    Mail::to($guestRequest->email)->send(new RequestConfirmation([
                        'subject' => 'Registration Access Denied',
                        'greeting' => 'Greetings!',
                        'body' => 'We regret to tell you that your registration access request for '.$guestRequest->email.' has been denied.',
                        'url' => '',
                        'text' => '',
                        'sender' => config('mail.from.sd_name'),
                    ]));

                    break;
                case RequestStatus::PENDING.RequestType::NEW_IP:
                    if (++$emailSent > $emailSentLimit) {
                        throw new FailedRequestException(200, 'You can only send a maximum of 5 consecutive accept or deny responses via email.', 'Only 5 emails were sent.');
                    }

                    self::changeStatus($id, ['status' => RequestStatus::DENIED, 'url' => '', 'deleted_at' => now()]);

                    Mail::to($guestRequest->email)->send(new RequestConfirmation([
                        'subject' => 'New IP Denied',
                        'greeting' => 'Greetings!',
                        'body' => 'We regret to tell you that your request to add the IP: '.$guestRequest->ip.' to access your account '.$guestRequest->email.' has been denied.',
                        'url' => '',
                        'text' => '',
                        'sender' => config('mail.from.sd_name'),
                    ]));

                    break;
                default:
                    self::changeStatus($id, ['deleted_at' => now()]);
                    break;
            }
        }

        app(AuthLogger::class)->info('delete REQUEST status of '.implode(',', $ids));
    }

    private static function changeStatus($id, array $fields)
    {
        $log_id = is_int($id) ? $id : implode(',', $id);
        $field_str = json_encode($fields);
        app(AuthLogger::class)->info('update REQUEST status id '.$log_id.' to '.$field_str);

        return DB::client()->table('guest_requests')
            ->when(is_int($id), function ($query) use ($id) {
                return $query->where('id', $id);
            })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('id', $id);
            })
            ->update($fields);
    }

    public static function whitelistIp($clientIp)
    {

        $whitelistedIp = DB::client()->table('ips')->where('ip', $clientIp)->first();

        if ($whitelistedIp == null) {
            $whitelistedIp = DB::client()->table('ips')->insertGetId(['ip' => $clientIp, 'is_active' => true, 'updated_at' => now(), 'created_at' => now()]);
        } elseif (strcmp($whitelistedIp->is_active, true) == 0) {
            $whitelistedIp = $whitelistedIp->id;
        } else {
            throw ValidationException::withMessages(['message' => $clientIp.' is blocked']);
        }

        return $whitelistedIp;
    }

    public static function setUserIp($clientEmail, $clientIp)
    {
        $user = DB::client()->table('users')->where('email', $clientEmail)->first();

        if ($user == null) {
            throw ValidationException::withMessages(['message' => $clientEmail.' user does not exists']);
        }

        $clientIpId = self::whitelistIp($clientIp);

        DB::client()->table('user_ips')->insert([
            'user_id' => $user->id,
            'ip_id' => $clientIpId,
            'updated_at' => now(),
            'created_at' => now(),
        ]);

        app(AuthLogger::class)->info('whitelist ip '.$clientIp.' for client id '.$user->id);
    }
}
