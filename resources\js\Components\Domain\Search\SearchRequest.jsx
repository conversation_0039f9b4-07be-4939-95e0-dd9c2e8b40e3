import { useState } from "react";
import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import SearchTextArea from "@/Components/SearchTextArea";
import { useForm } from "@inertiajs/react";
import InputError from "@/Components/InputError";
import { getEventValue } from "@/Util/TargetInputEvent";
export default function SearchRequest() {
    const { data, setData, post, processing, errors, transform, reset } =
        useForm({
            domains: "",
            extensions: [],
        });

    
    const [ext, setExt] = useState({ com: true, org: true, net: true });

    const filterSelectedExtension = () => {
        let cloneExt = { ...ext };
        return Object.keys(cloneExt).filter((i, v) => {
            return ext[i] == true;
        });
    };

    const submit = (e) => {
        e.preventDefault();
        //needed to get latest update on extension
        transform((data) => ({
            ...data,
            extensions: [...filterSelectedExtension()],
        }));

        post(route('domain.check'));
        reset();
    };

    const onHandleChange = (event) => {
        setData(event.target.name, getEventValue(event));
    };

    const onhandleExtensionChange = (event) => {
        let cloneExt = { ...ext };
        cloneExt[event.target.name] = event.target.checked;
        setExt((e) => ({ ...e, ...cloneExt }));

        setData((e) => ({
            ...data,
            extensions: [...filterSelectedExtension()],
        }));
    };

    return (
        <form
            onSubmit={submit}
            className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-2"
        >
            <SearchTextArea
                maxLength={2000}
                rows={2}
                cols={50}
                name="domains"
                placeholder="name separated by new line , or space"
                value={data.domains}
                handleChange={onHandleChange}
            />
            <InputError message={errors.error} className="mt-2" />
            <InputError message={errors.domains} className="mt-2" />
            <div className="flex justify-between">
                <div className="flex space-x-5">
                    <label className="text-gray-700 text-sm flex items-center">
                        Domain Extension
                    </label>
                    {Object.keys(ext).map((name) => {
                        return (
                            <label
                                key={"ext-" + name}
                                className="flex items-center"
                            >
                                <Checkbox
                                    name={name}
                                    checked={ext[name]}
                                    handleChange={onhandleExtensionChange}
                                />

                                <span className="ml-2 text-sm text-gray-600 uppercase">
                                    {name}
                                </span>
                            </label>
                        );
                    })}
                </div>
                <PrimaryButton processing={processing}>Search</PrimaryButton>
            </div>
        </form>
    );
}
