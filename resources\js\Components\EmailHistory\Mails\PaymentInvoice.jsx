import React from "react";

export default function PaymentInvoice({ emailBody, links }) {
    const parsedData = JSON.parse(emailBody);
    const data = parsedData.data;
    const summaryData = data.summaryData;

    const domains = Array.isArray(data.data) ? data.data : [data];

    // console.log("parsedData", parsedData);
    // console.log("summaryData", summaryData);

    return (
        <div
            className="bg-slate-100 min-h-fit flex items-center justify-center"
            style={{
                fontFamily:
                    "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
                color: "#1a202c",
            }}
        >
            <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full text-slate-500">
                <h1 className="text-2xl font-bold mb-8">Payment Invoice</h1>
                <p className="font-bold mb-4">Sold to: {parsedData.name}</p>
                <div className="border-b mb-4"></div>

                <p className="mb-2">
                    Transaction ID: {summaryData.transaction_id}
                </p>
                <p className="mb-2">
                    Date:{" "}
                    {new Date(domains[0].created_at).toLocaleDateString(
                        "en-US",
                        {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                        }
                    )}
                </p>
                <p className="mb-10">Payment Method: Card</p>

                <table className="w-full mt-4 border-collapse">
                    <thead>
                        <tr className="border-b">
                            <th className="text-left py-2">Domain</th>
                            <th className="text-left py-2">Type</th>
                            <th className="text-left py-2">Duration</th>
                            <th className="text-left py-2">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {domains.map((domain, index) => (
                            <tr key={index}>
                                <td className="py-2">
                                    {parsedData.domain ?? domain.name ?? "-"}
                                </td>
                                <td className="py-2">
                                    {domain.node_type ?? "Transfer"}
                                </td>
                                <td className="py-2">
                                    {domain.year_length ?? 1}
                                </td>
                                <td className="py-2">
                                    $
                                    {parseFloat(
                                        domain.node_total_amount ??
                                            domain.total_amount ??
                                            0
                                    ).toFixed(2)}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>

                <div className="mt-4">
                    <p className="mb-2">
                        <strong>Total Amount:</strong> $
                        {parseFloat(
                            domains[0].invoice_total_amount ??
                                domains[0].total_amount ??
                                0
                        ).toFixed(2)}
                    </p>
                    {parsedData.icann_total && (
                        <p className="mb-2">
                            <strong>Total ICANN Fee:</strong> $
                            {parseFloat(parsedData.icann_total).toFixed(2)}
                        </p>
                    )}
                    <p className="mb-2">
                        <strong>Total Paid:</strong> $
                        {parseFloat(
                            domains[0].invoice_paid_amount ??
                                domains[0].paid_amount ??
                                0
                        ).toFixed(2)}
                    </p>
                </div>

                <p className="mt-4 text-sm">
                    For more information, please see StrangeDomains's{" "}
                    <a
                        href={links.TERMS_AND_CONDITIONS}
                        className="text-blue-600 underline"
                    >
                        Terms and Conditions
                    </a>
                    .
                </p>
            </div>
        </div>
    );
}
