//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { RiUserAddLine } from 'react-icons/ri';

//* COMPONENTS
import AdminLayout from '@/Layouts/AdminLayout'
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import NavLink from '@/Components/NavLink';
import UserManagementRoleViewPermissionsModalComponent from '@/Components/UserManagement/UserManagementRoleViewPermissionsModalComponent';

//* PARTIALS
import PartialUserManagementAdminTable from './Partials/PartialUserManagementAdminTable';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementAdminIndex(props)
{
    //! PACKAGE
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const paginationInfo =
    {
        onFirstPage    : props.data.onFirstPage,
        onLastPage     : props.data.onLastPage,
        nextPageUrl    : props.data.nextPageUrl,
        previousPageUrl: props.data.previousPageUrl, 
        itemCount      : props.data.itemCount, 
        total          : props.data.total
    };    

    //! STATES
    const [stateTableItems, setStateTableItems]                                     = useState(props.data.items);
    const [stateSelectedItems, setStateSelectedItems]                               = useState([]);
    const [stateSelectedItem, setStateSelectedItem]                                 = useState();
    const [stateIsActiveModalViewPermissions, setStateIsActiveModalViewPermissions] = useState(false);
    
     //! VARIABLES 
    const actionClass = 'h-5 w-5'; 

    const actions = 
    [
        {
            label           : 'Add User', 
            icon            : <RiUserAddLine className={actionClass} />,
            hasAccess       : hasPermission('user-management.admin.create') && hasPermission('user-management.admin.store'),
            isDisabled      : false, 
            handleEventClick: () =>
            {
                router.get(
                    route('user-management.admin.create'),
                )            
            } 
        },
    ];
    
    //! FUNCTIONS
    //... 

    //! USE EFFECTS
    //...

    return (
        <AdminLayout>
            <UserManagementRoleViewPermissionsModalComponent
                selectedItem={stateSelectedItem} 
                stateIsModalOpen={stateIsActiveModalViewPermissions}
                handleEventModalClose={() =>
                    {
                        setStateIsActiveModalViewPermissions(false);
                        setStateSelectedItem(null);
                    }
                }
            />
            <div
                className="mx-auto container max-w-[1200px] mt-5 flex flex-col gap-8 rounded-lg"
            >
                <div
                    className="flex justify-between"
                >
                    <div>
                        <div className='text-3xl font-semibold mb-3'>User Management</div>
                        <span className='text-gray-500 max-w-lg'>View & Manage Users</span>
                    </div>
                    <div
                        className='flex justify-between'
                    >
                        <div
                            className='flex justify-between items-center gap-4'
                        >
                            {
                                actions.filter(action => action.hasAccess)
                                    .map(
                                        (action, actionIndex) => 
                                        {
                                            return (
                                                <AppButtonComponent
                                                    key={actionIndex}
                                                    isDisabled={action.isDisabled}
                                                    handleEventClick={action.handleEventClick}
                                                >
                                                    {action.icon} 
                                                    <span
                                                        className='capitalize'
                                                    >
                                                        {action.label}
                                                    </span>
                                                </AppButtonComponent>
                                            )
                                        }
                                    )
                            }
                        </div>
                    </div>
                </div>

                <PartialUserManagementAdminTable
                    allItems={props.data.others.allItems}
                    stateTableItems={props.data.items}
                    setStateTableItems={setStateTableItems}
                    stateSelectedItems={stateSelectedItems}
                    setStateSelectedItems={setStateSelectedItems}
                    stateSelectedItem={stateSelectedItem}
                    setStateSelectedItem={setStateSelectedItem}
                    stateIsActiveModalViewPermissions={stateIsActiveModalViewPermissions}
                    setStateIsActiveModalViewPermissions={setStateIsActiveModalViewPermissions}
                    paginationInfo={paginationInfo}
                />
            </div>
        </AdminLayout>
    )
}
