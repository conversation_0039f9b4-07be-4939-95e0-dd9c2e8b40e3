import Tag from "../Tag";

export const StatusBadge = ({ status, requestStatus }) => {
    // Map request status to display text and colors
    const getStatusDisplay = () => {
        if (requestStatus) {
            switch (requestStatus) {
                case 'PENDING':
                    return { text: 'Pending', color: 'bg-cyan-500' };
                case 'APPROVED':
                    return { text: 'In Process', color: 'bg-yellow-400' };
                case 'REJECTED':
                    return { text: 'Rejected', color: 'bg-red-500' };
                case 'CANCELLED':
                    return { text: 'Cancelled', color: 'bg-orange-500' };
                case 'DELETED':
                    return { text: 'Deleted', color: 'bg-red-500' };
                default:
                    return { text: status, color: getDefaultColor(status) };
            }
        }
        return { text: status, color: getDefaultColor(status) };
    };

    const getDefaultColor = (status) => {
        const statusColors = {
            "Active": "bg-green-500",
            "In Process": "bg-yellow-400",
            "Pending": "bg-cyan-500",
            "Not Available": "bg-gray-400",
            "Expired": "bg-red-500",
            "Failed": "bg-red-500",
            "Deleted": "bg-red-500"
        };
        return statusColors[status] || "bg-gray-400";
    };

    const { text, color } = getStatusDisplay();

    return (
        <Tag show={true} name={text} bgColor={color} />
    );
}