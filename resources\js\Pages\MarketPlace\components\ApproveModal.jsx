import { evaluate } from '@/Util/AxiosResponseHandler';
import { getEventValue } from '@/Util/TargetInputEvent';
import { router, useForm } from '@inertiajs/react';
import React from 'react'
import { useEffect } from 'react';
import { useState } from 'react'
import { toast } from 'react-toastify';

export default function ApproveModal(props) {

    const {
        data,
        setData,
        setError,
        errors,
    } = useForm({
        code: "",
        item_id: ''
    });

    const onHandleChange = (event) => {
        setData(event.target.name, event.target.value);
        setData((data) => ({ ...data, item_id: props.item_id }));
    };

    const handleClose = () => {
        setError({message: ''});
        props.setShowApproveModal(false);
    }

    const confirm = async (e) => {
        e.preventDefault();
        if((data.code.trim()).length <= 0) return;

        setError({message: ''});

        let response = await axios
            .post(route("audit_check"), data)
            .then((response) => {
                return response;
            })
            .catch((error) => {
                return error.response;
            });

        response = evaluate(response);

        if(response.code == 403) {
            return setError({message: '403: Not enough permissions.'});
        } else if (response.errors) {
            return setError({message: response.errors.message});
        } 

        props.setTempDomains(() => {
            return props.tempDomains.map(item => {
                return item.id === props.item_id ? {...item, status: 'filed'} : item
            })
        });

        toast.success("Audit Filed.");
        props.setShowApproveModal(false);
    }

    useEffect(() => {
        setData((data) => ({ ...data, code: "", }));
    }, [props.show])

    return (
        <div className={`${props.show ? '' : 'hidden'}  fixed z-10 overflow-y-auto top-0 w-full left-0`} id="approvemodal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75" />
                </div>
                <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                <div className="inline-block align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                    <div className="bg-white px-4 pt-5">
                        <label className="font-medium text-gray-800">Enter Authorization Code</label>
                        <input type="text" name="code" className="w-full outline-none rounded p-2 mt-2 mb-3" value={data.code} onChange={(e) => onHandleChange(e) } />
                    </div>
                    <h6 className='pl-5 text-sm text-red-500'>{errors ? errors.message : 'hidden'}</h6>
                    <div className="px-4 py-3 pb-4 text-right">
                        <button type="button" className="py-2 px-4 bg-gray-500 text-white rounded hover:bg-gray-700 mr-2" onClick={(e) => { handleClose(e); }}><i className="fas fa-times"></i> Cancel </button>
                        <button disabled={data.code.trim().length <= 0 ? true : false} type="button" className="py-2 px-4 bg-blue-500 text-white rounded font-medium hover:bg-blue-700 mr-2 transition duration-500 disabled:bg-gray-500 disabled:text-white" onClick={(e) => { confirm(e); }}><i className="fas fa-plus"></i> Confirm </button>
                    </div>
                </div>
            </div>
        </div>
    )
}
