//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import InputError from '@/Components/InputError';
import UserManagementPermissionsReviewModalComponent from '@/Components/UserManagement/UserManagementPermissionsReviewModalComponent';
import UserManagementSetPermissionsTabComponent from '@/Components/UserManagement/UserManagementSetPermissionsTabComponent';

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserManagementAdminFormSetPermissions(
    {
        //! VARIABLES 
        mode       = 'create',
        activeTabs = [],
        activePermissions, 
        categories, 
        categoryPermissions, 
        roles, 

        //! STATES
        stateInputName,
        setStateInputName,
        stateInputEmail,
        setStateInputEmail,
        stateInputSelectedPermissions,
        setStateInputSelectedPermissions,
        stateSelectedRole,
        setStateSelectedRole,
        stateErrorMessageName,
        stateErrorMessageEmail,
        stateErrorMessageRole,
        stateErrorMessagePermissions,

        //! EVENTS
        handleEventSubmit    = () => alert('submit')
    }
)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    const [stateIsActiveModalReviewPermissions, setStateIsActiveModalReviewPermissions] = useState(false);
    const [stateActiveCategoryTabs, setStateActiveCategoryTabs]                         = useState(activeTabs);
    
    //! FUNCTIONS
    async function fetchRolePermissions()
    {
        try 
        {
            const response = await axios.post(
                    route('user-management.role.view-role-permissions'),
                    {
                        id: stateSelectedRole
                    }
                );
            
            if (response.status == 200)
            {
                setStateInputSelectedPermissions(response.data.map(p => p.id));

                setStateActiveCategoryTabs([]); 
                setStateActiveCategoryTabs(categories.map(category => category.name));
                setStateIsActiveModalReviewPermissions(true); 
            }
        }
        catch (error)
        {
            toast.error('Error Fetching Role Permissions')
        }   
    }

    function handleCheckPermission(permissionId)
    {
        if (stateInputSelectedPermissions.includes(permissionId))
        {
            setStateInputSelectedPermissions(stateInputSelectedPermissions.filter(permission => permission != permissionId));
        }
        else 
        {
            setStateInputSelectedPermissions([...stateInputSelectedPermissions, permissionId]);
        }
    };

    function handleCheckAllByCategory(categoryId, isChecked)
    {
        const permissions = categoryPermissions
            .filter(categoryPermission => categoryPermission.categoryId == categoryId)
            .map(categoryPermission => categoryPermission.permissionId);

        setStateInputSelectedPermissions(
            prev =>
            {
                if (isChecked)
                {
                    const newPermissions = permissions.filter(id => !prev.includes(id));

                    return [...prev, ...newPermissions];
                }
                else
                {
                    return prev.filter(id => !permissions.includes(id));
                }
            }
        );
    }

    function handleCheckAllPermissions(isChecked)
    {
        setStateInputSelectedPermissions([]);

        if (isChecked)
        {
            setStateInputSelectedPermissions(activePermissions.map(permission => permission.permissionId))
        }
    }

    function handleSelectRole(value)
    {
        setStateSelectedRole(value);
    }

    function handleToggleCategory(category)
    {
        if (stateActiveCategoryTabs.includes(category))
        {
            setStateActiveCategoryTabs(stateActiveCategoryTabs.filter(item => item != category));
        }
        else 
        {
            setStateActiveCategoryTabs([...stateActiveCategoryTabs, category]);
        }
    }

    const isFirstRender = useRef(true);

    //! USE EFFECTS
    useEffect(
        () => 
        {
            if (mode == 'update')
            {
                if (isFirstRender.current)
                {
                    isFirstRender.current = false;

                    return;
                }
            }
            
            if (stateSelectedRole)
            {
                fetchRolePermissions();
            }
        }, 
        [
            stateSelectedRole
        ]
    );
    
    return(
        <div
            className='flex flex-col gap-8'
        >
            <UserManagementPermissionsReviewModalComponent
                stateIsModalOpen={stateIsActiveModalReviewPermissions}
                selectedPermissions={
                    activePermissions
                        .filter(permission => stateInputSelectedPermissions.includes(permission.permissionId))
                        .map(permission => 
                            (
                                {
                                    id:permission.permissionId, name: permission.permissionName
                                }
                            )
                        )
                }
                handleEventModalClose={
                    () =>
                    {
                        setStateIsActiveModalReviewPermissions(false);
                    }
                }
                handleEventModalConfirm={
                    () => 
                    {
                        handleEventSubmit(); 
                        setStateIsActiveModalReviewPermissions(false);
                    }
                }
            />
            
            <section
                className='flex flex-col gap-4'
            >
                <div
                    className='flex flex-col gap-1'
                >
                    <label
                        className='font-semibold' htmlFor="inputName"
                    >
                        Name
                    </label>
                    <input
                        className='cursor-pointer  placeholder:text-gray-400 leading-tight !text-inherit border border-gray-300 focus:border-gray-500 rounded-lg focus:ring-0'
                        type="text"
                        value={stateInputName}
                        placeholder='John Doe' 
                        onChange={(e) => { setStateInputName(e.target.value) }}
                    />
                    <InputError
                        message={stateErrorMessageName}
                    />
                </div>
            
                <div
                    className='flex flex-col md:flex-row justify-between gap-4'
                >
                    <div
                        className='flex flex-col w-full gap-3'
                    >
                        <label
                            className='font-semibold'
                            htmlFor="inputEmail"
                        >
                            Email Address
                        </label>    
                        <input
                            id='inputEmail'
                            className='cursor-pointer  placeholder:text-gray-400  leading-tight !text-inherit border border-gray-300 focus:border-gray-500 rounded-lg focus:ring-0'
                            type="text"
                            value={stateInputEmail}
                            placeholder='<EMAIL>' 
                            onChange={(e) => { setStateInputEmail(e.target.value) }}
                        />
                        <InputError
                            message={stateErrorMessageEmail}
                        />
                    </div>
                    <div
                        className='flex flex-col gap-2 w-full'
                    >
                        <label
                            htmlFor="selectRole"
                            className='font-semibold'
                        >
                            Assign Role
                        </label>
                        <select
                            id='selectRole'
                            value={stateSelectedRole}
                            className={`${stateSelectedRole == 0 ? 'italic text-gray-400' : ''} text-gray-500 w-full border border-gray-300 focus:border-gray-500 rounded-lg focus:ring-0`}
                            onChange={(e) => handleSelectRole(e.target.value) }
                        >
                            <option
                                value="0"
                                className='not-italic'
                            >
                                None
                            </option>
                            {
                                roles.map(
                                    (role, index) =>
                                    {
                                        return(
                                            <option
                                                value={role.id}
                                                key={index}
                                                className='not-italic'
                                            >
                                                {role.name}
                                            </option>
                                        )
                                    }
                                )
                            }
                        </select>
                        <InputError
                            message={stateErrorMessageRole}
                        />
                    </div>
                </div>
            </section>

            <hr />

            <section
                className='flex flex-col gap-4'
            >
                <InputError
                    message={stateErrorMessagePermissions}
                />
                <div
                    className={`
                        grid grid-cols-1 gap-4  md:grid-cols-2 xl:grid-cols-3 items-center
                        border py-4 px-4 rounded-lg
                    `}
                >
                    <div 
                        className='text-lg col-span-2 font-semibold'
                    >
                        Permissions | {stateInputSelectedPermissions.length} selected
                    </div>
                    <AppInputCheckboxComponent
                        id={`permissionAll`}
                        name={`permissionAll}`}
                        label={'Select All'}
                        isChecked={activePermissions.every(p => stateInputSelectedPermissions.includes(p.permissionId))}
                        handleEventOnChange={(e) => handleCheckAllPermissions(e.target.checked)}
                    />
                </div>
                <div
                    className='flex flex-col gap-4'
                >
                    {
                        categories.map(
                            (category, categoryIndex) => 
                            {
                                return (
                                    <UserManagementSetPermissionsTabComponent
                                        key={categoryIndex}
                                        category={category} 
                                        categoryPermissions={categoryPermissions.filter(categoryPermission => categoryPermission.categoryId == category.id)}
                                        stateActiveCategoryTabs={stateActiveCategoryTabs}
                                        stateInputSelectedPermissions={stateInputSelectedPermissions}
                                        handleToggleCategory={handleToggleCategory} 
                                        handleCheckPermission={handleCheckPermission} 
                                        handleCheckAllByCategory={handleCheckAllByCategory}
                                    />
                                );
                            }
                        )
                    }
                </div>
            </section>
            
            <section
                className='flex justify-end'
            >
                <AppButtonComponent
                    type='button'
                    className='flex items-center gap-4  bg-primary text-white rounded-md px-4 py-2'
                    isDisabled={stateInputSelectedPermissions.length == 0}
                    handleEventClick={() => setStateIsActiveModalReviewPermissions(true)}
                >
                    Review
                </AppButtonComponent>
            </section>
        </div>
    )
}
