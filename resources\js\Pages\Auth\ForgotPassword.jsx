import GuestLayout from "@/Layouts/GuestLayout";
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";
import { Head, useForm, Link } from "@inertiajs/react";
import { getEventValue } from "@/Util/TargetInputEvent";

export default function ForgotPassword({ status }) {
    const { data, setData, post, processing, errors } = useForm({
        email: "",
    });

    const onHandleChange = (event) => {
        setData(event.target.name, getEventValue(event));
    };

    const submit = (e) => {
        e.preventDefault();

        post(route("password.email"));
    };

    return (
        <GuestLayout>
            <Head title="Forgot Password" />
            {status ? 
                <div className="mt-6 font-medium text-sm text-green-600 text-center">
                    {status}
                </div> :
                <>  
                    <div className="mb-4 mt-6 text-sm text-gray-600">
                        Forgot your password? No problem. Just let us know your email
                        address and we will email you a password reset link that will
                        allow you to choose a new one.
                    </div>
                    <form onSubmit={submit}>
                        <TextInput
                            id="email"
                            type="text"
                            name="email"
                            placeholder="Enter your email"
                            value={data.email}
                            className="mt-1 block w-full"
                            isFocused={true}
                            handleChange={onHandleChange}
                        />

                        <InputError message={errors.email} className="mt-2" />

                        
                        <div className="flex flex-col items-center mt-6">
                            <PrimaryButton className="w-full" disabled={processing}>
                                Reset Password
                            </PrimaryButton>
                        </div>
                    </form>
                </>
            }
            <div className="flex flex-col items-center mt-4">
                <Link
                    href={route("home")}
                    className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                    Go back to Home
                </Link>
            </div>
        </GuestLayout>
    );
}
