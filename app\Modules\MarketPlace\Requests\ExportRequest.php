<?php

namespace App\Modules\MarketPlace\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ExportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'file' => 'required'
        ];
    }

    public function export() : BinaryFileResponse
    {
        $file = $this->file;
        return response()->download(storage_path("app\audits\\$file.csv"));
    }
}
