import React, { forwardRef, useEffect, useRef } from "react";

export default forwardRef(function SearchTextArea(
    {
        type = "text",
        name,
        value,
        className,
        autoComplete,
        required,
        isFocused,
        handleChange,
        placeholder,
        rows = 2,
        cols = 50,
        maxLength = 500,
    },
    ref
) {
    const input = ref ? ref : useRef();

    useEffect(() => {
        if (isFocused) {
            input.current.focus();
        }
    }, []);

    return (
        <textarea
            maxLength={maxLength}
            placeholder={placeholder}
            rows={rows}
            cols={cols}
            type={type}
            name={name}
            value={value}
            className={
                `border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-sm shadow-sm placeholder-gray-200 pl-6 ` +
                className
            }
            ref={input}
            autoComplete={autoComplete}
            required={required}
            onChange={(e) => handleChange(e)}
        />
    );
});
