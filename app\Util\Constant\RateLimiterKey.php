<?php

namespace App\Util\Constant;

final class RateLimiterKey
{
    public const AUTHENTICATE_REQUEST = 'AUTHENTICATE_REQUEST';

    public const FORGOT_PASSWORD_REQUEST = 'FORGOT_PASSWORD_REQUEST';

    public const RESET_PASSWORD_ACCESS = 'RESET_PASSWORD_ACCESS';

    public static function authAttempt(int $userId)
    {
        return self::AUTHENTICATE_REQUEST.'_'.$userId;
    }

    public static function forgotPasswordAttempt(string $userIP)
    {
        return self::FORGOT_PASSWORD_REQUEST.'_'.$userIP;
    }

    public static function resetPasswordAttempt(string $userIP)
    {
        return self::RESET_PASSWORD_ACCESS.'_'.$userIP;
    }
}
