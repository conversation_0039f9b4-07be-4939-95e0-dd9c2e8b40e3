import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";
import { router } from "@inertiajs/react";
import { useRef, useState } from "react";

import {
    MdMoreVert,
    MdOutlineStopCircle,
    MdOutlineCheckCircleOutline,
} from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";
import { toast } from "react-toastify";
export default function Item({ item, isSelected, onCheckboxChange }) {
    const [show, setShow] = useState(false);
    const ref = useRef();
    const responseStatus = { allow: "ACTIVE", block: "BLOCKED" };

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, item.status, getEventValue(e));
    };

    const handleResponse = (status) => {
        setShow(false);
        toast.info("Updating IP Status", );
        router.patch(route("ip.update-status"), {
            ids: [item.id],
            is_active: status,
        });
    };

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <Checkbox
                        name="ips"
                        value="ips"
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    />

                    <span className=" font-semibold cursor-pointer">
                        {item.ip}
                    </span>
                </label>
            </td>
            <td>
                {item.is_active ? (
                    <MdOutlineCheckCircleOutline className="text-success text-lg" />
                ) : (
                    <MdOutlineStopCircle className="text-gray-400 text-lg" />
                )}
            </td>

            <td>
                <span>{item.updated_at}</span>
            </td>
            <td>
                <span>{item.created_at}</span>
            </td>

            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        {!item.is_active ? (
                            <button
                                className="hover:bg-gray-100 px-5 py-1 "
                                onClick={() => handleResponse(1)}
                            >
                                Allow
                            </button>
                        ) : (
                            <button
                                className="hover:bg-gray-100 px-5 py-1 "
                                onClick={() => handleResponse(0)}
                            >
                                Block
                            </button>
                        )}
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
