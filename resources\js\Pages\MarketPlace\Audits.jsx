import AdminLayout from '@/Layouts/AdminLayout'
import React from 'react'
import { useState } from 'react';
import DataTable from 'react-data-table-component'
import { customStyles } from './components/customStyles';
import { BsThreeDotsVertical } from "react-icons/bs";
import { PiWarningOctagonLight } from "react-icons/pi";
import { CiGlobe } from "react-icons/ci";
import { MdOutlineFileDownload } from "react-icons/md";
import ApproveModal from './components/ApproveModal';
import { toast } from 'react-toastify';
import { evaluate } from '@/Util/AxiosResponseHandler';

export default function Audits(props) {

    const [page, setPage] = useState(0);
    const [domains, setDomains] = useState();
    const [perPage, setperPage] = useState(10);
    const [totalRows, settotalRows] = useState(0);
    const [selectedID, setselectedID] = useState();

    const [showApproveModal, setShowApproveModal] = useState(false);

    const [tempDomains, setTempDomains] = useState(props.audits);

    const getStatus = (status) => {
        let color = 'bg-red-300';

        if(status == 'pending verification') color = `bg-yellow-500`
        else if(status == 'filed') color = `bg-green-500`

        return <div className='flex'>
            <span className={`w-2 h-2 mt-1 mr-2 rounded-full ${color}`}> </span>
            <span className='capitalize'>{status}</span>
        </div>
    }

    const handlePageChange = async (npage) => {
        setPage(npage)
    };

    const handlePerRowsChange = async (newPerPage, page) => {
        setperPage(newPerPage)
    };

    const getfileName = (date1, date2) => {
        let d1 = new Date(date1);
        let d2 = new Date(date2);

        let m1 = d1.toLocaleString('default', { month: 'short' });
        let m2 = d2.toLocaleString('default', { month: 'short' });

        let dd1 = String(d1.getDate()).padStart(2, `0`);
        let dd2 = String(d2.getDate()).padStart(2, `0`);

        return `${m1}-${dd1}-${d1.getFullYear()}_${m2}-${dd2}-${d2.getFullYear()}`;
    }

    const getAction = (row, id) => {
        return <div className='relative group' style={{ textAlign: "right" }}>
            <button>
                <BsThreeDotsVertical />
            </button>
            <div className="absolute invisible group-focus-within:visible right-0 z-10 p-3 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabIndex="-1">
                <button onClick={(e) => { showConfirm(id); e.target.blur(); }}  id='confirm' className="flex hover:bg-[#E8F3F7] hover:text-primary rounded-md w-full" role="none">
                    <span className='pt-[7px] pl-2'><PiWarningOctagonLight className='w-6 h-6' /></span>
                    <span className="font-semibold block py-2 pl-2 pt-[12px] text-[12px]" role="menuitem" tabIndex="-1" id="menu-item-1">
                        Confirm
                    </span>
                </button>
                <button onClick={(e) => { resendEmail(row, id); e.target.blur(); }}  id='resend' className="flex hover:bg-[#E8F3F7] hover:text-primary rounded-md w-full" role="none">
                    <span className='pt-[7px] pl-2'><CiGlobe className='w-6 h-6' /></span>
                    <span className="font-semibold block py-2 pl-2 pt-[12px] text-[12px]" role="menuitem" tabIndex="-1" id="menu-item-2">
                        Resend Verification
                    </span>
                </button>
                <button onClick={(e) => { exportCSV(row); e.target.blur(); }}  id='export' className="flex hover:bg-[#E8F3F7] hover:text-primary rounded-md w-full" role="none">
                    <span className='pt-[7px] pl-2'><MdOutlineFileDownload className='w-6 h-6' /></span>
                    <span className="font-semibold block py-2 pl-2 pt-[12px] text-[12px]" role="menuitem" tabIndex="-1" id="menu-item-3">
                        Export
                    </span>
                </button>
            </div>
        </div>
    }

    const resendEmail = async (row, id) => {
        let file = getfileName(row.order_started_at, row.order_ended_at)

        let response = await axios
            .post(route("audit_resend_verification"), {
                id: id,
                file: file
            })
            .then((response) => {
                return response;
            })
            .catch((error) => {
                return error.response;
            });

        response = evaluate(response);

        if(response.code == 403) toast.error('403: Not Enough Permissions.');
        else if (response.errors) return toast.error(response.errors.message);
        else toast.success("Success");
    }

    const showConfirm = (id) => {
        setselectedID(id);
        setShowApproveModal(true);
    }

    const exportCSV = (row) => {
        let filename = getfileName(row.order_started_at, row.order_ended_at)

        const url = route('audit_export', { file: filename })
        window.location.href = url

        toast.success("Exported");
    }

    const columns = [
        {
            id: 'File',
            name: 'File',
            selector: row => getfileName(row.order_started_at, row.order_ended_at),
            cell: row => <div className='font-bold'>{getfileName(row.order_started_at, row.order_ended_at)}</div>,
            sortable: true,
            width: '250px'
        },
        {
            id: 'Domain',
            name: 'Domain',
            selector: row => parseInt(row.total_domain),
            sortable: true,
            width: '101px'
        },
        {
            id: 'Gross',
            name: 'Gross',
            selector: row => parseInt(row.total_price / 1000000),
            cell: row => `$${row.total_price / 1000000}`,
            sortable: true,
        },
        {
            id: 'Commission',
            name: 'Commission',
            selector: row => parseInt(row.total_commission / 1000000),
            cell: row => `$${row.total_commission / 1000000}`,
            sortable: true,
        },
        {
            id: 'Net',
            name: 'Net',
            selector: row => parseInt((row.total_price - row.total_commission) / 1000000),
            cell: row => `$${(row.total_price - row.total_commission) / 1000000}`,
            sortable: true,
        },
        {
            id: 'Status',
            name: 'Status',
            selector: row => row.status,
            cell: row => getStatus(row.status),
            sortable: true,
            width: '200px'
        },
        {
            id: "Actions",
            name: 'Actions',
            selector: row => row.id,
            cell: row => getAction(row, row.id),
            width: '90px',
        },
    ];

    return (
        <AdminLayout>
            <ApproveModal show={showApproveModal} setShowApproveModal={setShowApproveModal} item_id={selectedID} tempDomains={tempDomains} setTempDomains={setTempDomains}></ApproveModal>
            <div className='mx-auto container max-w-[1200px] mt-20 flex flex-col px-5 rounded-lg '>
                <DataTable
                    columns={columns}
                    data={tempDomains}
                    pagination
                    selectableRows
                    persistTableHead
                    highlightOnHover
                    customStyles={customStyles}
                    pointerOnHover
                    fixedHeader
                    paginationServer
                    paginationTotalRows={totalRows}
                    paginationDefaultPage={page}
                    onChangeRowsPerPage={handlePerRowsChange}
                    onChangePage={handlePageChange}
                    style={{minHeight: '100vh'}}
                    fixedHeaderScrollHeight="600px"
                    paginationRowsPerPageOptions={[10,25,50,100,250]}
                />
            </div>
        </AdminLayout>
    )
}
