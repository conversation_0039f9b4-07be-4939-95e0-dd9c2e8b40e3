//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useForm } from '@inertiajs/react';
import { Transition } from '@headlessui/react';

//* ICONS
import { AiFillEyeInvisible, AiOutlineEye } from "react-icons/ai";

//* COMPONENTS
import AppPasswordStrengthIndicatorComponent from '@/Components/App/AppPasswordStrengthIndicatorComponent';
import InputError from '@/Components/InputError';
import InputLabel from '@/Components/InputLabel';
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UpdatePasswordForm(
    {
        className = ''
    }
)
{
    //! PACKAGE
    const passwordInput        = useRef();
    const currentPasswordInput = useRef();
    
    const { data, setData, errors, put, reset, clearErrors, processing, recentlySuccessful } = useForm(
        {
            currentPassword      : '',
            password             : '',
            password_confirmation: '',
        }
    );
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [showInputPassword, setShowInputPassword]               = useState(false);
    const [showInputConfirmPassword, setShowInputConfirmPassword] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const updatePassword = (e) =>
    {
        e.preventDefault();

        clearErrors(); 

        put(
            route('password.update'),
            {
                preserveScroll: true,
                onSuccess     : () => reset(),
                onError       : (errors) =>
                {
                    console.log(errors);
                    if (errors.password)
                    {
                        //reset('password', 'password_confirmation');
                        passwordInput.current.focus();
                    }

                    if (errors.currentPassword)
                    {
                        //reset('currentPassword');
                        currentPasswordInput.current.focus();
                    }
                },
            }
        );
    };

    return (
        <section className={className}>
            <header>
                <h2 className="text-lg font-medium text-gray-900">Update Password</h2>

                <p className="mt-1 text-sm text-gray-600">
                    Ensure your account is using a long, random password to stay secure.
                </p>
            </header>

            <form
                onSubmit={updatePassword}
                className="mt-6 space-y-6"
            >
                <div
                    className='flex flex-col gap-2'
                >
                    <InputLabel htmlFor="currentPassword" value="Current Password" />

                    <TextInput
                        id="currentPassword"
                        ref={currentPasswordInput}
                        value={data.currentPassword}
                        placeholder="P@ssword25"
                        type="password"
                        className="block w-full"
                        autoComplete="current-password"
                        handleChange={(e) => setData('currentPassword', e.target.value)}
                    />

                    <InputError
                        message={errors.currentPassword}
                    />
                </div>

                <div
                    className='flex flex-col gap-2'
                >
                    <InputLabel htmlFor="password" value="New Password" />

                    <div
                        className='relative'
                    >
                        <TextInput
                            id="password"
                            type={showInputPassword ? "text" : "password"}
                            ref={passwordInput}
                            name="password"
                            value={data.password}
                            placeholder="P@ssword25"
                            className="w-full"
                            autoComplete="new-password"
                            handleChange={(e) => setData('password', e.target.value)}
                        />
                        <div
                            className="absolute right-0 top-1/2 -translate-y-1/2 pr-5"
                        >
                            <i
                                onClick={() => setShowInputPassword(!showInputPassword)}
                                className="text-xl cursor-pointer hover:text-primary duration-100 ease-in"
                            >
                                {showInputPassword ? (
                                    <AiFillEyeInvisible />
                                ) : (
                                    <AiOutlineEye />
                                )}
                            </i>
                        </div>
                    </div>
                    {
                        errors.password != null
                            ?
                                <InputError message={errors.password} />
                            :
                            (
                                <span className='text-xs text-gray-400 [&::first-letter]:uppercase'>
                                    Password must contain at least 8 characters, including 1 digit, 1 uppercase letter, 1 lowercase letter & 1 special character.
                                </span>                                               
                            )
                    }
                    {
                        data.password.length > 0 
                            ?
                                <AppPasswordStrengthIndicatorComponent
                                    password={data.password}
                                />
                            :
                                null 
                    }
                </div>

                <div
                    className='flex flex-col gap-2'
                >
                    <InputLabel
                        htmlFor="password_confirmation"
                        value="Confirm Password"
                    />

                    <div
                        className='relative'
                    >
                        <TextInput
                            id="password_confirmation"
                            type={showInputConfirmPassword ? "text" : "password"}
                            name="confirm_password"
                            value={data.password_confirmation}
                            placeholder="P@ssword25"
                            className="w-full"
                            autoComplete="new-password"
                            handleChange={(e) => setData('password_confirmation', e.target.value)}
                        />
                        <div
                            className="absolute right-0 top-1/2 -translate-y-1/2 pr-5"
                        >
                            <i
                                onClick={() => setShowInputConfirmPassword(!showInputConfirmPassword)}
                                className="text-xl cursor-pointer hover:text-primary duration-100 ease-in"
                            >
                                {showInputConfirmPassword ? (
                                    <AiFillEyeInvisible />
                                ) : (
                                    <AiOutlineEye />
                                )}
                            </i>
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-4">
                    <PrimaryButton disabled={processing}>Save</PrimaryButton>

                    <Transition
                        show={recentlySuccessful}
                        enterFrom="opacity-0"
                        leaveTo="opacity-0"
                        className="transition ease-in-out"
                    >
                        <p className="text-sm text-gray-600">Saved.</p>
                    </Transition>
                </div>
            </form>
        </section>
    );
}
