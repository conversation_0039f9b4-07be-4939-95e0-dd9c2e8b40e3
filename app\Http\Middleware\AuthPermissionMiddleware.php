<?php

namespace App\Http\Middleware;

use Closure;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

use Symfony\Component\HttpFoundation\Response;

class AuthPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) 
        {
            abort(401);
        }

        //! Allow Super Admins
        // if ($user->is_super_admin ?? false) 
        // {
        //     return $next($request);
        // }

        //! Get current route name
        $routeName = $request->route()->getName();

        if (!$routeName) 
        {
            abort(404);
        }

        //! Retrieve user permissions from cache
        $permissions = Cache::remember(
            "user_permissions_{$user->id}",
            60,
            function () use ($user) {
                return DB::table('admin_access')
                    ->select('access.name')
                    ->join('access', 'admin_access.access_id', '=', 'access.id')
                    ->where('admin_access.admin_id', $user->id)
                    ->pluck('access.name')
                    ->toArray();
            }
        );

        if (!in_array($routeName, $permissions)) 
        {
            abort(403, 'You do not have permissions to access this page');
        }

        return $next($request);
    }
}
