import PrimaryButton from "@/Components/PrimaryButton";
import { router, Link } from "@inertiajs/react";

export default function EmptyCart() {
    return (
        <div>
            <div className="mx-auto container max-w-[900px] text-center pt-14 flex justify-center items-center flex-wrap">
                <div className="w-auto h-[10rem]">
                    <img
                        className="h-full w-auto"
                        src="/assets/images/empty.svg"
                        alt="background"
                    />
                </div>

                <span className=" text-lg font-semibold ">
                    Your cart is empty
                </span>
            </div>
            <div className="w-full text-center flex flex-col  items-center pt-12 space-y-7">
                <PrimaryButton
                    className=" w-fit"
                    onClick={() => router.get(route("domain.search"))}
                >
                    Search for domain
                </PrimaryButton>
                <Link
                    href={route("domain")}
                    className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none"
                >
                    My domain
                </Link>
            </div>
        </div>
    );
}
