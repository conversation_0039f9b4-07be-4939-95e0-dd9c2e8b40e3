//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { MdKeyboardBackspace } from 'react-icons/md';

//* COMPONENTS
import AdminLayout from '@/Layouts/AdminLayout'

//* PARTIALS
import PartialUserManagementRoleFormSetPermissions from './Partials/PartialUserManagementRoleFormSetPermissions';

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementRoleUpdate(props)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    const allPermissions      = props.allPermissions;
    const activePermissions   = props.activePermissions;
    const categories          = props.categories;
    const categoryPermissions = props.categoryPermissions

    //! STATES
    const [stateInputName, setStateInputName]                               = useState(props.role.name);
    const [stateInputSelectedPermissions, setStateInputSelectedPermissions] = useState([]);
    const [stateErrorMessageName, setStateErrorMessageName]                 = useState(null);
    const [stateErrorMessagePermissions, setStateErrorMessagePermissions]   = useState(null);
    const [statePermissions, setStatePermissions]                           = useState([]);
    const [stateShouldSyncPermissions, setStateShouldSyncPermissions]       = useState(false);
    
    //! USE EFFECTS
    useEffect(
        () =>
        {
            const newIds = props.assignedPermissions.map(permission => permission.id);
        
            setStateInputSelectedPermissions(
                (prev) =>
                {
                    const combined = [...prev, ...newIds];
                    const unique = Array.from(new Set(combined));

                    return unique;
                }
            );
        },
        []
    );

    //! FUNCTIONS
    async function handleSubmit()
    {
        setStateErrorMessageName(null)
        setStateErrorMessagePermissions(null)
    
        router.post(
                route("user-management.role.update", {id : props.role.id}),
            {
                name           : stateInputName,
                permissions    : stateInputSelectedPermissions,
                syncPermissions: stateShouldSyncPermissions
            }, 
            {
                onError: (error) => 
                {
                    setStateErrorMessageName(error.name ?? null)
                    setStateErrorMessagePermissions(error.permissions ?? null)
                }
            }
        )
    }

    return (
        <AdminLayout>
            <div
                className="mx-auto container max-w-[1200px] my-5 flex flex-col first-letter:rounded-lg"
            >
                <div className="mb-[10px] flex items-center text-lg font-semibold">
                    <Link
                        href={route("user-management.role")}
                        className=" hover:!shadow-none flex pl-0"
                    >
                        <MdKeyboardBackspace className="text-3xl rounded-full p-1 cursor-pointer hover:text-gray-700 text-gray-700" />
                        <span className="hover:text-gray-700 text-gray-700 pt-[1px] pl-1"> Back to Roles </span>
                    </Link>
                </div>

                <div className="header">
                    <div className='text-3xl font-semibold mb-4'>
                        Update Role
                    </div>
                </div>

                <PartialUserManagementRoleFormSetPermissions
                    formMode={'update'}
                    categories={categories}
                    activePermissions={activePermissions}
                    allPermissions={allPermissions}
                    categoryPermissions={categoryPermissions}
                    stateInputName={stateInputName}
                    setStateInputName={setStateInputName}
                    stateInputSelectedPermissions={stateInputSelectedPermissions}
                    setStateInputSelectedPermissions={setStateInputSelectedPermissions}
                    stateErrorMessageName={stateErrorMessageName}
                    stateErrorMessagePermissions={stateErrorMessagePermissions}
                    stateShouldSyncPermissions={stateShouldSyncPermissions}
                    setStateShouldSyncPermissions={setStateShouldSyncPermissions}
                    handleEventSubmit={handleSubmit}
                />
            </div>
        </AdminLayout>
    )
}
