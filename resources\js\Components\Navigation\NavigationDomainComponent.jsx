//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    Md<PERSON><PERSON><PERSON>,
    MdOutlineHistory,
    MdExpandLess,
    MdExpandMore,
    MdAutoDelete,
    MdOutbound
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationDomainComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        history      : route().current("domain.history"),
        pendingDelete: route().current("domain.pending-delete.view"),
        requestDelete: route().current("domain.delete-request.view"),
        domainRedemption: route().current("domain-redemption.view"),
        transfer     : route().current("transfer.view"),
    };

    const links = 
    [
        {
            routeName: 'domain.history',
            hasAccess: hasPermission('domain.history'),
            isActive : routes.history,
            icon     : <MdOutlineHistory className="text-2xl"/>,
            label    : 'domains'
        }, 
        {
            routeName: 'domain.pending-delete.view',
            hasAccess: hasPermission('domain.pending-delete.view'),
            isActive : routes.pendingDelete,
            icon     : <MdDelete className="text-2xl"/>,
            label    : 'expired'
        }, 
        {
            routeName: 'domain.delete-request.view',
            hasAccess: hasPermission('domain.delete-request.view'),
            isActive : routes.requestDelete,
            icon     : <MdDelete className="text-2xl"/>,
            label    : 'request delete'
        }, 
        {
            routeName: 'domain-redemption.view',
            hasAccess: hasPermission('domain-redemption.view'),
            isActive : routes.domainRedemption,
            icon     : <MdAutoDelete className="text-2xl ml-1"/>,
            label    : 'deleted'
        },
        {
            routeName: 'transfer.view',
            hasAccess: hasPermission('transfer.view'),
            isActive : routes.transfer,
            icon     : <MdOutbound className="text-2xl"/>,
            label    : 'transfer'
        }
    ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () =>
    {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span
                    className=" text-inherit"
                >
                    Domain
                </span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
