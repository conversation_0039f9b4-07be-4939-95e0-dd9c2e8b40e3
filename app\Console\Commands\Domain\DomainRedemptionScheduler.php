<?php

namespace App\Console\Commands\Domain;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\PendingDelete\Services\RedemptionPeriodQueryService;
use Exception;
use Illuminate\Console\Command;

class DomainRedemptionScheduler extends Command
{
    private $redemptionDays = 46;

    /**
     * Toggle this to true/false to control execution
     * 
     * @var bool
     */
    private $shouldRun = false; // set to false to skip execution

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:domain-redemption-scheduler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically delete domains that have been expired for 46+ days';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            if (!$this->shouldRun) {
                app(AuthLogger::class)->info('DomainRedemptionScheduler: Skipped by configuration.');
                return;
            }

            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainRedemptionScheduler: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('DomainRedemptionScheduler: Running...');

        RedemptionPeriodQueryService::instance()->processExpiredDomains($this->redemptionDays);

        app(AuthLogger::class)->info('DomainRedemptionScheduler: Done');
    }
}
