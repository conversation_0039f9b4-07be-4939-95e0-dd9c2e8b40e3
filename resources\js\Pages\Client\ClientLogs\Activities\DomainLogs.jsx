//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router, usePage } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { IoMdArrowBack } from "react-icons/io";
import { Md<PERSON><PERSON><PERSON><PERSON>ist, Md<PERSON>oreVert } from "react-icons/md";
import Filter from "@/Components/Client/ClientLogs/DomainLogs/Filter";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import ActivitiesNav from "@/Components/Client/ClientLogs/ActivitiesNav";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import DropDownContainer from "@/Components/DropDownContainer";
import Payload from "@/Components/Client/ClientLogs/DomainLogs/Payload";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function DomainLogs()
{
    //! PACKAGE
    const {
        logs = [],
        email,
        user_id,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount,
        total,
        filters,
        statuses,
    } = usePage().props;
    
    const { limit = 20 } = route().params ?? {};

    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [isLoading, setIsLoading] = useState(false);
    const [activeDropdown, setActiveDropdown] = useState(null);
    const [payloadData, setPayloadData] = useState(null);
    const [showPayloadModal, setShowPayloadModal] = useState(false);
    const [loadingPayload, setLoadingPayload] = useState(false);

    //! USE EFFECTS
    useEffect(() => {
        function handleClickOutside(event) {
            if (activeDropdown !== null && 
                !event.target.closest('.action-button') && 
                !event.target.closest('.dropdown-content')) {
                setActiveDropdown(null);
            }
        }
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [activeDropdown]);

    //! FUNCTIONS
    const onBackClick = () => {
        window.history.back();
    };

    const groupedLogs = logs.reduce((acc, log) => {
        const date = log.formatted_created_at;
        if (!acc[date]) {
            acc[date] = [];
        }
        acc[date].push(log);
        return acc;
    }, {});


    const toggleDropdown = (logId) => {
        setActiveDropdown(activeDropdown === logId ? undefined : logId);
    };

    const handleView = (logId) => {
        setLoadingPayload(true);
        axios.get(route("client.logs.domain.view", logId))
            .then(response => {
                console.log("Payload response:", response.data);
                const logData = logs.find(log => log.id === logId);
                setPayloadData({
                    ...logData,
                    ...response.data.payload
                });
                setShowPayloadModal(true);
                setLoadingPayload(false);
            })
            .catch(error => {
                console.error("Failed to load payload data:", error);
                setLoadingPayload(false);
                toast.error("Failed to load payload data");
            });
    };

    const closePayloadModal = () => {
        setShowPayloadModal(false);
        setPayloadData(null);
    };

    const LoadingSkeleton = () => (
        <div className="animate-pulse">
            {[1, 2, 3].map((group) => (
                <div key={group} className="mb-8">
                    <div className="flex items-center mb-4">
                        <div className="bg-gray-200 rounded-md w-32 h-8"></div>
                        <div className="flex-grow border-t border-gray-200 ml-2"></div>
                    </div>
                    <div className="bg-white mb-4">
                        {[1, 2, 3].map((row) => (
                            <div
                                key={row}
                                className="flex border-b border-gray-100 py-4"
                            >
                                <div className="w-1/4 px-4">
                                    <div className="w-32 h-4 bg-gray-200 rounded"></div>
                                </div>
                                <div className="w-1/4 px-4">
                                    <div className="w-40 h-4 bg-gray-200 rounded"></div>
                                </div>
                                <div className="w-1/4 px-4 text-center">
                                    <div className="w-16 h-4 bg-gray-200 rounded mx-auto"></div>
                                </div>
                                <div className="w-1/4 px-4">
                                    <div className="w-20 h-4 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );

    const handleLimitChange = (e) => {
        router.get(route("client.logs.domain.all"), {
            ...route().params,
            limit: e.target.value,
            page: 1,
        });
    };

    return (
        <AdminLayout>
            <div className="flex flex-col">
                <div className="flex items-center space-x-2 mb-4 pl-6">
                    <button onClick={onBackClick} className="text-2xl pt-1">
                        <IoMdArrowBack />
                    </button>
                    <h2 className="text-2xl font-bold">
                        {user_id
                            ? `Client Domain Logs for: ${email}`
                            : "All Client Activity Logs"}
                    </h2>
                </div>
                <div className="flex flex-col md:flex-row">
                    <ActivitiesNav activeTab="DomainLogs" user_id={user_id} />
                    <div className="px-4 w-full md:w-5/6">
                        <div className="mx-auto container max-w-[1200px] mt-4 flex flex-col justify-between">
                            <h2 className="text-4xl font-bold">Domain Logs</h2>
                            <div
                                className="flex justify-start"
                                style={{ position: "relative", top: "15px" }}
                            >
                                <label className="mr-2 text-sm pt-1 text-gray-600">
                                    Show
                                </label>
                                <select
                                    value={limit}
                                    onChange={handleLimitChange}
                                    className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                                >
                                    {[20, 25, 30, 40, 50, 100].map((val) => (
                                        <option key={val} value={val}>
                                            {val}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="flex justify-between items-center mt-4 pt-4 pb-4">
                                <div className="flex items-center space-x-2">
                                    <MdFilterList className="text-xl" />
                                    <span>Filter:</span>
                                    <Filter
                                        routeName={
                                            user_id
                                                ? "client.logs.domain"
                                                : "client.logs.domain.all"
                                        }
                                        userId={user_id}
                                        userEmail={email}
                                        onLoading={setIsLoading}
                                    />
                                </div>
                            </div>
                            <div className="mt-4">
                                {isLoading || loadingPayload ? (
                                    <LoadingSkeleton />
                                ) : (
                                    Object.entries(groupedLogs).map(
                                        ([date, dateLogs]) => (
                                            <React.Fragment key={date}>
                                                <div className="flex items-center">
                                                    <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                                        {date}
                                                    </div>
                                                    <div className="flex-grow border-t border-gray-300 ml-2"></div>
                                                </div>
                                                <table className="min-w-full bg-white mb-4">
                                                    <tbody className="text-md">
                                                        {dateLogs.map(
                                                            (log, index) => (
                                                                <tr key={index}>
                                                                    <td className="w-1/5 py-3 px-4">
                                                                        {log.domain_name}
                                                                    </td>
                                                                    <td className="w-2/5 py-3 px-4 whitespace-normal break-words align-top">
                                                                        {log.message ? (
                                                                            <span data-testid="log-message">
                                                                                {log.message}
                                                                            </span>
                                                                        ) : (
                                                                            <span className="text-gray-400">No message available</span>
                                                                        )}
                                                                    </td>
                                                                    <td className="w-1/6 py-3 px-4 pr-6 text-center">
                                                                        <span
                                                                            className={`inline-block mr-1 px-2 py-1 font-semibold rounded-full text-white text-xs ${
                                                                                log.status === "failed"
                                                                                    ? "bg-red-500"
                                                                                    : "bg-green-500"
                                                                            }`}
                                                                        >
                                                                            {log.status === "failed"
                                                                                ? "Failed"
                                                                                : "Success"}
                                                                        </span>
                                                                    </td>
                                                                    <td className="w-1/6 py-3 px-4 text-left">
                                                                        {new Date(
                                                                            log.created_at
                                                                        ).toLocaleTimeString(
                                                                            "en-US",
                                                                            {
                                                                                hour: "2-digit",
                                                                                minute: "2-digit",
                                                                                hour12: true,
                                                                            }
                                                                        )}
                                                                    </td>
                                                                    <td className="py-3 px-4" style={{width: '1%'}}>
                                                                        <div className="relative">
                                                                            <button
                                                                                className="flex items-center action-button"
                                                                                onClick={() => {
                                                                                    toggleDropdown(log.id);
                                                                                }}
                                                                            >
                                                                                <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                                                                            </button>
                                                                            {activeDropdown === log.id && (
                                                                                <div className="dropdown-content">
                                                                                    <DropDownContainer show={true}>
                                                                                        {
                                                                                            hasPermission('client.logs.domain.view') 
                                                                                                ?
                                                                                                    <button
                                                                                                        className="hover:bg-gray-100 px-5 py-1"
                                                                                                        onClick={() => handleView(log.id)}
                                                                                                    >
                                                                                                        View Payload
                                                                                                    </button>
                                                                                                :
                                                                                                    <div
                                                                                                        className='font-semibold capitalize text-danger px-5 py-1'
                                                                                                    >
                                                                                                        no actions permitted
                                                                                                    </div>
                                                                                        }
                                                                                    </DropDownContainer>
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            )
                                                        )}
                                                    </tbody>
                                                </table>
                                            </React.Fragment>
                                        )
                                    )
                                )}
                                {logs.length === 0 && (
                                    <div className="text-center text-gray-500 py-4">
                                        No logs found
                                    </div>
                                )}
                            </div>
                            <div className="mt-6">
                                <CursorPaginate
                                    onFirstPage={onFirstPage}
                                    onLastPage={onLastPage}
                                    nextPageUrl={nextPageUrl}
                                    previousPageUrl={previousPageUrl}
                                    itemCount={itemCount}
                                    total={total}
                                    itemName="log"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {showPayloadModal && (
                <Payload data={payloadData} onClose={closePayloadModal} />
            )}
        </AdminLayout>
    );
}
 