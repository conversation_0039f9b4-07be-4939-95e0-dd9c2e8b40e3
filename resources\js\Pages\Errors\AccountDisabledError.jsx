//* PACKAGES
import React from 'react'

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//... 

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AccountDisabledError(
    {
        //! PROPS
        code,
        error,
        help        

        //! STATES 
        //...

        //! EVENTS
        //...
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    //... 

    //! VARIABLES
    //...

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <section
            className="flex items-center h-screen p-16 "
        >
            <div
                className="container flex flex-col items-center justify-center px-5 mx-auto my-8"
            >
                <div
                    className="max-w-md text-center"
                >
                    <h2
                        className="mb-8 font-extrabold text-9xl text-primary"
                    >
                        {code}
                    </h2>
                    <p 
                        className="text-2xl font-semibold md:text-3xl text-gray-700"
                    >
                        {error}
                    </p>
                    <p
                        className="mt-4 mb-8 dark:text-gray-400"
                    >
                        {help}
                    </p>
                    <div
                        className="flex justify-center"
                    >
                        <button
                            className="px-4 py-2 text-white bg-primary rounded"
                            onClick={() => { window.location.href = route('login'); }}
                        >
                            Go to Login Page
                        </button>
                    </div>
                </div>
            </div>
        </section>
    );
}
