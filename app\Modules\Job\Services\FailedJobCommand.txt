<?php

namespace App\Modules\Job\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\Job\Constants\JobConnection;
use App\Traits\CursorPaginate;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;



class FailedJobCommand
{
    use CursorPaginate;
    private static $pageLimit = 3;
    public static function index(string $dbConnection)
    {
        $failedJobs = DB::connection($dbConnection)->table('failed_jobs')->orderBy('id', 'desc')->cursorPaginate(self::$pageLimit);
        return CursorPaginate::cursor($failedJobs, ['source=' . $dbConnection]);
    }

    public static function get(string $dbConnection, string $id)
    {
        $job = DB::connection($dbConnection)->table('failed_jobs')->find($id);

        if ($job != null)
            return $job;

        throw new FailedRequestException(401, "failed queue with id " . $id . " does not exist", "INVALID ID");
    }

}