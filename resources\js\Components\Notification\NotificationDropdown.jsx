import React, { useEffect } from "react";
import { Link } from "@inertiajs/react";
import Dropdown from "@/Components/Dropdown";
import NotificationDropdownItem from "@/Components/Notification/NotificationDropdownItem";
import { useAtom } from "jotai";
import axios from "axios";
import InfiniteScroll from "react-infinite-scroll-component";
import LoaderSpinner from "../LoaderSpinner";
import { MdNotificationsNone } from "react-icons/md";

// Import Jotai atoms
import {
    notifications,
    totalNotifications,
    hasMore,
    pageNumber,
    tunreadCounter,
    hideBadge,
    readNotification,
} from "@/State/NotificationsDropdownJotaiState";
import { useState } from "react";

export const NotificationDropdown = () => {
    // Jotai state atoms
    const [totalNotificationss, setTotalNotifications] =
        useAtom(totalNotifications);
    const [notificationss, setNotifications] = useAtom(notifications);
    const [pageNumbers, setPageNumber] = useAtom(pageNumber);
    const [hasMores, setHasMore] = useAtom(hasMore);
    const [unreadCounter, setUnreadCounter] = useAtom(tunreadCounter);
    const [hideBadgeCount, setHideBadgeCount] = useAtom(hideBadge);

    const [isStorageLoaded, setIsStorageLoaded] = useState(false);

    // 🔹 1st useEffect: Load notifications from sessionStorage first
    useEffect(() => {
        const storedNotifications = sessionStorage.getItem("notifications");
        const storedreadNotification = sessionStorage.getItem("readNotifications");
        const storedTotalNotifications =
            sessionStorage.getItem("totalNotifications");
        const storedHideBadge = sessionStorage.getItem("HideBadgeCount");
        const storedPageNumber = sessionStorage.getItem("pageNumber");
        const storedHasMore = sessionStorage.getItem("HasMore");
        const storedUnreadCounter = sessionStorage.getItem("UnreadCounter");

        if (storedNotifications) {
            const parsedNotifications = JSON.parse(storedNotifications);
            setNotifications(parsedNotifications);
               
        }

        // if (!storedreadNotification && storedNotifications) {
        //     const parsed = JSON.parse(storedNotifications);
        //     const readMap = {};
        //     parsed.forEach((n) => {
        //         if (n.read_at) {
        //             readMap[n.id] = true;
        //         }
        //     });
        //     sessionStorage.setItem("readNotificationsMap", JSON.stringify(readMap));
        // }
        

        if (storedTotalNotifications) {
            setTotalNotifications(Number(storedTotalNotifications)); // Convert string to number
        }

        if (storedPageNumber) {
            setPageNumber(Number(storedPageNumber)); // Convert string to number
        }

        if (storedHideBadge === "true") {
            setHideBadgeCount(true);
            console.log("onRefresh");
        }

        if (storedHasMore === "false") {
            setHasMore(false);
        }

        if (storedUnreadCounter) {
            setUnreadCounter(Number(storedUnreadCounter)); // Convert string to number
        }

        setIsStorageLoaded(true); // Mark sessionStorage as loaded ✅
    }, []);

    // 🔹 2nd useEffect: Fetch from API only if sessionStorage was empty
    useEffect(() => {
        if (!isStorageLoaded) return; // Ensure sessionStorage has loaded before running

        if (!notificationss || notificationss.length === 0) {
            const fetchNotifications = async () => {
                try {
                    const res = await axios.get(
                        `/notification/dropdown-data?page=1`
                    );
                    const unreadRes = await axios.get(
                        `/notification/unread-count`
                    );

                    if (res.data.items.length > 0) {
                        setNotifications(res.data.items);
                        sessionStorage.setItem(
                            "notifications",
                            JSON.stringify(res.data.items)
                        ); // Save to sessionStorage
                        
                    }

                    setUnreadCounter(unreadRes.data);
                    setTotalNotifications(res.data.total);
                    setPageNumber(2);
                    sessionStorage.setItem("pageNumber", "2"); // ✅ Save page number
                    sessionStorage.setItem(
                        "totalNotifications",
                        res.data.total
                    ); // ✅ Save totalNotifications
                    sessionStorage.setItem("UnreadCounter", unreadRes.data); // ✅ Save totalNotifications
                } catch (error) {
                    console.error("Error fetching notifications:", error);
                }
            };

            fetchNotifications();
        }
    }, [isStorageLoaded]); //✅ Runs only after storage has been loaded

    // Function to fetch more notifications
    const fetchMoreData = () => {
        if (
            hasMores &&
            notificationss.length < totalNotificationss &&
            totalNotificationss <= 200
        ) {
            setTimeout(() => {
                const fetchNotications = async () => {
                    const res = await axios(
                        `/notification/dropdown-data?page=${pageNumbers}`
                    );
                    setTotalNotifications(res.data.total);

                    sessionStorage.setItem(
                        "totalNotifications",
                        res.data.total.toString()
                    ); // ✅ Save total notifications

                    setNotifications((prevNotifications) => {
                        const newNotifications = prevNotifications.concat(
                            res.data.items
                        );
                        // Store updated notifications in sessionStorage
                        sessionStorage.setItem(
                            "notifications",
                            JSON.stringify(newNotifications)
                        );
                        return newNotifications;
                    });
                };
                fetchNotications();
                setPageNumber(pageNumbers + 1);
                sessionStorage.setItem("pageNumber", pageNumbers + 1); // ✅ Save page number
            }, 1500);
        } else {
            setHasMore(false);
            sessionStorage.setItem("HasMore", false);
        }
    };

    const onhideNotificationBadge = () => {
        setHideBadgeCount(true);
        sessionStorage.setItem("HideBadgeCount", true);
    };
    return (
        <div className="hidden sm:flex sm:items-center sm:ml-6">
            <div className="ml-3 relative">
                <Dropdown>
                    <Dropdown.Trigger>
                        <Link
                            as="button"
                            title="Notifications"
                            onClick={(e) => [
                                e.preventDefault(),
                                onhideNotificationBadge(),
                            ]}
                        >
                            <div className="flex items-center hover:bg-black hover:bg-opacity-20 rounded-full p-1 transition duration-150">
                                <div className="relative">
                                    {unreadCounter > 0 && !hideBadgeCount && (
                                        <span className="absolute text-xs right-0 top-0 bg-danger text-white font-semibold rounded-full px-[5px] -mr-2">
                                            {unreadCounter > 99
                                                ? `${unreadCounter}+`
                                                : unreadCounter}
                                        </span>
                                    )}
                                    <MdNotificationsNone className="rounded-full text-3xl p-1" />
                                </div>
                            </div>
                        </Link>
                    </Dropdown.Trigger>
                    <Dropdown.Content
                        width="w-[32rem]"
                        contentClasses="pb-6 bg-white overflow-y-auto max-h-[90vh]"
                    >
                        {Array.isArray(notificationss) &&
                        notificationss.length > 0 ? (
                            <>
                                <div className="flex justify-end">
                                    <Link
                                        className="px-6 pb-1"
                                        href={route("notification")}
                                        method="get"
                                        as="button"
                                    >
                                        <span className="text-sm text-link">
                                            Show All
                                        </span>
                                    </Link>
                                </div>
                                <InfiniteScroll
                                    dataLength={notificationss.length}
                                    next={fetchMoreData}
                                    hasMore={hasMores}
                                    loader={
                                        <div className="text-center text-blue-400 shadow-sm text-base">
                                            <LoaderSpinner
                                                position={"inline-block"}
                                            />
                                            <br />
                                            Loading...
                                        </div>
                                    }
                                    scrollableTarget="scrollableDiv"
                                    endMessage={
                                        <p className="text-center text-sm text-black">
                                            No more notifications
                                        </p>
                                    }
                                >
                                    {notificationss.map((item) => (
                                        <NotificationDropdownItem
                                            key={item.id}
                                            item={item}
                                        />
                                    ))}
                                </InfiniteScroll>
                            </>
                        ) : (
                            <span className="flex justify-center py-10 text-xl font-bold text-gray-200">
                                No Notification
                            </span>
                        )}
                    </Dropdown.Content>
                </Dropdown>
            </div>
        </div>
    );
};

export default NotificationDropdown;
