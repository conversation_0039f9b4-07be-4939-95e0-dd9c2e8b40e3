<?php

namespace App\Modules\MarketPlace\Controllers;

use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\CheckRequest;
use App\Modules\MarketPlace\Requests\ExportRequest;
use App\Modules\MarketPlace\Requests\ResendRequest;
use App\Modules\MarketPlace\Services\MarketAuditService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class MarketAuditsController extends Controller
{
    public function index()
    {
        $res = DB::table('afternic_sale_audits')->get();

        return Inertia::render('MarketPlace/Audits', ['data' => 'hi from audits', 'audits' => $res]);
    }

    public function resendEmail(ResendRequest $request) : void
    {
        $request->resend();
    }

    public function checkCode(CheckRequest $request) : void
    {
        $request->check();
    }

    public function exportCSV(ExportRequest $request) : BinaryFileResponse
    {
        return $request->export();
    }
}
