<?php

namespace App\Modules\Client\Controllers;;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

use <PERSON><PERSON><PERSON>\Agent\Agent;

class SessionController extends Controller
{
    public function index($userId)
    {

        // Initialize the Agent class
        $agent = new Agent();
        // Fetch all sessions for a given user ID
        $sessions = DB::client()->table('sessions')
            ->where('user_id', $userId)
            ->where('status', 'active')
            ->orderBy('last_activity', 'desc') // Order by last_activity descending
            ->get()
            ->map(function ($session) use ($agent) {
                // Decode the session payload (base64 encoded)
                $decodedPayload = unserialize(base64_decode($session->payload));

                // Parse the user-agent string from the session
                $agent->setUserAgent($session->user_agent);

                // Get browser and version
                $browser = $agent->browser();
                $browserVersion = $agent->version($browser);

                // Get OS details
                $os = $agent->platform();

                // Get device type (Desktop, Mobile, etc.)
                $device = $agent->device();

                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $session->user_agent,
                    'browser' => $browser,
                    'browser_version' => $browserVersion,
                    'os' => $os,
                    'device' => $device,
                    'last_activity' => $session->last_activity,
                    'data' => $decodedPayload, // Expose any other relevant data from session
                ];
            });
        $userName = DB::client()->table('users')->where('id', $userId)->select('first_name as name')->first();
        //return view('sessions.user_sessions', compact('sessions'));

        return Inertia::render('Sessions/Index', [
            'sessions' => $sessions, 'userName' => $userName // Pass current session ID to frontend
        ]);
    }

    public function device_session_details($sessionId)
    {
        // Initialize the Agent class
        $agent = new Agent();

        $sessions = DB::client()->table('sessions')
            ->where('id', $sessionId)
            ->get()
            ->map(function ($session) use ($agent) {
                // Decode the session payload (base64 encoded)
                $decodedPayload = unserialize(base64_decode($session->payload));

                // Parse the user-agent string from the session
                $agent->setUserAgent($session->user_agent);

                // Get browser and version
                $browser = $agent->browser();
                $browserVersion = $agent->version($browser);

                // Get OS details
                $os = $agent->platform();

                // Get device type (Desktop, Mobile, etc.)
                $device = $agent->device();

                $formattedDate = Carbon::createFromTimestamp($session->last_activity)->format('F j, Y \\a\\t g:iA');

                return [
                    'id' => $session->id,
                    'ip_address' => $session->ip_address,
                    'user_agent' => $session->user_agent,
                    'browser' => $browser,
                    'browser_version' => $browserVersion,
                    'os' => $os,
                    'device' => $device,
                    'last_activity' => $session->last_activity,
                    'formattedDate' => $formattedDate,
                    'data' => $decodedPayload, // Expose any other relevant data from session
                ];
            });

            return Inertia::render('Sessions/Details',  [
                'sessions' => $sessions,
            ]);
    }
}
