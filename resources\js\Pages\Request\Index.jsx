import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/Request/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useEffect, useState } from "react";
import { Link, router } from "@inertiajs/react";
import {
    MdArrowBackIos,
    MdArrowForwardIos,
    MdOutlineSettings,
    MdOutlineSortByAlpha,
} from "react-icons/md";
import {
    ImSortAlphaAsc,
    ImSortAlphaDesc,
    ImCalendar,
} from 'react-icons/im'
import {
    TbSortAscending2,
    TbSortDescending2
} from 'react-icons/tb'
import CursorPaginate from "@/Components/Util/CursorPaginate";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";
export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const STATUS_TYPE = {
        ALL: "ALL",
        PENDING: "PENDING",
        ACCEPT: "ACCEPT",
        DENIED: "DENIED",
        DELETED: "DELETED",
    };

    const SORT_TYPE = {
        EMAIL_ASC: "email:asc",
        EMAIL_DESC: "email:desc",
        CREATE_DATE_ASC: "created_at:asc",
        CREATE_DATE_DESC: "created_at:desc",
        DELETE_DATE_ASC: "deleted_at:asc",
        DELETE_DATE_DESC: "deleted_at:desc",
    }

    const DT_FORMAT = {
        STRING: 1,
        COUNT: 2,
        LOCAL: 3,
        UTC: 4,
    }

    const [datetimeFormat, setDatetimeFormat] = useState(DT_FORMAT.STRING);

    const paramStatus = route().params.status;
    const paramDeleted =
        Object.keys(route().params).indexOf("deleted") == -1 ? undefined : true;
    const defaultStatus = paramDeleted
        ? STATUS_TYPE.DELETED
        : paramStatus
            ? paramStatus
            : STATUS_TYPE.ALL;

    const [status, setStatus] = useState(defaultStatus);
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectedPendingItems, setSelectedPendingItems] = useState([]);

    const { orderby } = route().params?.orderby ? route().params : { orderby: SORT_TYPE.CREATE_DATE_ASC };

    const orderToggle = (sort) => {
        let payload = {};

        payload.orderby = sort;
        if (paramStatus) payload.status = paramStatus;

        router.get(route("request"), payload)
    };

    const resetStates = () => {
        setSelectAll(false);
        setSelectedItems([]);

        if (selectedPendingItems.length > 0) setSelectedPendingItems([]);
    };

    const handleStatusChange = (statusType) => {
        let payload = {};

        setStatus(statusType);

        if (selectedItems.length > 0) resetStates();
        if (statusType === "ALL") {
            payload = {};
        } else {
            payload = { status: statusType };
        }
        toast.info("Responding To Request.");
        router.get(route("request"), payload);
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);

        const ids = items.map((item) => item.id);

        setSelectedItems(checked ? ids : []);
    };

    const handleItemCheckboxChange = (itemId, itemStatus, checked) => {

        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });

        if (itemStatus === STATUS_TYPE.PENDING) {
            setSelectedPendingItems((prevSelectedPendingItems) => {
                return checked
                    ? [...prevSelectedPendingItems, itemId]
                    : prevSelectedPendingItems.filter((id) => id !== itemId);
            });
        }
    };

    const handleResponse = (status) => {
        toast.info((status === STATUS_TYPE.ACCEPT ? "Accepting" : "Declining") + " Request");
        router.patch(route("request.response"), {
            ids: selectedPendingItems,
            status: status,
        });

        resetStates();
    };

    const handleDeleteResponse = () => {
        toast.info("Deleting Request");
        router.delete(route("request.delete"), {
            data: selectedItems,
        });

        resetStates();
    };

    const allCapsToCapitalization = (string) => {
        if (string === 'ACCEPT') return 'Accepted'
        if (string === 'DENIED') return 'Declined'
        return string.charAt(0) + string.slice(1).toLowerCase();
    };

    const datetimeFormatToggle = (format) => {
        format++;
        if (format > Object.keys(DT_FORMAT).length) format = 1;
        setDatetimeFormat(format);
    }

    const [hasSpinner, setSpinner] = useState(false);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 justify-end">
                    <SecondaryButton
                        onClick={() => handleResponse(STATUS_TYPE.ACCEPT)}
                        processing={
                            selectedItems.length == 0 ||
                            [STATUS_TYPE.ACCEPT, STATUS_TYPE.DENIED].includes(
                                status
                            )
                        }
                    >
                        Accept
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={() => handleResponse(STATUS_TYPE.DENIED)}
                        processing={
                            selectedItems.length == 0 ||
                            [STATUS_TYPE.ACCEPT, STATUS_TYPE.DENIED].includes(
                                status
                            )
                        }
                    >
                        Decline
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={handleDeleteResponse}
                        processing={
                            selectedItems.length == 0 ||
                            status == STATUS_TYPE.DELETED
                        }
                    >
                        Delete
                    </SecondaryButton>
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    {Object.values(STATUS_TYPE).map((e) => {
                        let param = {};
                        if (e != "ALL") param = { status: e };
                        return (
                            <Link
                                key={e}
                                as="button"
                                href={route("request", param)}
                                onClick={() => {
                                    setSpinner(true);
                                    setStatus(e)
                                }}
                            >
                                <div
                                    className={`px-5 py-1 rounded-sm ${status == e
                                        ? "bg-gray-100 text-gray-700"
                                        : "hover:bg-gray-100 hover:text-link"
                                        }`}
                                >
                                    <span className=" text-inherit">
                                        {allCapsToCapitalization(e)}
                                    </span>
                                </div>
                            </Link>
                        );
                    })}
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <div className="flex space-x-2">
                                        <label className="flex items-center pl-2 space-x-2">
                                            {status == STATUS_TYPE.DELETED || (
                                                items.length === 0 ?
                                                    <Checkbox
                                                        disabled={true}
                                                        className="opacity-0 pointer-events-none"
                                                    /> :
                                                    <Checkbox
                                                        name="select_all"
                                                        value="select_all"
                                                        checked={items.length === selectedItems.length}
                                                        handleChange={
                                                            handleSelectAllChange
                                                        }
                                                    />
                                            )}
                                            <span className="">Email</span>
                                        </label>
                                        <button
                                            disabled={items.length === 0}
                                            onClick={() => orderToggle(orderby === SORT_TYPE.EMAIL_ASC ? SORT_TYPE.EMAIL_DESC : SORT_TYPE.EMAIL_ASC)}
                                        >
                                            {orderby === SORT_TYPE.EMAIL_ASC ? <ImSortAlphaAsc /> : <ImSortAlphaDesc />}
                                        </button>
                                    </div>
                                </th>
                                <th>
                                    <span>Message</span>
                                </th>
                                <th>
                                    <span>IP</span>
                                </th>
                                <th>
                                    <span>Type</span>
                                </th>
                                <th>
                                    <span>Status</span>
                                </th>

                                <th className="w-[17%]">
                                    <div className="flex space-x-2">
                                        {status === STATUS_TYPE.DELETED ? (
                                            <>
                                                <span>Delete Date</span>
                                                <button
                                                    disabled={items.length === 0}
                                                    onClick={() => orderToggle(orderby === SORT_TYPE.DELETE_DATE_ASC ? SORT_TYPE.DELETE_DATE_DESC : SORT_TYPE.DELETE_DATE_ASC)}
                                                >
                                                    {orderby === SORT_TYPE.DELETE_DATE_ASC ? <TbSortAscending2 /> : <TbSortDescending2 />}
                                                </button>
                                                <button
                                                    disabled={items.length === 0}
                                                    onClick={() => datetimeFormatToggle(datetimeFormat)}
                                                >
                                                    <ImCalendar />
                                                </button>
                                            </>

                                        ) : (
                                            <>
                                                <span>Request Date</span>
                                                <button
                                                    disabled={items.length === 0}
                                                    onClick={() => orderToggle(orderby === SORT_TYPE.CREATE_DATE_ASC ? SORT_TYPE.CREATE_DATE_DESC : SORT_TYPE.CREATE_DATE_ASC)}
                                                >
                                                    {orderby === SORT_TYPE.CREATE_DATE_ASC ? <TbSortAscending2 /> : <TbSortDescending2 />}
                                                </button>
                                                <button
                                                    onClick={() => datetimeFormatToggle(datetimeFormat)}
                                                >
                                                    <ImCalendar />
                                                </button>
                                            </>
                                        )}
                                    </div>
                                </th>

                                {status !== STATUS_TYPE.DELETED && (
                                    <th>
                                        <span className="text-xl">
                                            <MdOutlineSettings />
                                        </span>
                                    </th>
                                )}
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            { hasSpinner ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                                    </td>
                                </tr>    
                            ) : (
                                <>
                                    {items.map((item, index) => {
                                        return (
                                            <Item
                                                item={item}
                                                key={"dl-" + index}
                                                isSelected={selectedItems.includes(
                                                    item.id
                                                )}
                                                onCheckboxChange={
                                                    handleItemCheckboxChange
                                                }
                                                dateTimeFormat={datetimeFormat}
                                            />
                                        );
                                    })}
                                </>
                            )
                        }
                        </tbody>
                            
                    </table>
                </div>
                { hasSpinner ? " " : 
                    (
                        <>
                            <CursorPaginate
                                onFirstPage={onFirstPage}
                                onLastPage={onLastPage}
                                nextPageUrl={nextPageUrl}
                                previousPageUrl={previousPageUrl}
                                itemCount={itemCount}
                                total={total}
                                itemName={itemName}
                            />
                        </>
                    )
                }
            </div>
        </AdminLayout>
    );
}
