
//* PACKAGES
import { useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { TbTrash } from 'react-icons/tb';
import { FaPlus } from 'react-icons/fa';

//* COMPONENTS
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';
import AdminLayout from '@/Layouts/AdminLayout'

//* PARTIALS
import UserManagementRoleViewPermissionsModalComponent from '@/Components/UserManagement/UserManagementRoleViewPermissionsModalComponent';
import PartialUserManagementCategoryTable from '@/Pages/UserManagement/Category/Partials/PartialUserManagementCategoryTable';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//... 

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementCategoryIndex(props)
{
    //! PACKAGE
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const paginationInfo =
    {
        onFirstPage    : props.data.onFirstPage,
        onLastPage     : props.data.onLastPage,
        nextPageUrl    : props.data.nextPageUrl,
        previousPageUrl: props.data.previousPageUrl, 
        itemCount      : props.data.itemCount, 
        total          : props.data.total
    };    

    //! STATES
    const [stateTableItems, setStateTableItems]                                     = useState(props.data.items);
    const [stateSelectedItems, setStateSelectedItems]                               = useState([]);
    const [stateSelectedItem, setStateSelectedItem]                                 = useState();
    const [stateIsActiveModalViewPermissions, setStateIsActiveModalViewPermissions] = useState(false);
    const [stateIsActiveModalVerification, setStateIsActiveModalVerification]   = useState(false);
    
    //! VARIABLES 
    const actionClass = 'h-5 w-5'; 

    const actions = 
    [
        {
            label           : 'create category', 
            icon            : <FaPlus className={actionClass} />,
            hasAccess       : hasPermission('user-management.category.create') && hasPermission('user-management.category.store'),
            isDisabled      : false, 
            handleEventClick: () =>
            {
                router.get(
                    route('user-management.category.create'),
                )            
            } 
        },
        {
            label           : 'delete selected',
            icon            : <TbTrash className={actionClass} />,
            hasAccess       : hasPermission('user-management.category.bulk-delete'),
            isDisabled      : stateSelectedItems.length == 0, 
            handleEventClick: () =>
            {
                setStateIsActiveModalVerification(true)
            } 
        }, 
    ];
    
    //! FUNCTIONS
    function handleCategoryBulkDelete(e)
    {   
        router.delete(
            route('user-management.category.bulk-delete'),
            {
                data: { categories: stateSelectedItems },
                onSuccess: () =>
                {
                    toast.success(
                        'Categories Deleted',
                        {
                            autoClose: 5000
                        }
                    );

                    setStateSelectedItems([]); 
                },
                onError: () => toast.error('Something went wrong'),
            }
        );
    }

    //! USE EFFECTS
    //...

    return (
        <AdminLayout>
            <UserManagementRoleViewPermissionsModalComponent
                selectedItem={stateSelectedItem} 
                stateIsModalOpen={stateIsActiveModalViewPermissions}
                handleEventModalClose={() =>
                    {
                        setStateIsActiveModalViewPermissions(false);
                        setStateSelectedItem(null);
                    }
                }
            />
            <AppPromptPasswordVerificationComponent
                show={stateIsActiveModalVerification}
                onSubmitSuccess={handleCategoryBulkDelete}
                onClose={() => 
                    {
                        setStateSelectedItem(null);
                        setStateIsActiveModalVerification(false);
                    }
                }
            />
            <div
                className="mx-auto container max-w-[1200px] mt-5 flex flex-col gap-8 rounded-lg"
            >
                <div
                    className='flex justify-between'
                >
                    <div>
                        <div className='text-3xl font-semibold mb-3'>Category</div>
                        <span className='text-gray-500 max-w-lg'>View & Manage Categories</span>
                    </div>
                    <div
                        className='flex justify-between items-center gap-4'
                    >
                        {
                            actions.filter(action => action.hasAccess)
                                .map(
                                    (action, actionIndex) => 
                                    {
                                        return (
                                            <AppButtonComponent
                                                key={actionIndex}
                                                isDisabled={action.isDisabled}
                                                handleEventClick={action.handleEventClick}
                                            >
                                                {action.icon} 
                                                <span
                                                    className='capitalize'
                                                >
                                                    {action.label}
                                                </span>
                                            </AppButtonComponent>
                                        )
                                    }
                                )
                        }
                    </div>
                </div>

                <PartialUserManagementCategoryTable
                    allItems={props.data.others.allItems}
                    stateTableItems={props.data.items}
                    setStateTableItems={setStateTableItems}
                    stateSelectedItems={stateSelectedItems}
                    setStateSelectedItems={setStateSelectedItems}
                    stateSelectedItem={stateSelectedItem}
                    setStateSelectedItem={setStateSelectedItem}
                    stateIsActiveModalViewPermissions={stateIsActiveModalViewPermissions}
                    setStateIsActiveModalViewPermissions={setStateIsActiveModalViewPermissions}
                    paginationInfo={paginationInfo}
                />
            </div>
        </AdminLayout>
    )
}
