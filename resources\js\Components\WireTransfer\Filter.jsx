import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import { useRef, useState } from "react";
import { router } from "@inertiajs/react";
import useOutsideClick from "@/Util/useOutsideClick";
import { offFilter, updateFieldValue } from "@/Components/Util/Filter/FilterMethod";
import { toast } from "react-toastify";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import "react-toastify/dist/ReactToastify.css";

export default function Filter() {
    const currentParams = route().params;
    const containerRef = useRef();

    const [nameInput, setNameInput] = useState(name || "");
    const [companyInput, setCompanyInput] = useState(currentParams.company || "");
    const filterConfig = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: currentParams.orderby ? [currentParams.orderby] : [],
                type: "option",
                items: [
                    "amount:asc",
                    "amount:desc",
                    "received_amount:asc",
                    "received_amount:desc",
                    "created_at:desc",
                    "created_at:asc",
                    "updated_at:desc",
                    "updated_at:asc"
                ],
                name: "Order By",
            },
            name: {
                active: false,
                value: name ? [name] : [],
                type: "text",
                name: "Name",
                tempValue: nameInput,
            },
            company: {
                active: false,
                value: currentParams.company ? [currentParams.company] : [],
                type: "text",
                name: "Company",
                tempValue: companyInput,
            },
            status: {
                active: false,
                value: currentParams.status ? [currentParams.status] : [],
                type: "option",
                items: ["pending","verified", "unverified", "rejected"],
                name: "Status",
            },
            
        },
    };

    const [filter, setFilter] = useState(filterConfig);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter((prevFilter) => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce(
                    (acc, key) => ({
                        ...acc,
                        [key]: {
                            ...updatedFilter.field[key],
                            active: false,
                        },
                    }),
                    {}
                ),
            };
        });
    });

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) => {
        if (key === "name") {
            setNameInput(value);

            if (!value || value === nameInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                const payload = {
                    ...currentParams,
                    [key]: value,
                };
                router.get(route("billing.wire.transfer"), payload, {
                    preserveState: true,
                    replace: true,
                });
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    name: {
                        ...prevFilter.field.name,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        if (key === "company") {
            
            setCompanyInput(value);

            if (!value || value === companyInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                const payload = {
                    ...currentParams,
                    [key]: value,
                };
                router.get(route("billing.wire.transfer"), payload, {
                    preserveState: true,
                    replace: true,
                });
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    company: {
                        ...prevFilter.field.company,
                        tempValue: value
                    }
                }
            }));
            return;
        }


        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: {
                    ...newValue,
                    active: false
                }
            },
        };

        setFilter(updatedFilter);

        const payload = {
            ...currentParams,
            [key]: value,
        };

        if (updatedFilter.container.reload) {
            toast.info("Reloading Data, Please Wait...");
        }

        router.get(route("billing.wire.transfer"), payload, {
            preserveState: true,
            replace: true,
        });
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.name}
                    fieldKey="name"
                    placeholder='Search name'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.name.tempValue || field.name.value[0] || "";
                        handleFieldUpdateValue("name", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
                <TextFilter
                    fieldProp={field.company}
                    fieldKey="company"
                    placeholder='Search company'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.company.tempValue || field.company.value[0] || "";
                        handleFieldUpdateValue("company", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
            </div>
        </div>
    );
}
