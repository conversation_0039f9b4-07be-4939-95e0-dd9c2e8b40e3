//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import LoaderSpinner from "@/Components/LoaderSpinner";
import PrimaryLink from "@/Components/PrimaryLink";
import CursorPaginate from "@/Components/Util/CursorPaginate";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index({
    balance,
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [hasSpinner, setSpinner] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS    
    const toDollar = (number) => {
        const floatValue = parseFloat(number);
        if (isNaN(floatValue)) return 0;

        // return floatValue.toFixed(2);
        return floatValue.toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col justify-between">
                <>
                    <div className="flex flex-col space-y-10 mt-[24px] border-t border-gray-200">
                        <div className="flex  bg-gray-50 p-10 rounded-sm space-y-1 justify-between bg-[url('/assets/images/wave.svg')] bg-cover bg-center ">
                            <div className="flex flex-col">
                                <span className="text-gray-600 text-lg font-semibold uppercase">
                                    Account Balance
                                </span>

                                <label className="text-lg text-gray-400">
                                    Balance
                                </label>

                                <span className=" text-4xl  text-gray-700">
                                    <span className=" pr-2 text-gray-400">
                                        $
                                    </span>
                                    {toDollar(
                                        items[0]
                                            ? items[0].running_balance
                                            : 0
                                    )}
                                </span>
                                <span className="text-gray-400 text-sm">
                                    {items[0]
                                        ? items[0].created_at
                                        : new Date().toLocaleDateString()}
                                </span>
                            </div>
                            <div className=" inline-flex items-center pr-2">
                                {
                                    hasPermission('system.credits.adjust') && hasPermission("system.credits.store")
                                        ?
                                            <PrimaryLink
                                                href={route("system.credits.adjust")}
                                            >
                                                Adjust Account Balance
                                            </PrimaryLink>
                                        :
                                            null
                                }
                            </div>
                        </div>
                        <div className="flex flex-col space-y-4 ">
                            <span className="text-gray-700 text-md font-semibold">
                                Recent activities
                            </span>
                            <div className="w-full">
                                <table className="w-full text-left border-separate border-spacing-y-3.5  text-gray-600">
                                    <tbody className="">
                                        {hasSpinner ? (
                                            <tr>
                                                <td colSpan={5}>
                                                    <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg">
                                                        <LoaderSpinner
                                                            ml="ml-96"
                                                            h="h-12"
                                                            w="w-12"
                                                            position="absolute"
                                                        />
                                                        <br />
                                                        <span className="relative top-9 left-72 ml-20">
                                                            Loading Data...
                                                        </span>
                                                    </div>
                                                </td>
                                            </tr>
                                        ) : (
                                            <>
                                                <tr>
                                                    <th>Type</th>
                                                    <th>Balance</th>
                                                    <th>Change</th>
                                                    <th>Purpose</th>
                                                </tr>
                                                {items.map((transaction) => {
                                                    return (
                                                        <tr
                                                            key={
                                                                "tr-" +
                                                                transaction.id
                                                            }
                                                            className=" hover:bg-gray-50"
                                                        >
                                                            <td className="border-b border-gray-200 pb-2">
                                                                <div className=" flex flex-col">
                                                                    <span>
                                                                        {transaction.type ==
                                                                        "debit"
                                                                            ? "ADD_FUND"
                                                                            : "SUB_FUND"}
                                                                    </span>
                                                                    <span className=" text-sm text-gray-400">
                                                                        {
                                                                            transaction.created_at
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </td>

                                                            <td className="border-b border-gray-200">
                                                                <span>
                                                                    {" "}
                                                                    {toDollar(
                                                                        transaction.running_balance
                                                                    )}{" "}
                                                                </span>
                                                            </td>

                                                            <td className="border-b border-gray-200">
                                                                <span>
                                                                    {transaction.type ==
                                                                    "credit"
                                                                        ? "- "
                                                                        : "+ "}
                                                                    {toDollar(
                                                                        transaction.amount
                                                                    )}
                                                                </span>
                                                            </td>
                                                            <td className="border-b border-gray-200">
                                                                <span>
                                                                    {" "}
                                                                    {
                                                                        transaction.note
                                                                    }{" "}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    );
                                                })}
                                            </>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </>
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                />
            </div>
        </AdminLayout>
    );
}
