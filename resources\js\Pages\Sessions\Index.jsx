import NavLink from "@/Components/NavLink";
import TimeAgo from "@/Components/TimeAgo";
import AdminLayout from "@/Layouts/AdminLayout";
import { usePage } from "@inertiajs/react";
import {
    MdKeyboardBackspace,
    MdDesktopMac,
    MdLaptopWindows,
    MdOutlineTabletAndroid,
    MdOutlineTabletMac,
} from "react-icons/md";

const sessionOSIcon = (os) => {
    const iconClasses = "text-2xl";
    switch (os) {
        case "Windows":
            return <MdLaptopWindows className={iconClasses} />;
        case "iOS":
            return <MdOutlineTabletMac className={iconClasses} />;
        case "AndroidOS":
            return <MdOutlineTabletAndroid className={iconClasses} />;
        default:
            return <MdDesktopMac className={iconClasses} />;
    }
};

const BackButton = () => (
    <button
        onClick={() => window.history.back()}
        className="flex items-center text-gray-600 hover:text-gray-800"
    >
        <MdKeyboardBackspace className="text-3xl hover:bg-black hover:bg-opacity-20 rounded-full p-1 transition duration-150 cursor-pointer" />
        <span className="ml-2 text-xl font-semibold mb-2">Back</span>
    </button>
);

const SessionItem = ({ session }) => (
    <div className="mt-4 space-y-4">
        <div className="flex justify-between items-center bg-white p-4 rounded-md shadow border">
            <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center">
                    {sessionOSIcon(session.os)}
                </div>
                <div>
                    <p className="font-medium text-gray-700">
                        {session.device} - {session.os}
                    </p>
                    <p className="text-sm text-gray-500">
                        <TimeAgo timestamp={session.last_activity} /> • {session.ip_address}
                    </p>
                    <p className="text-sm text-gray-500">
                        <NavLink href={route("client.session.details", session.id)} className="text-blue-500" padding="pl-0">
                            {session.browser} {" > "}
                            <span className="hover:underline text-base text">more details</span>
                        </NavLink>
                    </p>
                </div>
            </div>
        </div>
    </div>
);

export default function Index() {
    const { sessions, userName } = usePage().props;

    return (
        <AdminLayout>
            <div className="p-6 bg-white min-h-screen max-w-[1160px] mx-auto container mt-5 flex flex-col">
                <div className="w-full max-w-md mb-4">
                    <BackButton />
                </div>
                <h1 className="text-xl font-semibold mb-2">Devices and Sessions of: {userName.name}</h1>

                {sessions.length > 0 ? (
                    <>
                        <div className="mt-6 flex justify-between">
                            <p className="text-sm text-gray-600">Where you're signed in:</p>
                        </div>
                        {sessions.map((session) => (
                            <SessionItem key={session.id} session={session} />
                        ))}
                    </>
                ) : (
                    <p className="text-gray-500">No sessions found.</p>
                )}
            </div>
        </AdminLayout>
    );
}
