import React from "react";
import PrimaryLink from "@/Components/PrimaryLink";
import { Link } from "@inertiajs/react";
import UserLayout from "@/Layouts/AdminLayout";
export default function RegistrationCompleteMessage({ message }) {
    return (
        <div>
            <div className="mx-auto container max-w-[900px] text-center pt-14 flex justify-center items-center flex-wrap">
                <div className="w-auto h-[10rem]">
                    <img
                        className="h-full w-auto"
                        src="/assets/images/order_confirmed.svg"
                        alt="background"
                    />
                </div>

                <span className=" text-lg font-semibold ">{message}</span>
            </div>
            <div className="w-full text-center flex flex-col  items-center pt-12 space-y-7">
                <Link
                    href={route("login")}
                    className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none "
                >
                    Log in now
                </Link>
            </div>
        </div>
    );
}
