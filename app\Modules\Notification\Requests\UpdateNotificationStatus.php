<?php

namespace App\Modules\Notification\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\ScheduleNotification;
use App\Modules\Notification\Constants\NotificationStatus;

class UpdateNotificationStatus extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'ids' => 'required|array',
            'status' => 'required|string|in:pending,disabled'
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $hasExpiredNotifications = ScheduleNotification::whereIn('id', $this->ids)
                ->where('status', NotificationStatus::EXPIRED)
                ->exists();

            if ($hasExpiredNotifications) {
                $validator->errors()->add('ids', 'Cannot update expired notifications.');
            }
        });
    }
}
