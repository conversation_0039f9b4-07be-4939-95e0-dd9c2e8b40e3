import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/Job/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import EmptyResult from "@/Components/Util/EmptyResult";
import AdminLayout from "@/Layouts/AdminLayout";
import LoaderSpinner from "@/Components/LoaderSpinner";
import "react-toastify/dist/ReactToastify.css";
import { toast } from "react-toastify";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useState } from "react";
import { MdOutlineSortByAlpha, MdOutlineSettings, MdKeyboardBackspace } from "react-icons/md";
import { router } from "@inertiajs/react";

export default function Failed({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const { source } = route().params;
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);
        setSelectedItems(checked ? items.map((item) => item.id) : []);
    };

    const handleItemCheckboxChange = (itemId, checked) => {
        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });

        let temp = [...selectedItems];

        if (temp.includes(itemId)) {
            temp = temp.filter((e) => e != itemId);
        } else {
            temp.push(itemId);
        }

        temp.length == items.length ? setSelectAll(true) : setSelectAll(false);
    };
    const sleep = (ms) => new Promise((r) => setTimeout(r, ms));
    const handleForget = async (option) => {
        toast.info("Forgetting Failed Jobs.");

        if ("all".localeCompare(option) == 0)
            router.post(route("job.failed-flush"), {
                source: source,
            });
        else
            router.post(route("job.failed-forget"), {
                source: source,
                option: option,
            });

        await sleep(500);
        setSelectedItems([]);
        setSelectAll(false);
    };

    const handleRetry = async (option) => {
        toast.info("Retrying Failed Jobs.");
        router.post(route("job.failed-retry"), {
            source: source,
            option: option,
        });

        await sleep(500);
        setSelectedItems([]);
        setSelectAll(false);
    };

    const getSelectedItemUuid = () => {
        return Object.values(items)
            .filter((item) => selectedItems.includes(item.id))
            .map((item) => item.uuid)
            .join(" ");
    };

    const capitalizeEveryFirstLetter = (str) => {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    const [hasSpinner, setSpinner] = useState(false);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1000px] mt-10 flex flex-col ">
                <div className="flex items-start space-x-4 text-gray-700 space-y-2 mb-10">
                    <a href={route("job", { source })} >
                        <MdKeyboardBackspace className="text-3xl hover:bg-black hover:bg-opacity-20 rounded-full p-1 mt-3 transition duration-150 cursor-pointer" />
                    </a>
                    <div className="space-y-2">
                        <h1 className="text-4xl font-bold pl-3">Failed Jobs</h1>
                        <p className="text-gray-600 pl-3">
                            List of jobs that encountered an error during execution and were not completed successfully.
                        </p>
                    </div>
                </div>



                {/* <div className="mb-10">
                    <h1 className="text-4xl font-bold pl-3">Failed Jobs</h1>
                    <p className="text-gray-600 pl-3">
                        List of jobs that encountered an error during execution and were not completed successfully.
                    </p>
                </div> */}
                <div className="flex items-center space-x-4 justify-end">
                    <SecondaryButton
                        processing={source == undefined}
                        onClick={() => handleForget("all")}
                    >
                        Forget all
                    </SecondaryButton>
                    <SecondaryButton
                        processing={source == undefined}
                        onClick={() => handleRetry("all")}
                    >
                        Retry all
                    </SecondaryButton>
                    <SecondaryButton
                        processing={
                            source == undefined || selectedItems.length == 0
                        }
                        onClick={() => handleForget(getSelectedItemUuid())}
                    >
                        Forget selected
                    </SecondaryButton>
                    <SecondaryButton
                        processing={
                            source == undefined || selectedItems.length == 0
                        }
                        onClick={() => handleRetry(getSelectedItemUuid())}
                    >
                        Retry selected
                    </SecondaryButton>
                </div>
                {/* <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    {connection.map((key, i) => {
                        return (
                            <Link
                                key={key + "_" + i}
                                as="button"
                                href={route("job.failed", { source: key })}
                                onClick={() => setSpinner(true)}
                            >
                                <div
                                    className={`px-5 py-1 rounded-sm ${key.localeCompare(source) == 0
                                        ? "bg-gray-100 text-gray-700"
                                        : "hover:bg-gray-100 hover:text-link"
                                        }`}
                                >
                                    <span className=" text-inherit">
                                        {capitalizeEveryFirstLetter(key)}
                                    </span>
                                </div>
                            </Link>
                        );
                    })}
                </div> */}
                {source == undefined ? (
                    <EmptyResult message="Please select a source" />
                ) : (
                    <>
                        <div className="pt-5">
                            <table className="min-w-full text-left border-spacing-y-2.5 border-separate">
                                <thead className=" bg-gray-50 text-sm">
                                    <tr>
                                        <th>
                                            <label className="flex items-center pl-2 space-x-2">
                                                <Checkbox
                                                    name="select_all"
                                                    value="select_all"
                                                    checked={selectAll}
                                                    handleChange={
                                                        handleSelectAllChange
                                                    }
                                                />
                                                <span>UUID</span>
                                                <MdOutlineSortByAlpha />
                                            </label>
                                        </th>
                                        <th>
                                            <span>Connection</span>
                                        </th>
                                        <th>
                                            <span>Queue</span>
                                        </th>

                                        <th>
                                            <span>Failed At</span>
                                        </th>
                                        <th>
                                            <span className="text-xl">
                                                <MdOutlineSettings />
                                            </span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="text-sm">
                                    {hasSpinner ? (
                                        <tr>
                                            <td colSpan={5}>
                                                <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                                            </td>
                                        </tr>
                                    ) : (
                                        <>
                                            {items != undefined &&
                                                Object.values(items).map((e) => {
                                                    return (
                                                        <Item
                                                            item={e}
                                                            source={source}
                                                            key={"fj-" + e.id}
                                                            isSelected={selectedItems.includes(
                                                                e.id
                                                            )}
                                                            onCheckboxChange={
                                                                handleItemCheckboxChange
                                                            }
                                                            handleRetry={handleRetry}
                                                            handleForget={handleForget}
                                                        />
                                                    );
                                                })}
                                        </>
                                    )}

                                </tbody>
                            </table>
                        </div>
                        {hasSpinner ? " " :
                            (
                                <>
                                    <CursorPaginate
                                        onFirstPage={onFirstPage}
                                        onLastPage={onLastPage}
                                        nextPageUrl={nextPageUrl}
                                        previousPageUrl={previousPageUrl}
                                        itemCount={itemCount}
                                        total={total}
                                        itemName={itemName}
                                    />
                                </>
                            )
                        }
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
