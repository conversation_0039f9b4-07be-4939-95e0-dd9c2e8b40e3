import { useState } from "react";

import NavLink from "@/Components/NavLink";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>dExpand<PERSON>ess,
    MdExpandMore,
    MdOutlineConstruction,
    MdPieChartOutlined,
    MdPercent,
    MdAdminPanelSettings,
    MdOutlineAdminPanelSettings,
} from "react-icons/md";
export default function ActivityNav({ postRouteName }) {
    const routes = {
        admin: route().current("activity.admin"),
        client: route().current("activity.client"),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Activity</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("activity.admin")}
                active={routes.admin}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdAdminPanelSettings className="text-2xl " />
                    <span className=" text-inherit">Admin</span>
                </span>
                
            </NavLink>
            <NavLink
                href={route("activity.client")}
                active={routes.client}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdOutlineAdminPanelSettings className="text-2xl " />
                    <span className=" text-inherit">Client</span>
                </span>
                
            </NavLink>
        </>
    );
}
