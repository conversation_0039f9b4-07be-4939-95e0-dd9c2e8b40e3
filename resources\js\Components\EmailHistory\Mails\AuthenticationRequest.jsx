import React from 'react';

function AuthenticationRequest({ emailData, links }) {
    const data = JSON.parse(emailData);
    const { greeting, body, text, sender } = data.viewData;

    return (
      <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
        <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full">
            <p className="font-bold">{greeting}</p>
            <p className="mt-4">{body}</p>
            <p className="mt-4">{text}</p>
            <p className="mt-4 font-bold mb-2">Sincerely,</p>
            <p className="font-bold">{sender}</p>
        </div>
      </div>
    );
}

export default AuthenticationRequest;
