<?php

namespace App\Modules\Setting\Controllers;

use App\Exceptions\FailedRequestException;
use App\Http\Controllers\Controller;
use App\Modules\Setting\Requests\ShowCommissionListForm;
use App\Modules\Setting\Requests\StoreCommissionForm;
use App\Modules\Setting\Requests\UpdateCommissionForm;
use App\Modules\Setting\Services\FeeSettingService;
use Inertia\Inertia;

class CommisionSettingController extends Controller
{
    public function index(ShowCommissionListForm $request)
    {
        throw new FailedRequestException(404, 'Page not found.', '');
        // return Inertia::render('Setting/Commission/Index', array_merge(['fees' => FeeSettingService::get()], $request->show()));
    }

    public function updateStatus(UpdateCommissionForm $request)
    {
        throw new FailedRequestException(404, 'Page not found.', '');
        // $request->updateStatus();
        // return redirect()->back();
    }

    public function store(StoreCommissionForm $request)
    {
        throw new FailedRequestException(404, 'Page not found.', '');
        // $request->store();
        // return redirect()->route('setting.commission');
    }

    public function create()
    {
        throw new FailedRequestException(404, 'Page not found.', '');
        // return Inertia::render('Setting/Commission/New', ['fees' => FeeSettingService::get()]);
    }
}
