import { useState, useRef, useEffect } from "react";

export default function UserSelection({ 
    users, 
    onSelectionChange, 
    showSelectionInterface = true,
    selectionMode,
    selectedUsers: initialSelectedUsers,
    excludedUsers,
    setExcludedUsers,
    selectedEmails,
    setSelectedEmails
}) {
    const [searchQuery, setSearchQuery] = useState("");
    const [searchResults, setSearchResults] = useState([]);
    const [showDropdown, setShowDropdown] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const dropdownRef = useRef(null);

    const getFilteredEmails = (userList, excludedIds) => {
        return userList
            .filter(user => !excludedIds.has(user.id))
            .map(user => user.email);
    };

    const updateSelectedEmails = (emails, mode) => {
        setSelectedEmails(emails);
        onSelectionChange(emails, mode);
    };

    useEffect(() => {
        if (selectionMode === 'all') {
            const allEmails = getFilteredEmails(users, excludedUsers);
            updateSelectedEmails(allEmails, 'all');
        } else {
            updateSelectedEmails([], 'select');
        }
    }, [selectionMode]);

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target)
            ) {
                setShowDropdown(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [dropdownRef]);

    const handleSearch = (query) => {
        setSearchQuery(query);
        if (query.trim() === '') {
            setSearchResults([]);
            setShowDropdown(false);
            return;
        }

        const filteredUsers = users.filter(user => {
            const matchesSearch = user.email.toLowerCase().includes(query.toLowerCase());
            // Only filter out already selected emails
            return matchesSearch && !selectedEmails.includes(user.email);
        });
        
        setSearchResults(filteredUsers);
        setShowDropdown(true);
    };

    const handleSelectUser = (user) => {
        if (selectedEmails.length < 20) {
            const newEmails = [...selectedEmails, user.email];
            updateSelectedEmails(newEmails, 'select');
            setSearchQuery('');
            setShowDropdown(false);
        } else {
            setErrorMessage("You can only select up to 20 users.");
        }
    };

    const handleRemoveUser = (email) => {
        if (selectionMode === 'all') {
            const user = users.find(u => u.email === email);
            if (user) {
                const newExcluded = new Set(excludedUsers);
                newExcluded.add(user.id);
                setExcludedUsers(newExcluded);
                const remainingEmails = getFilteredEmails(users, newExcluded);
                updateSelectedEmails(remainingEmails, 'all');
            }
        } else {
            const newEmails = selectedEmails.filter(e => e !== email);
            updateSelectedEmails(newEmails, 'select');
        }
    };

    // Only render if showSelectionInterface is true
    if (!showSelectionInterface) {
        return null;
    }

    return (
        <div className="relative">
            {/* Selection Box */}
            <div className="h-[200px] overflow-y-auto p-2 border rounded-md bg-white">
                <div className="flex flex-wrap gap-2">
                    {selectedEmails.map((email) => (
                        <div
                            key={email}
                            className="inline-flex items-center bg-[#f1f8ff] text-[#0969da] px-3 py-1.5 rounded-full text-sm border border-[#c1d1f0]"
                        >
                            <span>{email}</span>
                            <button
                                type="button"
                                onClick={() => handleRemoveUser(email)}
                                className="ml-2 text-[#0969da]/60 hover:text-[#0969da]"
                            >
                                ×
                            </button>
                        </div>
                    ))}
                    <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => handleSearch(e.target.value)}
                        placeholder="Search users by email"
                        className="flex-1 border-0 focus:ring-0 min-w-[200px] text-sm"
                    />
                </div>
            </div>

            {/* Dropdown */}
            {showDropdown && searchResults.length > 0 && (
                <div ref={dropdownRef} className="absolute left-0 right-0 mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto z-10">
                    {searchResults.map((user) => (
                        <button
                            key={user.id}
                            type="button"
                            onClick={() => handleSelectUser(user)}
                            className="w-full text-left px-4 py-2 hover:bg-gray-100 text-sm"
                        >
                            {user.email}
                        </button>
                    ))}
                </div>
            )}
            
            {errorMessage && (
                <p className="text-sm text-red-600 mt-1">{errorMessage}</p>
            )}
        </div>
    );
}
