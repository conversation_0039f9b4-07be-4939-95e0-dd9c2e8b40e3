<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Services\MarketAuditService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ResendRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required',
            'file' => 'required'
        ];
    }

    public function resend() : void
    {
        $id  = $this->id;
        $file = $this->file;
        $code = substr(md5(uniqid(rand(), true)), 6, 6);

        DB::table('afternic_sale_audits')
        ->where('id', $id)
        ->update(['confirm_code' => Hash::make($code)]);

        MarketAuditService::instance()->sendEmail($file, $code, "Re: Strange Marketplace Audit Report $file.");
    }
}
