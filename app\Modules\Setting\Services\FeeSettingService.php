<?php

namespace App\Modules\Setting\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Setting\Constants\FeeType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class FeeSettingService
{
    private const MIN_FEE = 1;

    private const MAX_FEE = 10000;

    public static function get()
    {
        $list = DB::client()->table('fees')->orderby('id', 'asc')->get()->all();
        $settings = [];

        foreach ($list as $value) {
            if ($value->type != FeeType::PROTECTION) {
                if ($value->type === FeeType::RENEW) {
                    $value->nameType = 'RENEWAL';
                } elseif ($value->type === FeeType::PENALTY_LATE_RENEWAL) {
                    $value->nameType = 'LATE RENEWAL';
                } else {
                    $value->nameType = $value->type;
                }

                $settings[$value->type] = (array) $value;
            }
        }

        // print_r($settings);

        return $settings;
    }

    public static function update($type, $value)
    {
        $validator = strpos($type, 'PENALTY') !== 0 ? self::validateUpdateValues($type, $value) : null;

        if ($validator && $validator->errors()) {
            return $validator;
        }

        DB::client()->table('fees')->where('type', $type)->update(['value' => $value]);
        app(AuthLogger::class)->info('update fee '.$type.' to '.$value);

        return $validator;
    }

    public static function validateUpdateValues($type, $value)
    {
        $validator = Validator::make([], []);

        if (! self::validateFeeType($type)) {
            $validator->errors()->add('message', 'Fee type name does not exists.');

            return $validator;
        }

        if (! self::validateFeeValue(floatval($value))) {
            $validator->errors()->add('message', 'Value not valid.');

            return $validator;
        }

        $isMinimum = self::validateMinimumExtensionValue($type, floatval($value));

        if (array_key_exists('status', $isMinimum) && ! $isMinimum['status']) {
            $validator->errors()->add('message', $isMinimum['message']);

            return $validator;
        }
    }

    public static function validateFeeType(string $type)
    {
        if (! in_array($type, FeeType::all())) {
            return false;
        }

        return true;
    }

    public static function validateExtensionName(string $extension)
    {
        $names = DB::client()->table('extensions')->pluck('name')->toArray();

        if (! in_array($extension, $names)) {
            return false;
        }

        return true;
    }

    public static function validateFeeValue(float $value)
    {
        if ($value < self::MIN_FEE || $value > self::MAX_FEE) {
            return false;
            // throw new FailedRequestException(400, 'Value not valid.', 'Bad request');
        }

        return true;
    }

    public static function getDefaultExtensionValue(string $type)
    {
        $fee_id = DB::client()->table('fees')->where('type', $type)->value('id');
        $value = DB::client()->table('extension_fees')->where('fee_id', $fee_id)->where('is_default', true)->first()->value;

        return $value;
    }

    public static function getMinimumClientExtensionValue(string $type)
    {
        $fee_id = DB::client()->table('fees')->where('type', $type)->value('id');
        $min = DB::client()->table('extension_fees')->where('fee_id', $fee_id)->min('value');

        return $min;
    }

    public static function computeMinimumBaseFeeValue(string $type)
    {
        $minimumDefaultValue = self::getDefaultExtensionValue($type);
        $minimumClientValue = self::getMinimumClientExtensionValue($type);

        $total = 0;

        $minValues = [$minimumDefaultValue, $minimumClientValue];
        foreach ($minValues as $value) {
            $total += $value;
        }

        return $total;
    }

    public static function validateMinimumBaseFeeValue(string $type, float $value)
    {
        $min = self::computeMinimumBaseFeeValue($type);
        $minValue = ($min < 0) ? $min * -1 : $min;

        if ($min < 0 && $minValue >= $value) {
            return [
                'status' => false,
                'message' => 'Value in conflict with extension fees. Minimum value should be '.intval($minValue + 1),
            ];
        }

        return [
            'status' => true,
            'message' => 'OK',
        ];
    }

    public static function validateMinimumExtensionValue(string $type, float $value)
    {
        // $minValue = self::computeMinimumBaseFeeValue($type);
        $minimumClientValue = self::getMinimumClientExtensionValue($type);
        $minValue = ($minimumClientValue < 0) ? $minimumClientValue * -1 : $minimumClientValue;

        // ddd($minValue);
        if ($minimumClientValue < 0 && $minValue >= $value) {
            return [
                'status' => false,
                'message' => 'Value in conflict with client fees. Minimum total fee should be '.intval($minValue + 1),
            ];
        }

        return [
            'status' => true,
            'message' => 'OK',
        ];
    }
}
