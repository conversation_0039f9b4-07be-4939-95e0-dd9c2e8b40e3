<?php

namespace App\Modules\AdminCredit\Services;

use App\Exceptions\FailedRequestException;
use App\Models\AdminCredits;
use App\Models\SystemCreditsBlock;
use App\Modules\AdminCredit\Constants\AdminCreditType;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Error;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class AdminCreditBlockService
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $adminCreditBlockService = new self;

        return $adminCreditBlockService;
    }

    public function createGenesis()
    {
        $previousBlock = $this->getLatestBlock();

        if ($previousBlock) {
            throw new FailedRequestException(400, 'Genesis block already exists.', 'Bad request');
        }

        self::genesis();
    }

    public function addBlock(array $data, int $adminId)
    {
        $previousBlock = $this->getLatestBlock();

        if(!$previousBlock) {
            throw new FailedRequestException(400, 'No previous blocks found.', 'Bad request');
        }

        return self::add($previousBlock, $adminId, $data);
    }

    public function getLatestBlock()
    {
        return DB::table('system_credits_blocks')->orderBy('block_index', 'desc')->first() ?? null;
    }

    // private methods

    private function genesis()
    {
        $block = [
            'admin_id' => 0,
            'block_index' => 0,
            'type' => AdminCreditType::GENESIS,
            'running_balance' => 0,
            'amount' => 0,
            'previous_hash' => '0',
            'created_at' => Carbon::now()->timestamp,
        ];

        $block['hash'] = self::generate($block);

        return SystemCreditsBlock::insertGetId($block);
    }

    private static function add(object $previousBlock, int $adminId, array $data)
    {
        $block = [
            'admin_id' => $adminId,
            'block_index' => intval($previousBlock->block_index) + 1,
            'type' => $data['type'],
            'running_balance' => $data['running_balance'],
            'amount' => $data['amount'],
            'previous_hash' => $previousBlock->hash,
            'created_at' => Carbon::now()->timestamp,
            'updated_at' => null,
        ];

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::PAYMENT_CREATED,
            "System Credit Block added: " . $block['type'] . " - " . $block['amount']  . "$ by " . auth()->user()->email
        ));

        $block['hash'] = self::generate($block);

        return SystemCreditsBlock::create($block);
    }

    private static function generate(array $data)
    {
        $flattenData = Arr::flatten($data);
        $stringData = Arr::join($flattenData, '');

        return hash('sha256', $stringData);
    }

    private static function validate(object $block)
    {
        $testBlock = [
            'admin_id' => $block->admin_id,
            'block_index' => $block->block_index,
            'type' => $block->type,
            'running_balance' => $block->running_balance,
            'amount' => $block->amount,
            'timestamp' => $block->timestamp,
            'previous_hash' => $block->previous_hash,
        ];

        $computedHash = self::generate($testBlock);

        return $block->hash === $computedHash;
    }
}