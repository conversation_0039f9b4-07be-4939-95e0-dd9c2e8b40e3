<?php

namespace App\Modules\UserManagement\Requests;

use App\Modules\CustomLogger\Services\AuthLogger;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class UserManagementRoleBulkDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'roles' => ['required', 'array']
        ];
    }

    public function handleProcess()
    {
        foreach ($this->roles as $role) 
        {
            DB::table('access_roles')
                ->where('role_id', $role['id'])
                ->delete();

            DB::table('roles')
                ->where('id', '=', $role['id'])
                ->delete();

            app(AuthLogger::class)->info("role {$role['role']} with ID Number {$role['id']} deleted");
        }
    }
}
