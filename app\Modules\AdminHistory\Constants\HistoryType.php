<?php

namespace App\Modules\AdminHistory\Constants;

final class HistoryType
{
    public const ADMIN_LOGIN = 'Login';

    public const ADMIN_LOGOUT = 'Logout';

    public const PAYMENT_CREATED = 'Payment Created';

    public const DOMAIN_DELETED = 'Domain Deleted';

    public const DOMAIN_RESTORED = 'Domain Restored';

    public const DOMAIN_UPDATE = 'Domain Update';

    public const CLIENT_DELETE = 'Client Delete';

    public const CLIENT_INVITE = 'Client Invite';

    public const CLIENT_INVITE_RESEND = 'Client Invite Resend';

    public const BANK_TRANSFER_VERIFIED = 'Bank Transfer Verified';

    public const BANK_TRANSFER_REJECTED = 'Bank Transfer Rejected';

    public const NOTIFICATION_CREATED = 'Notification Created';

    public const USER_MANAGEMENT = 'User Management';

}
