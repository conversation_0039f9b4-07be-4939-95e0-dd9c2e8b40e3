import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { offFilter, updateFieldValue } from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function Filter() {
    const { orderby, job, stats, registry, isPrev, cursor } = route().params;
    const config = {
        container: {
            active: false,
            reload: false,
        },

        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [
                    "job:desc", "job:asc",
                ],
                name: "Order By",
            },
            job: {
                active: false,
                value: job ? [job] : [],
                type: "text",
                name: "Job",
            },
            stats: {
                active: false,
                value: stats ? [stats] : [],
                type: "option",
                items: [
                    'Active',
                    'Inactive',
                ],
                name: "Stats",
            }
        },
    };

    const [filter, setFilter] = useState(config);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, () => {
        const updatedFilter = offFilter(filter);
        setFilter({ ...updatedFilter });

        if (!updatedFilter.container.reload) return;

        toast.info("Reloading Data, Please Wait...");
        updatedFilter.container.reload = false;
        submit({ ...updatedFilter });
    });

    const submit = (updatedFilter) => {
        let { orderby, job, stats } = updatedFilter.field;
        let payload = {
            cursor: cursor || null,
            registry,
            isPrev
        };

        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (job.value.length > 0) payload.job = job.value[0];
        if (stats.value.length > 0) payload.stats = stats.value[0];

        router.get(route("job", payload));
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");

        setFilter({
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        });
    };

    return (
        <div className="flex items-center relative">
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.job}
                    fieldKey="job"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => setFilter(offFilter(filter))}
                    placeholder="Search Job"
                />
                <OptionFilter
                    fieldProp={field.stats}
                    fieldKey="stats"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
