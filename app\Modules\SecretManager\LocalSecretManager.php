<?php

namespace App\Modules\SecretManager;

class LocalSecretManager implements SecretManagerInterface
{
    private static function getDefaultEnv(string $key): string
    {
        $pattern = '/[^a-zA-Z0-9]/';
        $envKey = strtoupper(preg_replace($pattern, '_', $key));

        return env($envKey, $key);
    }

    public function getPayload(string $key): string
    {
        return self::getDefaultEnv($key);
    }

    public function getMeta(string $metadata): string
    {
        return self::getDefaultEnv($metadata);
    }
}
