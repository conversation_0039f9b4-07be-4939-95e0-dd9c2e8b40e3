<?php

namespace App\Modules\Job\Resources;

use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class JobCollection extends JsonResource
{
    public function boot(): void
    {
        JsonResource::withoutWrapping();
    }

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        try {
            // get payload property
            $payload = preg_replace("/(^[^{]*{)|}|\"|\0[a-zA-Z\\\]*\0/", '', json_decode($this->payload)->data->command);

            // separate by key property
            $payload = preg_split("/s\:\d{1,}\:/", $payload);

            // remove parameter with no value and empty string
            $payload = array_filter($payload, function ($section) {
                return ! empty($section) && str_contains($section, ':');
            });
        } catch (Exception $e) {
            Log::error($e->getMessage());
            app(AuthLogger::class)->error($e->getMessage());
        }

        return [
            'id' => $this->id,
            'created_at' => $this->created_at,
            'available_at' => $this->available_at,
            'queue' => $this->queue,
            'payload' => $this->payload
        ];
    }
}
