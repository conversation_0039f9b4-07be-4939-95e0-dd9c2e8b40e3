<?php

namespace App\Modules\Setting\Services;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ExtensionFees
{
    public static function instance(): self
    {
        $extensionFees = new self;

        return $extensionFees;
    }

    public function getQuery()
    {
        return DB::client()->table('extension_fees')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('extensions', 'extensions.id', '=', 'extension_fees.extension_id')
            ->join('tlds', 'tlds.extension_id', '=', 'extension_fees.extension_id')
            ->whereNull('extension_fees.deleted_at')
            ->orderby('extensions.id', 'asc')
            ->orderby('fees.type', 'asc');
    }

    public function selectFees(Builder &$builder)
    {
        $builder->select(
            'extension_fees.*',
            'fees.type',
            'fees.value as fee_value',
            'extensions.name as extension_name',
            'tlds.id as tld_id'
        );
    }

    public function getDefaultQuery()
    {
        $builder = $this->getQuery();
        $this->selectFees($builder);

        return $builder->where('is_default', true)->get()->all();
    }

    public function getUserCustomFeesByUserIds(array $userIds)
    {
        $builder = $this->getQuery();
        $this->selectFees($builder);

        return $builder->whereIn('user_id', $userIds)->get()->all();
    }

    public function getUserCustomFeeById(int $id)
    {
        $builder = $this->getQuery();
        $this->selectFees($builder);

        return $builder->where('user_id', $id)->get()->all();
    }
}
