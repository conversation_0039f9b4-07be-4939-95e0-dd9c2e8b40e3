import React from 'react'
import SecondaryButton from "@/Components/SecondaryButton";
import DropDownContainer from "@/Components/DropDownContainer";
import { useState, useRef } from "react";
import useOutsideClick from "@/Util/useOutsideClick";

export const ExtensionDropdown = ({
    extName,
    extensions,
    onChange }) => {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    return (
        <div>
            <span ref={ref} className="relative">
                <SecondaryButton
                    className="flex items-center"
                    onClick={() => setShow(!show)}
                >
                    <span>
                        {extName}
                    </span>
                    <svg
                        className="ml-2 -mr-0.5 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                    >
                        <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                        />
                    </svg>
                </SecondaryButton>
                <DropDownContainer show={show} className='left-0'>
                    {extensions && extensions.map((item, index) => (
                        <button
                            key={"cl" + index}
                            className="text-left hover:bg-gray-100 px-5 py-1"
                            onClick={() => {
                                setShow(!show)
                                onChange(item)
                            }}
                        >
                            <span>{item}</span>
                        </button>
                    ))}
                </DropDownContainer>
            </span>
        </div>
    )
}
