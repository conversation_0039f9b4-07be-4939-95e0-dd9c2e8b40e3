<?php

namespace App\Modules\Notification\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Notification\Services\GeneralNotificationService;
use App\Modules\Notification\Requests\GeneralNotificationRequest;
use Inertia\Inertia;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Inertia\Response;

class GeneralNotificationController extends Controller
{
    public function __construct(
        private GeneralNotificationService $service
    ) {}

    public function index()
    {
        return Inertia::render('Notification/GeneralNotification/Index', [
            'users' => $this->service->getAllUsers()
        ]);
    }

    public function store(GeneralNotificationRequest $request)
    {
        $this->service->createScheduledNotifications($request->getNotificationData());
        
        return redirect()
            ->route('notification.management')
            ->with('success', 'New notification has been successfully scheduled!');
    }
}