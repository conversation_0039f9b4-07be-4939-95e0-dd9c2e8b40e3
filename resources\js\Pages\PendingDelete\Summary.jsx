import Checkbox from "@/Components/Checkbox";
import PrimaryButton from "@/Components/PrimaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useForm } from "@inertiajs/react";
import { useState } from "react";
import { MdKeyboardBackspace } from "react-icons/md";


export default function Summary({ domains }) {
    const [isConfirmed, setIsConfirmed] = useState(false);

    const { delete: destroy, processing } = useForm({
        ids: domains.map((domain) => domain.id)
    });

    const onHandleChangeConfirmed = (e) => {
        setIsConfirmed(getEventValue(e));
    };

    const handleOnSubmit = (e) => {
        e.preventDefault();

        destroy(route("domain.pending-delete.delete"));
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[600px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                    <a href="#" onClick={() => window.history.back()}>
                        <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                    </a>
                    <span className=" text-inherit">
                        Summary
                    </span>
                    <span className="text-gray-500">{domains.length} {domains.length > 1 ? 'items' : 'item'}</span>
                </div>

                <span className="text-2xl text-gray-700 font-bold tracking-wide">
                    Domain Deletion
                </span>

                <span className="text-justify">
                    Requesting to <span className="font-bold text-danger">DELETE</span>
                    {` ${domains.length > 1 ? "these domains" : "this domain"} initiates a 30-day redemption period. If not restored, it will enter a 5-day pending delete phase before being released for public registration.`}
                </span>

                <span className="text-justify font-bold text-danger">
                    Please ensure the domains are correct, as this action is irreversible.
                </span>
                <form
                    className="flex flex-col space-y-4"
                    onSubmit={handleOnSubmit}
                >

                    <div className="flex items-start justify-end flex-col">
                        <label>
                            <Checkbox
                                name="is_confirmed"
                                value="is_confirmed"
                                checked={isConfirmed}
                                handleChange={onHandleChangeConfirmed}
                            />
                            <span className="ml-2 text-sm text-gray-600">
                                I confirm that I have verified the domains.
                            </span>
                        </label>
                    </div>
                    <div className="pt-4">
                        <PrimaryButton className="w-full p-t" processing={!isConfirmed || processing}>
                            Delete ({domains.length}) {domains.length > 1 ? 'Domains' : 'Domain'}
                        </PrimaryButton>
                    </div>
                </form>
                <div>
                    <span className="font-semibold text-gray-700">
                        Domain List
                    </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-gray-700  pb-6 border-b border-gray-200">
                    {domains.map((domain, index) => {
                        return (
                            <span key={"ob-" + index}>
                                {domain.name}
                            </span>
                        );
                    })}
                </div>
            </div>
        </AdminLayout>
    );
}
