<?php

namespace App\Modules\Client\Requests;

use App\Modules\Client\Services\ClientDetailService;
use Illuminate\Foundation\Http\FormRequest;

class ClientDetailRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // 'id' => ['required', 'integer'],
        ];
    }

    public function getClientDetails()
    {
        $clientId = $this->route('id');
        return ClientDetailService::instance()->getClientDetails($clientId);
    }
}