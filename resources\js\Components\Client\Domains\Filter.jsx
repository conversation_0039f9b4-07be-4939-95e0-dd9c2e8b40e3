import React, { useRef, useState } from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import { updateFieldValue, offFilter } from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import useOutsideClick from "@/Util/useOutsideClick";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ExpireDateFilter from "@/Components/Util/Filter/ExpireDateFilter";

export default function Filter({ id }) {
    const { orderby, status, expiryDate, minExpireDate, maxExpireDate } = route().params;
    const tld = route().params.tld?.split(',');

    const config = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: ["domain:desc", "domain:asc", "expiry:desc", "expiry:asc", "created:desc", "created:asc"],
                name: "Order By",
            },
            tld: {
                active: false,
                value: tld ? [...tld] : [],
                type: "multiOption",
                items: ["com", "net", "org"],
                name: "TLD",
            },
            expiryDate: {
                active: false,
                value: expiryDate ? [expiryDate] : [],
                type: "option-text",
                items: ["min:minExpireDate:Minimum", "max:maxExpireDate:Maximum"],
                name: "Expiry",
            },
            minExpireDate: {
                active: false,
                value: minExpireDate ? [minExpireDate] : [],
                type: "text",
                name: "Min Expiry Date",
                hidden: true,
            },
            maxExpireDate: {
                active: false,
                value: maxExpireDate ? [maxExpireDate] : [],
                type: "text",
                name: "Max Expiry Date",
                hidden: true,
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const ref = useRef();
    const { field } = filter;

    const [dateInput, setDateInput] = useState({
        min: "",
        max: "",
    });
    const [range, setRange] = useState({
        min: false,
        max: false,
    });

    useOutsideClick(ref, () => {
        setDateInput(() => ({
            min: "",
            max: "",
        }));
        setRange(() => ({
            min: false,
            max: false,
        }));

        const shouldReload = filter.container.reload;
        const updatedFilter = offFilter(filter);
        setFilter({ ...updatedFilter });

        if (!shouldReload) return;

        submit({ ...updatedFilter });
    });

    const submit = (updatedFilter) => {
        let { orderby, tld, minExpireDate, maxExpireDate } = updatedFilter.field;
        let payload = {};

        payload.status = status;
        if (tld.value.length > 0) payload.tld = tld.value.join(',');
        if (minExpireDate.value.length > 0) payload.minExpireDate = minExpireDate.value[0];
        if (maxExpireDate.value.length > 0) payload.maxExpireDate = maxExpireDate.value[0];
        if (orderby.value.length > 0) payload.orderby = orderby.value[0];

        router.get(route("client.domains", {id: id}), payload);
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {

        const oldValue = [...filter.field[key].value];
        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const hasChanged = JSON.stringify(oldValue) !== JSON.stringify(newValue.value);
        const reload = forceReload || hasChanged;

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        };

        setFilter(updatedFilter);

        if (reload) {
            submit(updatedFilter);
        }
    };

    const handleDateFieldValue = (key, value, doSetFilter = true) => {
        setDateInput({...dateInput, [key[0]] : value})

        const newValue = updateFieldValue(doSetFilter ? value : "", { ...filter.field[key[1]] });
        let reload = true;

        //if (newValue.value.length == 0 && value != "") reload = false; // ignore toggled item

        setFilter({
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key[1]]: { ...newValue },
            },
        });
    };

    return (
        <div className="flex items-center relative">
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <OptionFilter
                    fieldProp={field.tld}
                    fieldKey="tld"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <ExpireDateFilter
                    fieldProp={field.expiryDate}
                    fields={filter['field']}
                    dateInput={dateInput}
                    range={range}
                    setRange={setRange}
                    fieldKey="expiryDate"
                    handleDateFieldValue={handleDateFieldValue}
                    offFilter={() => setFilter(offFilter(filter))}
                />
            </div>
        </div>
    );
}
