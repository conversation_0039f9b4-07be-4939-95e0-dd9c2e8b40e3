import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import RadioButton from "@/Components/RadioButton";
import TextArea from "@/Components/TextArea";
import TextInput from "@/Components/TextInput";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { router } from "@inertiajs/react";
import { useState } from "react";
import { MdKeyboardBackspace } from "react-icons/md";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function SystemCreditChangeBalance({ registry = 'System Credits', balance, errors }) {

    const [isSubmitted, setIsSubmitted] = useState(false);
    const [amount, setAmount] = useState("");
    const [purpose, setPurpose] = useState("");
    const [transType, setTransType] = useState("debit");
    const [amountErrorMsg, setAmountErrorMsg] = useState("");

    const CREDIT = "credit";
    const DEBIT = "debit";

    const currencyFormatter = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
    });

    const toDollar = (number) => {
        const floatValue = parseFloat(number);
        if (isNaN(floatValue)) return 0;

        return currencyFormatter.format(floatValue.toFixed(2));
    };

    const calExpectedBalance = () => {
        const parseBalance = parseFloat((balance != null) ? balance.running_balance : 0);
        const parseAmount = parseFloat(amount);

        let tempExpBalance = 0;
        if (transType == CREDIT) {
            tempExpBalance = parseBalance - parseAmount;
        } else {
            tempExpBalance = parseBalance + parseAmount;
        }

        return isNaN(tempExpBalance)
            ? toDollar(parseBalance)
            : toDollar(tempExpBalance);
    };

    const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

    const setProcessing = () => {
        return amount <= 0 || purpose.length == 0 || isSubmitted
    }

    const handleUpdateBalance = async () => {
        setIsSubmitted(true);

        axios.post(route("system.credits.store"), {
            amount: amount,
            type: transType,
            description: purpose,
        })
            .then(() => {
                toast.success("Account Balance Updated");
                router.get(route("system.credits"));

            })
            .catch((err) => {
                console.log(err)
                toast.error(err.response.data.message);
            })
    };

    const getAmountErrorMsg = (val) => {
        setAmountErrorMsg("");
        if (val <= 0) setAmountErrorMsg("Amount Must Be Greater Than 0.")
    }

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[600px] mt-20 flex flex-col space-y-8 justify-between">
                <div className="flex flex-col space-y-10">
                    <div className="flex flex-col space-y-8 pt-4 text-gray-700">
                        <div className="flex">
                            <a href="#" onClick={() => router.get(route('system.credits'))}>
                                <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                            </a>
                            <span className="rounded-full p-1">Back</span>
                        </div>
                    </div>
                    <div className="flex rounded-sm space-y-1 justify-between bg-[url('/assets/images/wave2.svg')] bg-cover bg-center ">
                        <div className="flex flex-col">

                            <label className="text-lg text-gray-400">
                                Balance
                            </label>

                            <div className=" text-4xl  text-gray-700 ">
                                <p className="first-letter:text-gray-400 first-letter:pr-2">
                                    {toDollar((balance != null) ? balance.running_balance : 0)}
                                </p>
                            </div>
                            <span className="text-gray-400 text-sm">
                                {(balance != null) ? balance.created_at : ''}
                            </span>
                        </div>
                    </div>
                    <div className="flex flex-col space-y-8 pt-4 text-gray-700">
                        <div className=" space-y-2">
                            <label className="font-semibold ">
                                Transaction type
                            </label>

                            <div className="flex">
                                <div
                                    className="flex items-center space-x-2 cursor-pointer px-3 py-1 text-left leading-5 text-gray-700 min-w-[8rem] hover:bg-gray-100 fade-in-out"
                                    onClick={() => setTransType(DEBIT)}
                                >
                                    <RadioButton
                                        checked={transType == DEBIT}
                                        name={DEBIT}
                                        value={DEBIT}
                                        className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                        handleChange={() => { }}
                                    />
                                    <span>debit</span>
                                </div>
                                <div
                                    className="flex items-center space-x-2 cursor-pointer px-3 py-1 text-left leading-5 text-gray-700 min-w-[8rem] hover:bg-gray-100 fade-in-out"
                                    onClick={() => setTransType(CREDIT)}
                                >
                                    <RadioButton
                                        checked={transType == CREDIT}
                                        name={CREDIT}
                                        value={CREDIT}
                                        className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                        handleChange={() => { }}
                                    />
                                    <span>credit</span>
                                </div>
                            </div>
                        </div>
                        <div className=" space-y-2">
                            <label className="font-semibold ">Amount ($)</label>

                            <TextInput
                                type="number"
                                className=" w-full "
                                name="amount"
                                placeholder="enter amount"
                                min="0"
                                value={amount}
                                handleChange={(e) => {
                                    setAmount(getEventValue(e));
                                    setIsSubmitted(false);
                                    getAmountErrorMsg(getEventValue(e));
                                }}
                            />

                            <InputError
                                // message={errors.amount}
                                message={amountErrorMsg}
                                className="mt-2"
                            />
                        </div>

                        <div className=" space-y-2">
                            <label className="font-semibold ">Purpose</label>

                            <TextArea
                                maxLength={200}
                                type="text"
                                rows={2}
                                name="description"
                                value={purpose}
                                placeholder="Describe your purpose"
                                className="mt-1 block w-full"
                                handleChange={(e) => {
                                    setPurpose(getEventValue(e));
                                    setIsSubmitted(false);
                                }}
                            />
                            <InputError
                                message={errors.description}
                                className="mt-2"
                            />
                        </div>
                        <div className=" space-y-2 ">
                            <div className="flex justify-between">
                                <label>Current balance</label>
                                <span>{toDollar((balance != null) ? balance.running_balance : 0)}</span>
                            </div>
                            <div className="flex justify-between">
                                <label className="capitalize">
                                    {transType}
                                </label>
                                <span>
                                    {transType == CREDIT ? "-" : "+"}{" "}
                                    {toDollar(amount)}
                                </span>
                            </div>
                            <div className="flex justify-between items-center border-t b-gray-100 pt-2">
                                <label>Expected balance</label>
                                <span className=" text-xl font-semibold">
                                    {calExpectedBalance()}
                                </span>
                            </div>
                        </div>
                    </div>

                    <PrimaryButton
                        className=" w-full fade-in-out"
                        processing={setProcessing()}
                        onClick={handleUpdateBalance}
                    >
                        Update balance to ({calExpectedBalance()})
                    </PrimaryButton>
                </div>
            </div>
        </AdminLayout>
    );
}
