import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import { useRef, useState } from "react";
import useOutsideClick from "@/Util/useOutsideClick";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";

export default function Filter({ parent = "client" }) {
    const { orderby, email } = route().params;
    const status = route().params.status?.split(",");

    const containerRef = useRef();

    const [emailInput, setEmailInput] = useState(email || "");

    const config = {
        container: {
            active: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                // items: [
                //     "created:desc",
                //     "created:asc",
                //     "name:desc",
                //     "name:asc",
                //     "lastActive:desc",
                //     "lastActive:asc",
                // ],
                items: [
                    "email:desc",
                    "email:asc",
                    "lastActive:desc",
                    "lastActive:asc",
                ],
                name: "Order By",
            },
            status: {
                active: false,
                value: status ? [...status] : [],
                type: "multiOption",
                items: ["enabled", "disabled", 'pending'],
                name: "Status",
            },
            email: {
                active: false,
                value: email ? [email] : [],
                type: "text",
                name: "Email",
                tempValue: emailInput,
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });



    const submit = (updatedFilter) => {
        let { orderby, status, email } = updatedFilter.field;
        let payload = {};

        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (status.value.length > 0) payload.status = status.value.join(",");
        if (email.value.length > 0) payload.email = email.value[0];

        switch (parent) {
            case "extension_fee":
                router.get(route("client.extension.fee", payload));
                break;
            case "transaction_threshold":
                router.get(route("client.transaction.threshold", payload));
                break;
            default:
                router.get(route("client", payload));
        }
    };

    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);

        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    const handleFieldUpdateValue = (key, value) => {
        if (key === "email") {
            setEmailInput(value);

            if (!value || value === emailInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        submit(updatedFilter);
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
            </div>
        </div>
    );
}
