<?php

namespace App\Util\Constant;

final class Transaction
{
    public final const SYSTEM_LIMIT = 50;

    public final const USER_LIMIT = 5;

    public final const LENGTH = 30; // 30 days

    public final const REGISTER = 'REGISTER';

    public final const TRANSFER = 'TRANSFER';

    public final const RENEW = 'RENEW';

    public final const DELETE = 'DELETE';

    public final const REDEEM = 'REDEEM';

    public final const AUTH_REQUEST = 'AUTH_REQUEST';

    public final const DOMAIN_SEARCH = 'DOMAIN_SEARCH';

    public final const PUSH = 'PUSH';

    public final const TYPES = [
        self::REGISTER => 'Domain Registration',
        self::TRANSFER => 'Domain Transfer',
        self::RENEW => 'Domain Renewal',
        self::DELETE => 'Domain Deletion',
        self::REDEEM => 'Domain Redemption',
        self::AUTH_REQUEST => 'Domain Authorization Code Request',
        self::DOMAIN_SEARCH => 'Domain Search',
        self::PUSH => 'Domain Push'
    ];
}
