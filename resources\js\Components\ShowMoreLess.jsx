import { useState } from "react";

export default function ShowMoreLess({ 
    containerClassName = "inline-flex items-center", 
    conditionTrueClassName = "max-w-[8rem] break-words",
    conditionFalseClassName = "max-w-[6rem]",
    textButtonClassName = "text-link",
    condition,
    item,
    onClick,
    moreLabel = "more...",
    lessLabel = "show less",
    doSplit,
    itemUniqueKey,
    separator,
}){
    const [more, setMore] = useState(false);

    return (
        <div className={`${containerClassName}`}>
            { condition ? (
                <>
                    <div
                        className={`pr-3 ${more || "truncate"} ${doSplit && more && "flex flex-col"} ${conditionTrueClassName}`}
                    >   
                        { doSplit && more ? 
                            item.split(separator).map((content, index) => (
                                <span key={`${itemUniqueKey}${index}`}>{content}</span>
                            ))
                            :
                            <span>{item}</span>
                        }
                        
                    </div>
                    <button
                        className={`${textButtonClassName}`}
                        onClick={() => {
                            if(onClick) onClick()
                            setMore(!more)
                        }}
                    >
                        {more ? lessLabel : moreLabel}
                    </button>
                </>
            ) : (
                <div className={`${conditionFalseClassName}`}>
                    <span>{item}</span>
                </div>
            )}
        </div>
    )
    
}