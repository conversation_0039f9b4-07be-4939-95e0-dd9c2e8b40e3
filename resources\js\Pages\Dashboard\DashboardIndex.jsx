//* PACKAGES
import React from 'react'
import "react-toastify/dist/ReactToastify.css";

//* ICONS
//... 

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";

//* PARTIALS
import PartialDashboardActivityLogs from './Partials/PartialDashboardActivityLogs';
import PartialDashboardQuickAccess from './Partials/PartialDashboardQuickAccess';
import PartialDashboardDomainHistory from './Partials/PartialDashboardDomainHistory';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function DashboardIndex()
{
    //! PACKAGE
    //...
    //! HOOKS
    const { hasPermission } = usePermissions();
        
    //! VARIABLES
    //... 

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //... 

    return (
        <AdminLayout>
            <div
                className="p-6 space-y-6 mx-auto container max-w-[1600px] mt-2 flex flex-col px-5 rounded-lg"  
            >
                {/* Domain History & Activity Logs */}
                <div
                    className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                >
                    {/* Domain History */}
                    <PartialDashboardDomainHistory />

                    {/* My Activity Logs */}
                    <PartialDashboardActivityLogs />
                </div>

                {/* Quick Access */}
                <PartialDashboardQuickAccess />
            </div>
        </AdminLayout>
    );
}
