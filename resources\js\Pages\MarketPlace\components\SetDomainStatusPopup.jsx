import axios from 'axios';
import React, { useState } from 'react'
import { useEffect } from 'react';
import { toast } from 'react-toastify';

export default function SetDomainStatusPopup({modal, showModal, row, tempDomains, setTempDomains}) {

    const [status, setStatus] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const options = ['Pending Hold', 'Pending Order', 'Pending', 'Transfer Requested', 'Completed', 'Cancelled', 'Refunded'];

    const handleUpdate = () => {
        setIsLoading(true)

        axios.post(route('domain.update'), {
            id: row.market_domain_id,
            status: status
        }).then(() => {
            showModal(false);
            setIsLoading(false);
            toast.success('updated');

            setTempDomains(prev => prev.map(domain => domain.domain_id === row.domain_id ? { ...domain, status: status } : domain ) );        
        });

    }

    useEffect(() => {
        setStatus(row.status)
    }, [row]);

    return (
        <div className={` ${modal ? '' : 'hidden'} fixed z-10 overflow-y-auto top-0 w-full left-0`} id="modal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
                    <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                    <div className="inline-block px-4 align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                        <div className='flex flex-col w-full'>
                            <label className="w-full text-gray-800 pt-3 pb-1 flex justify-between"> <span>{row.domain}</span> <span>{row.status}</span> </label>
                            <div className="flex items-center w-full pt-1">
                                <select value={status} onChange={(e) => {setStatus(e.target.value)} } className='rounded-md capitalize w-full focus:ring-0 border-gray-300 focus:border-gray-500' name="" id="">
                                    {
                                        options.map((a, i) => {
                                            return <option value={a} key={i}>{a.replaceAll('_', ' ')}</option>
                                        })
                                    }
                                </select>
                            </div>
                        </div>
                        <div className="py-4 text-right">
                            <button type="button" disabled={isLoading} onClick={() => { showModal(false); }} className="cursor-pointer py-2 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-700 mr-2" ><i className="fas fa-times"></i> Close </button>
                            <button type="button" disabled={isLoading} onClick={() => { handleUpdate() }} className="cursor-pointer disabled:bg-gray-500 py-2 px-4 bg-primary text-white rounded-md font-medium hover:bg-blue-500 transition duration-500"><i className="fas fa-plus"></i> Update </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
/**

*/