import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";

export default function DisplayFilter({ handleDisplayToggle, container, field }) {
    const { active } = container;

    const handleShowContainer = (show) => {
        handleDisplayToggle({
            container: { ...container, active: show },
        });
    };

    const hasOpenField = () => {
        let activeList = Object.keys(field).map((e) => {
            if (field[e].active) return true;
            return false;
        });
        return activeList.indexOf(true) > -1 ? true : false;
    };

    const handleSelectedField = (key) => {
        handleDisplayToggle({
            container: { ...container, active: false },
            field: {
                ...field,
                [key]: { ...field[key], active: true },
            },
        });
    };

    return (
        <>
            <button
                className={`${!active && !hasOpenField() ? "" : "hidden"
                    }  text-gray-200 w-max`}
                onClick={(e) => handleShowContainer(true)}
            >
                <span>Select filter</span>
            </button>

            <DropDownContainer
                show={active}
                className=" left-full top-0 space-y-1 z-20" 
            >
                {Object.keys(field).map((k) => {
                    if (field[k].hidden) return
                    return (
                        <button
                            className="px-3 py-1 text-left leading-5 text-gray-700 min-w-[8rem] hover:bg-gray-100"
                            key={k}
                            onClick={() => handleSelectedField(k)}
                        >
                            {field[k].name}
                        </button>
                    );
                })}
            </DropDownContainer>
        </>
    );
}
