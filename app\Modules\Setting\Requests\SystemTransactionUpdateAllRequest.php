<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Services\TransactionThresholdService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class SystemTransactionUpdateAllRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            '*.transaction_id' => ['required', 'integer'],
            '*.name' => ['required', 'string', 'max:30'],
            '*.system_limit' => ['required', 'integer'],
            '*.user_limit' => ['required', 'integer'],
            '*.length' => ['required', 'integer'],
            '*.trigger_id' => ['required', 'integer'],
            '*.allow_action' => ['required', 'boolean'],
            '*.notify_subscriber' => ['required', 'boolean'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        dd($validator->errors());
    }

    public function update(): void
    {
        TransactionThresholdService::instance()->updateAll($this->all());
    }
}
