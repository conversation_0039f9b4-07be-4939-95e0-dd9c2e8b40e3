//* PACKAGES
import { useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { FaPlus } from 'react-icons/fa';
import { TbTrash } from 'react-icons/tb';

//* COMPONENTS
import AdminLayout from '@/Layouts/AdminLayout'
import AppButtonComponent from '@/Components/App/AppButtonComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';

//* PARTIALS

import PartialUserManagementRoleTable from '@/Pages/UserManagement/Role/Partials/PartialUserManagementRoleTable';
import UserManagementRoleViewPermissionsModalComponent from '@/Components/UserManagement/UserManagementRoleViewPermissionsModalComponent';

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//... 

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementRoleIndex(props)
{
    //! PACKAGE
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const paginationInfo =
    {
        onFirstPage    : props.data.onFirstPage,
        onLastPage     : props.data.onLastPage,
        nextPageUrl    : props.data.nextPageUrl,
        previousPageUrl: props.data.previousPageUrl, 
        itemCount      : props.data.itemCount, 
        total          : props.data.total
    };    

    //! STATES
    const [stateTableItems, setStateTableItems]                                     = useState(props.data.items);
    const [stateSelectedItems, setStateSelectedItems]                               = useState([]);
    const [stateSelectedItem, setStateSelectedItem]                                 = useState();
    const [stateIsActiveModalViewPermissions, setStateIsActiveModalViewPermissions] = useState(false);
    const [stateIsActiveModalVerification, setStateIsActiveModalVerification]   = useState(false);
    
    //! VARIABLES 
    const actionClass = 'h-5 w-5'; 

    const actions = 
    [
        {
            label           : 'create role', 
            icon            : <FaPlus className={actionClass} />,
            hasAccess       : hasPermission('user-management.role.create') && hasPermission('user-management.role.store'),
            isDisabled      : false, 
            handleEventClick: () =>
            {
                router.get(
                    route('user-management.role.create'),
                )            
            } 
        },
        {
            label           : 'delete selected',
            icon            : <TbTrash className={actionClass} />,
            hasAccess       : hasPermission('user-management.role.bulk-delete'),
            isDisabled      : stateSelectedItems.length == 0, 
            handleEventClick: () =>
            {
                setStateIsActiveModalVerification(true)
            }
        }, 
    ];
        
    //! FUNCTIONS
    function handleCategoryBulkDelete(e)
    {   
        router.delete(
            route('user-management.role.bulk-delete'),
            {
                data: { roles: stateSelectedItems },
                onSuccess: () =>
                {
                    toast.success(
                        'Roles Deleted',
                        {
                            autoClose: 5000
                        }
                    );

                    setStateSelectedItems([]); 
                },
                onError: () => toast.error('Something went wrong'),
            }
        );
    }

    //! USE EFFECTS
    //...

    return (
        <AdminLayout>
            <UserManagementRoleViewPermissionsModalComponent
                selectedItem={stateSelectedItem}
                stateIsModalOpen={stateIsActiveModalViewPermissions}
                handleEventModalClose={() => {
                    setStateIsActiveModalViewPermissions(false);
                    setStateSelectedItem(null);
                }
                }
            />
            <AppPromptPasswordVerificationComponent
                show={stateIsActiveModalVerification}
                onSubmitSuccess={handleCategoryBulkDelete}
                onClose={() => 
                    {
                        setStateSelectedItem(null);
                        setStateIsActiveModalVerification(false);
                    }
                }
            />
            <div
                className="mx-auto container max-w-[1200px] mt-5 flex flex-col gap-8 rounded-lg"
            >
                <div
                    className='flex justify-between'
                >
                    <div>
                        <div className='text-3xl font-semibold mb-3'>Roles</div>
                        <span className='text-gray-500 max-w-lg'>View & Manage Roles</span>
                    </div>
                    <div
                        className='flex justify-between items-center gap-4'
                    >
                        {
                            actions.filter(action => action.hasAccess)
                                .map(
                                    (action, actionIndex) => 
                                    {
                                        return (
                                            <AppButtonComponent
                                                key={actionIndex}
                                                isDisabled={action.isDisabled}
                                                handleEventClick={action.handleEventClick}
                                            >
                                                {action.icon} 
                                                <span
                                                    className='capitalize'
                                                >
                                                    {action.label}
                                                </span>
                                            </AppButtonComponent>
                                        )
                                    }
                                )
                        }
                    </div>
                </div>

                <PartialUserManagementRoleTable
                    allItems={props.data.others.allItems}
                    stateTableItems={props.data.items}
                    setStateTableItems={setStateTableItems}
                    stateSelectedItems={stateSelectedItems}
                    setStateSelectedItems={setStateSelectedItems}
                    stateSelectedItem={stateSelectedItem}
                    setStateSelectedItem={setStateSelectedItem}
                    stateIsActiveModalViewPermissions={stateIsActiveModalViewPermissions}
                    setStateIsActiveModalViewPermissions={setStateIsActiveModalViewPermissions}
                    paginationInfo={paginationInfo}
                />
            </div>
        </AdminLayout>
    );
}
