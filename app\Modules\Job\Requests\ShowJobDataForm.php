<?php

namespace App\Modules\Job\Requests;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Job\Constants\ConnectionSource;
use App\Modules\Job\Constants\JobConnection;
use App\Modules\Job\Services\FailedJobService;
use App\Modules\Job\Services\ShowJobService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Carbon\Carbon;
use Exception;

class ShowJobDataForm extends FormRequest
{
    private $tabs = ['QUEUE' => 'queue', 'FAILED' => 'failed'];
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $connections = array_column(JobConnection::getJobs($this->source), 'table');

        return [
            'connection' => ['required', 'string', Rule::in($connections)],
            'source' => ['required', 'string', Rule::in(ConnectionSource::all())],
            'date' => ['date', 'nullable'],
            'tab' => ['string', Rule::in(array_values($this->tabs))],
        ];
    }

    protected function prepareForValidation()
    {
        if (!$this->has('tab')) {
            $this->merge([
                'tab' => $this->tabs['QUEUE'],
            ]);
        }
    }

    public function getJobData()
    {
        $jobData = [
            'onQueueCount' => 0,
            'onFailedCount' => 0,
        ];

        try {
            $jobData['onQueueCount'] = ShowJobService::countOnJobQueue($this->connection);
            $jobData['onFailedCount'] = ShowJobService::countOnFailed($this->connection);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            app(AuthLogger::class)->error($e->getMessage());
        }

        return $jobData;
    }

    public function show()
    {
        $date = $this->date ?? Carbon::now();

        try {
            if ($this->tab === $this->tabs['QUEUE'])
                $data = ShowJobService::getQueuedJobs($this);
            elseif ($this->tab === $this->tabs['FAILED'])
                $data = FailedJobService::getFailedByJobs($this);

            $data['queueCount'] = ShowJobService::countOnQueue($this->connection);
            $data['stats'] = ShowJobService::stats($this->connection, $date);
            $data['date'] = $date;
        } catch (Exception $e) {
            Log::error($e->getMessage());
            app(AuthLogger::class)->error($e->getMessage());
        }

        return $data;
    }
}
