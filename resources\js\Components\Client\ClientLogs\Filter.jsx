import React from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function Filter({ onFilter, transactionTypes = [], routeName }) {
    const { email, type, date } = route().params;

    const config = {
        container: {
            active: false,
            reload: false,
        },

        field: {
            email: {
                active: false,
                value: email ? [email] : [],
                type: "text",
                name: "Email",
            },
            type: {
              active: false,
              value: type ? [type] : [],
              type: "option",
              items: Object.values(transactionTypes).map((item) =>
                  item.replace(/_/g, " ")
              ),
              name: "Last Activity",
          },
            date: {
                active: false,
                value: date ? [date] : [],
                type: "option",
                items: ["today", "yesterday", "last 7 days", "last 30 days"],
                name: "Date",
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, () => {
        const updatedFilter = offFilter(filter);
        setFilter({ ...updatedFilter });

        if (!updatedFilter.container.reload) return;

        toast.info("Reloading Data, Please Wait...");
        updatedFilter.container.reload = false;
        submit({ ...updatedFilter });
    });

    const submit = (updatedFilter) => {
        const { email, type, date } = updatedFilter.field;
        let payload = {};

        if (email.value.length > 0) payload.email = email.value[0];
        if (type.value.length > 0) payload.type = type.value[0].replace(/ /g, '_').toUpperCase();
        if (date.value.length > 0) payload.date = date.value[0];

        router.get(route(routeName), payload, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        const newValue = updateFieldValue(value, {
            ...filter.field[key],
        });
        const reload =
            forceReload || !(newValue.value.length === 0 && value !== "");

        setFilter({
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        });
    };

    return (
        <div className="flex items-center relative">
            <ActiveFilter
                field={filter.field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={filter.field.type}
                    fieldKey="type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={filter.field.email}
                    fieldKey="email"
                    placeholder="Search email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={filter.field.date}
                    fieldKey="date"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
