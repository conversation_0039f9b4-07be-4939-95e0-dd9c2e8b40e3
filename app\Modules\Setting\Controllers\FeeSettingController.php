<?php

namespace App\Modules\Setting\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Setting\Services\FeeSettingService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FeeSettingController extends Controller
{
    public function index()
    {
        return Inertia::render('Setting/Fee', ['fees' => FeeSettingService::get()]);
    }

    public function update(Request $request)
    {
        $validator = FeeSettingService::update($request->type, $request->value);

        if ($validator && $validator->errors()) {
            return redirect()->back()->withErrors($validator->errors()->messages());
        }

        return redirect()->back();
    }
}
