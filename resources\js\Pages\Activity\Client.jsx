import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import Item from "@/Components/Epp/Poll/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import EmptyResult from "@/Components/Util/EmptyResult";
import AdminLayout from "@/Layouts/AdminLayout";
import useOutsideClick from "@/Util/useOutsideClick";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { router } from "@inertiajs/react";
import { useRef, useState } from "react";
// import { useState } from "react";

import axios from "axios";
import { evaluate } from "@/Util/AxiosResponseHandler";
import PrimaryButton from "@/Components/PrimaryButton";
import Search from "@/Components/Util/Search";

export default function Client({ log }) {
    log = log.log;
    const [nextPage, setNextPage] = useState(1);
    const [logProcess, setLogProcess] = useState(false);
    const { page } = route().params;
    const [data, setData] = useState(log.length == 0 ? [] : log.split("\n"));
    const dataLength = data.length;

    const previousLog = async () => {
        if (logProcess) return;
        setLogProcess(true);

        toast.info("Fetching More Logs, Please Wait.");

        let response = await axios
            .get(route("activity.client-more"), { params: { page: nextPage } })
            .then((response) => {
                return response;
            })
            .catch((error) => {
                return error.response;
            });

        response = evaluate(response);

        if (response.success) {
            setNextPage(nextPage + 1);
            const newList = response.data.log.log.split("\n").concat(data);
            setData(newList);
        } else {
            toast.error("Pulling Failed, Something Went Wrong.");
        }

        setLogProcess(false);
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
                {data.length == 0 ? (
                    <Search message="Select registry to view log" />
                ) : (
                    <>
                        <div className="flex justify-center">
                            <PrimaryButton onClick={() => previousLog()}>
                                Load more
                            </PrimaryButton>
                        </div>
                        <div className="border p-3 max-h-[100vh] overflow-x-hidden">
                            <ul className=" list-disc ">
                                {data.map((e, i) => {
                                    return (
                                        <p
                                            className=" text-sm text-gray-500 "
                                            key={"admin-log-" + i}
                                        >
                                            <span className="text-gray-800 pr-1">
                                                {dataLength - i}
                                            </span>
                                            {e}
                                        </p>
                                    );
                                })}
                            </ul>
                        </div>
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
