//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
//...

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import PrimaryButton from "@/Components/PrimaryButton";
import Search from "@/Components/Util/Search";
import SecondaryButton from "@/Components/SecondaryButton";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { evaluate } from "@/Util/AxiosResponseHandler";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Log({ log })
{
    //! PACKAGE
    const { registry } = route().params;
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! STATES
    const [nextPage, setNextPage] = useState(2);
    const [moreProcess, setMoreProcess] = useState(false);
    const [refreshProcess, setRefreshProcess] = useState(false);
    const [data, setData] = useState(log.length == 0 ? [] : log.split("\n"));

    //! VARIABLES
    const dataLength = data.length;
    const logColor = {
        INFO: "text-primary",
        DEBUG: "text-primary",
        ERROR: "text-danger",
        WARN: "text-yellow-400",
    };

    const config = {
        verisign: {
            active: "verisign" == registry,
        },
        pir: {
            active: "pir" == registry,
        },
    };

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const refreshLog = async () => {
        if (refreshProcess) return;
        const payload = { registry: registry };

        setRefreshProcess(true);
        toast.info("Refreshing " + registry + " Logs, Please Wait.");

        let response = await axios
            .get(route("epp.log.refresh"), { params: { ...payload } })
            .then((response) => {
                return response;
            })
            .catch((error) => {
                // console.log(error.response);
                return error.response;
            });

        response = evaluate(response);

        if (response.success) {
            toast.success("Done!");
            router.get(route("epp.log"), payload);
        } else {
            toast.error("Pulling Failed, Something Went Wrong.");
        }

        setRefreshProcess(false);
    };

    const moreLog = async () => {
        if (moreProcess) return;
        const payload = { registry: registry, page: nextPage };

        setMoreProcess(true);
        toast.info("Fetching More " + registry + " Logs, Please Wait.");

        let response = await axios
            .get(route("epp.log.more"), { params: { ...payload } })
            .then((response) => {
                return response;
            })
            .catch((error) => {
                // console.log(error.response);
                return error.response;
            });

        response = evaluate(response);

        if (response.success) {
            toast.success("Done!");
            setNextPage(nextPage + 1);
            let newPulledLog = response.data?.log;

            if (newPulledLog == undefined) return;
            const newList = newPulledLog.split("\n").concat(data);
            setData(newList);
        } else {
            toast.error("Pulling Failed, Something Went Wrong.");
        }

        setMoreProcess(false);
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1280px] mt-20 flex flex-col space-y-4">
                {registry != undefined && (
                    <div className="flex items-center space-x-4 justify-end">
                        {
                            hasPermission('epp.log.refresh')
                                ?
                                    <SecondaryButton
                                        processing={refreshProcess}
                                        onClick={() => refreshLog()}
                                    >
                                        {registry} : Refresh logs
                                    </SecondaryButton>
                                :
                                    null
                        }
                    </div>
                )}

                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    <a
                        href={route("epp.log", { registry: "verisign" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${
                                config.verisign.active &&
                                "bg-gray-100 text-link"
                            }`
                        }
                    >
                        <span className=" text-inherit">Verisign</span>
                    </a>
                    <a
                        href={route("epp.log", { registry: "pir" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${config.pir.active && "bg-gray-100 text-link"}`
                        }
                    >
                        <span className=" text-inherit">PIR</span>
                    </a>
                </div>

                {data.length == 0 ? (
                    <Search message="Select registry to view log" />
                ) : (
                    <>
                        <div className="flex justify-center">
                            <PrimaryButton onClick={() => moreLog()}>
                                Load more
                            </PrimaryButton>
                        </div>
                        <div className="border p-3 max-h-[100vh] overflow-x-hidden">
                            <ul className=" list-disc ">
                                {data.map((e, i) => {
                                    const charCode = e.charCodeAt(0);

                                    if (charCode > 47 && charCode < 58) {
                                        const logPart = e.split(" ", 4);

                                        return (
                                            <li key={"log-" + registry + i}>
                                                <span className=" text-sm space-x-2">
                                                    <span className="text-gray-800 pr-1">
                                                        {dataLength - i}
                                                    </span>
                                                    <span className=" bg-gray-500 text-gray-200 px-2 rounded-sm">
                                                        {logPart[0]}-
                                                        {logPart[1]}
                                                    </span>
                                                    <span
                                                        className={` ${
                                                            logColor[
                                                                logPart[2]
                                                            ] == undefined
                                                                ? "text-gray-900"
                                                                : logColor[
                                                                    logPart[2]
                                                                ]
                                                        } font-semibold`}
                                                    >
                                                        {logPart[2]}
                                                    </span>
                                                    <span>{logPart[3]}</span>
                                                </span>
                                            </li>
                                        );
                                    } else
                                        return (
                                            <p
                                                className=" text-sm text-gray-500 "
                                                key={"log-" + registry + i}
                                            >
                                                <span className="text-gray-800 pr-1">
                                                    {dataLength - i}
                                                </span>
                                                {e}
                                            </p>
                                        );
                                })}
                            </ul>
                        </div>
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
