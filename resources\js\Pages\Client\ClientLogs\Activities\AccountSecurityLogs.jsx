//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router, usePage } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { IoMdArrowBack } from "react-icons/io";
import {
    MdFilterList,
    MdOutlinePersonOutline,
    MdExitToApp,
    MdSecurity,
    MdOutlineCheckCircle,
    MdOutlineLibraryAddCheck,
    MdOutlineContactPage,
    MdOutlineSystemUpdateAlt,
    MdOutlineVerifiedUser,
    MdVisibility,
    MdMoreVert,
} from "react-icons/md";
import { HiOutlinePencilSquare } from "react-icons/hi2";
import { TbCategoryPlus } from "react-icons/tb";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import ActivitiesNav from "@/Components/Client/ClientLogs/ActivitiesNav";
import Filter from "@/Components/Client/ClientLogs/AcountSecurityLog/Filter";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import DropDownContainer from "@/Components/DropDownContainer";
import Payload from "@/Components/Client/ClientLogs/AcountSecurityLog/Payload";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AccountSecurityLogs()
{
    //! PACKAGE
    const { logs, email, user_id, statuses, pagination } = usePage().props;
    const { limit = 20 } = route().params ?? {};
    const dropdownRefs = useRef({});
    const dropdownContainerRef = useRef(null);

    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [isLoading, setIsLoading]               = useState(false);
    const [activeDropdown, setActiveDropdown]     = useState(null);
    const [payloadData, setPayloadData]           = useState(null);
    const [showPayloadModal, setShowPayloadModal] = useState(false);
    const [loadingPayload, setLoadingPayload]     = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(dropdownContainerRef, () => {
        setActiveDropdown(null);
    });

    const handleView = (logId) => {
        setLoadingPayload(true);
        axios.get(route("client.logs.security.payload", { id: logId }))
            .then(response => {
                console.log("Payload response:", response.data);
                const logData = logs.find(log => log.id === logId);
                // Combine log data with payload data for complete information
                setPayloadData({
                    ...logData,
                    ...response.data.payload
                });
                setShowPayloadModal(true);
                setLoadingPayload(false);
                setActiveDropdown(null); // Close dropdown after viewing
            })
            .catch(error => {
                console.error("Failed to load payload data:", error);
                setLoadingPayload(false);
                toast.error("Failed to load payload data");
            });
    };

    const handleLoading = (loading) => {
        setIsLoading(loading);
    };

    const onBackClick = () => {
        window.history.back();
    };

    const toggleDropdown = (logId) => {
        setActiveDropdown(activeDropdown === logId ? null : logId);
    };

    const typeIcons = {
        SIGN_IN: <MdOutlineCheckCircle />,
        SIGN_OUT: <MdExitToApp />,
        REGISTER: <MdOutlineLibraryAddCheck />,
        PROFILE_UPDATE: <HiOutlinePencilSquare />,
        SECURITY_UPDATE: <MdSecurity />,
        CATEGORY_UPDATE: <TbCategoryPlus />,
        CONTACT_UPDATE: <MdOutlineContactPage />,
        DOMAIN_UPDATE: <MdOutlineSystemUpdateAlt />,
        IDENTITY_VERIFICATION: <MdOutlineVerifiedUser />,
    };

    const getStatusLabel = (type) => {
        const normalizedType = type.replace(/ /g, "_").toUpperCase();
        const label = (statuses && statuses[normalizedType]) || type;
        const icon = typeIcons[normalizedType] || <MdOutlinePersonOutline />;
        return { label, icon };
    };

    const groupedLogs =
        logs?.length > 0
            ? logs.reduce((acc, log) => {
                const date = log.formatted_created_at;
                if (!acc[date]) {
                    acc[date] = [];
                }
                acc[date].push(log);
                return acc;
            }, {})
            : {};

    const LoadingSkeleton = () => (
        <div className="animate-pulse">
            {[1, 2, 3].map((group) => (
                <div key={group} className="mb-8">
                    {/* Date skeleton */}
                    <div className="flex items-center mb-4">
                        <div className="bg-gray-200 rounded-md w-32 h-8"></div>
                        <div className="flex-grow border-t border-gray-200 ml-2"></div>
                    </div>

                    {/* Table skeleton */}
                    <div className="bg-white mb-4">
                        {[1, 2, 3].map((row) => (
                            <div
                                key={row}
                                className="flex border-b border-gray-100 py-4"
                            >
                                <div className="w-2/5 px-4">
                                    <div className="flex items-center space-x-2">
                                        <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                                        <div className="w-24 h-4 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                                <div className="w-2/5 px-12">
                                    <div className="w-48 h-4 bg-gray-200 rounded"></div>
                                </div>
                                <div className="w-1/5 px-12">
                                    <div className="w-16 h-4 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );

    const handleLimitChange = (e) => {
        router.get(route("client.logs.security.all"), {
            ...route().params,
            limit: e.target.value,
            page: 1,
        });
    };

    const closePayloadModal = () => {
        setShowPayloadModal(false);
        setPayloadData(null);
    };

    return (
        <AdminLayout>
            <div className="flex flex-col">
                <div className="flex items-center space-x-2 mb-4 pl-6">
                    <button onClick={onBackClick} className="text-2xl pt-1">
                        <IoMdArrowBack />
                    </button>
                    <h2 className="text-2xl font-bold">
                        {user_id
                            ? `Client Activity Logs for: ${email}`
                            : "All Client Activity Logs"}
                    </h2>
                </div>
                <div className="flex flex-col md:flex-row">
                    <ActivitiesNav
                        activeTab="AccountSecurityLogs"
                        user_id={user_id}
                    />
                    <div
                        className={`px-4 ${
                            user_id ? "w-full md:w-5/6" : "w-full md:w-5/6"
                        }`}
                    >
                        <div className="mx-auto container max-w-[1200px] mt-4 flex flex-col justify-between">
                            <h2 className="text-4xl font-bold">
                                Account Security Logs
                            </h2>
                            <div
                                className="flex justify-start"
                                style={{ position: "relative", top: "15px" }}
                            >
                                <label className="mr-2 text-sm pt-1 text-gray-600">
                                    Show
                                </label>
                                <select
                                    value={limit}
                                    onChange={handleLimitChange}
                                    className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                                >
                                    {[20, 25, 30, 40, 50, 100].map((val) => (
                                        <option key={val} value={val}>
                                            {val}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="flex justify-between items-center mt-4 pt-4 pb-4">
                                <div className="flex items-center space-x-2">
                                    <MdFilterList className="text-xl" />
                                    <span>Filter:</span>
                                    <Filter
                                        routeName={
                                            user_id
                                                ? "client.logs.security"
                                                : "client.logs.security.all"
                                        }
                                        userId={user_id}
                                        userEmail={email}
                                        onLoading={handleLoading}
                                    />
                                </div>
                            </div>

                            {/* Content Area */}
                            <div className="mt-4">
                                {isLoading ? (
                                    <LoadingSkeleton />
                                ) : Object.keys(groupedLogs).length === 0 ? (
                                    <div className="bg-white rounded-md p-8 text-center text-gray-500">
                                        <p className="text-lg">No Logs Found</p>
                                    </div>
                                ) : (
                                    Object.entries(groupedLogs).map(
                                        ([date, logs]) => (
                                            <React.Fragment key={date}>
                                                <div className="flex items-center">
                                                    <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                                        {date}
                                                    </div>
                                                    <div className="flex-grow border-t border-gray-300 ml-2"></div>
                                                </div>
                                                <table className="min-w-full mb-4">
                                                    <tbody className="text-md">
                                                        {logs.map(
                                                            (item, index) => {
                                                                const {
                                                                    label,
                                                                    icon,
                                                                } =
                                                                    getStatusLabel(
                                                                        item.type
                                                                    );
                                                                return (
                                                                    <tr
                                                                        key={
                                                                            index
                                                                        }
                                                                    >
                                                                        <td className="w-2/5 py-2 px-4">
                                                                            <div className="flex items-center space-x-2 whitespace-nowrap overflow-hidden text-ellipsis">
                                                                                <span className="text-xl">
                                                                                    {
                                                                                        icon
                                                                                    }
                                                                                </span>
                                                                                <span>
                                                                                    {
                                                                                        label
                                                                                    }
                                                                                </span>
                                                                            </div>
                                                                        </td>
                                                                        <td className="w-2/5 py-3 px-12">
                                                                            {
                                                                                item.message
                                                                            }
                                                                        </td>
                                                                        <td className="w-1/5 py-3 px-6">
                                                                            {new Date(
                                                                                item.created_at
                                                                            ).toLocaleTimeString(
                                                                                "en-US",
                                                                                {
                                                                                    hour: "2-digit",
                                                                                    minute: "2-digit",
                                                                                    hour12: true,
                                                                                }
                                                                            )}
                                                                        </td>
                                                                        <td>
                                                                            <span
                                                                                ref={(el) => {
                                                                                    if (el) dropdownRefs.current[item.id] = el;
                                                                                    if (activeDropdown === item.id) dropdownContainerRef.current = el;
                                                                                }}
                                                                                className="relative"
                                                                            >
                                                                                <button
                                                                                    className="flex items-center"
                                                                                    onClick={() => toggleDropdown(item.id)}
                                                                                >
                                                                                    <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                                                                                </button>
                                                                                <DropDownContainer
                                                                                    show={activeDropdown === item.id}
                                                                                >
                                                                                    {                                                                                        
                                                                                        hasPermission('client.logs.security.payload') 
                                                                                            ?
                                                                                                <button
                                                                                                    className="hover:bg-gray-100 px-5 py-1 flex items-center space-x-2"
                                                                                                    disabled={loadingPayload}
                                                                                                    onClick={() => handleView(item.id)}
                                                                                                >
                                                                                                    {loadingPayload ? (
                                                                                                        <>
                                                                                                            <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                                                                                                            <span>Loading...</span>
                                                                                                        </>
                                                                                                    ) : (
                                                                                                        <span>View Payload</span>
                                                                                                    )}
                                                                                                </button>
                                                                                            :
                                                                                                <div
                                                                                                    className='font-semibold capitalize text-danger px-5 py-1'
                                                                                                >
                                                                                                    no actions permitted
                                                                                                </div>
                                                                                    }
                                                                                </DropDownContainer>
                                                                            </span>
                                                                        </td>
                                                                    </tr>
                                                                );
                                                            }
                                                        )}
                                                    </tbody>
                                                </table>
                                            </React.Fragment>
                                        )
                                    )
                                )}
                            </div>

                            {!isLoading && (
                                <div className="mt-6">
                                    <CursorPaginate
                                        onFirstPage={
                                            pagination.current_page === 1
                                        }
                                        onLastPage={
                                            pagination.current_page ===
                                            pagination.last_page
                                        }
                                        nextPageUrl={pagination.next_page_url}
                                        previousPageUrl={
                                            pagination.prev_page_url
                                        }
                                        itemCount={pagination.itemCount}
                                        total={pagination.total}
                                        itemName="log"
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {showPayloadModal && !loadingPayload && (
                <Payload
                    data={payloadData}
                    onClose={closePayloadModal}
                />
            )}
        </AdminLayout>
    );
}
