import React from 'react';
import Modal from '@/Components/Modal';
import SecondaryButton from '@/Components/SecondaryButton';
import DangerButton from '@/Components/DangerButton';

export default function DeleteConfirmationModal({ show, onClose, onConfirm, itemCount = 1 }) {
    return (
        <Modal show={show} onClose={onClose}>
            <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900">
                    Delete Confirmation
                </h2>

                <p className="mt-3 text-sm text-gray-600">
                    Are you sure you want to delete {itemCount} {itemCount === 1 ? 'notification' : 'notifications'}? This action cannot be undone.
                </p>

                <div className="mt-6 flex justify-end gap-4">
                    <SecondaryButton onClick={onClose}>Cancel</SecondaryButton>
                    <DangerButton onClick={onConfirm}>Delete</DangerButton>
                </div>
            </div>
        </Modal>
    );
} 