<?php

namespace App\Listeners;

use App\Events\DomainHistoryEvent;
use Exception;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;

class DomainListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  DomainHistoryEvent  $event
     * @return void
     */
    public function handle(DomainHistoryEvent $event)
    {
        try {
            $this->createTransactionHistory($event);
            app(AuthLogger::class)->info('Domain History: '.$event->message);
        } catch (Exception $e) {
            $this->createTransactionHistory($event, 'failed');
            app(AuthLogger::class)->error('Domain History: '.$e->getMessage());
        }
    }

    private function createTransactionHistory(DomainHistoryEvent $event, ?string $overrideStatus = null): void
    {
        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $event->domainId,
            'type' => $event->type,
            'user_id' => $event->userId,
            'status' => $overrideStatus ?? $event->status,
            'message' => $event->message,
            'payload' => json_encode($event->payload),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}