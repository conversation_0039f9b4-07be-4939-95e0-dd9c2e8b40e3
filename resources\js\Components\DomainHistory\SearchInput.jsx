import React, { useState, useEffect } from "react";
import { CiSearch } from "react-icons/ci";
import { usePage } from "@inertiajs/react";

export default function SearchInput({ onSearchChange, placeholder }) {
    const { search } = usePage().props;
    const [searchTerm, setSearchTerm] = useState(search || "");

    useEffect(() => {
        setSearchTerm(search || "");
    }, [search]);

    const handleChange = (e) => {
        const value = e.target.value;
        setSearchTerm(value);
        onSearchChange({ search: value });
    };

    return (
        <div className="relative">
            <input
                type="text"
                value={searchTerm}
                placeholder={placeholder || "Search user"}
                className="border rounded px-2 py-1 pl-8"
                onChange={handleChange}
            />
            <span className="absolute left-2 top-1/2 transform -translate-y-1/2">
                <CiSearch className="text-gray-400" />
            </span>
        </div>
    );
}
