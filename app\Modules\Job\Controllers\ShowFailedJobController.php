<?php

namespace App\Modules\Job\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Job\Requests\ShowFailedJobForm;
use Inertia\Inertia;

class ShowFailedJobController extends Controller
{
    public function index(ShowFailedJobForm $request)
    {
        return Inertia::render('Job/Failed', $request->index());
    }

    public function get(ShowFailedJobForm $request)
    {
        return Inertia::render('Job/FailedQueue', ['job' => $request->getById()]);
    }
}
