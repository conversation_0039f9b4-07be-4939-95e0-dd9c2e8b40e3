import AdminLayout from "@/Layouts/AdminLayout";
import { Link } from "@inertiajs/react";
import { MdArrowBack } from "react-icons/md";
import { useState } from "react";
import CursorPaginate from "@/Components/Util/CursorPaginate";

export default function UsersList({ notification }) {
    const [currentPage, setCurrentPage] = useState(1);
    const usersPerPage = 10;
    
    // Calculate pagination
    const indexOfLastUser = currentPage * usersPerPage;
    const indexOfFirstUser = indexOfLastUser - usersPerPage;
    const currentUsers = notification.users?.list?.slice(indexOfFirstUser, indexOfLastUser) || [];
    const totalPages = Math.ceil((notification.users?.list?.length || 0) / usersPerPage);

    return (
        <AdminLayout>
            <div className="flex justify-center min-h-screen">
                <div className="w-full max-w-3xl py-6 px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center mb-8">
                        <Link
                            href={route('notification.management')}
                            className="text-xl font-normal flex items-center text-gray-900"
                        >
                            <MdArrowBack className="mr-2 text-4xl" /> List of Users
                        </Link>
                    </div>
                    
                    <div className="p-6">
                        <div className="space-y-3 mb-8">
                            <div>
                                <span className="font-medium">Title:</span>{" "}
                                <span>{notification.title}</span>
                            </div>
                            <div>
                                <span className="font-medium">Status:</span>{" "}
                                <span>{notification.status.charAt(0).toUpperCase() + notification.status.slice(1)}</span>
                            </div>
                            <div>
                                <span className="font-medium">Type:</span>{" "}
                                <span>{notification.type}</span>
                            </div>
                            <div>
                                <span className="font-medium">Schedule:</span>{" "}
                                <span>{notification.schedule_type.charAt(0).toUpperCase() + notification.schedule_type.slice(1)}</span>
                            </div>
                            {/* <div>
                                <span className="font-medium">Schedule:</span>{" "}
                                <span>{notification.min}</span>
                            </div> */}
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-4">
                                <span className="font-medium">Users:</span>
                                {/* <span className="text-sm text-gray-600">
                                    Total: {notification.users?.list?.length || 0} users
                                </span> */}
                            </div>
                            
                            <div className="mt-2 space-y-2">
                                {currentUsers.map((user, index) => (
                                    <div 
                                        key={index}
                                        className="flex items-center justify-between bg-gray-100 px-3 py-2 rounded"
                                    >
                                        <span className="text-gray-900">
                                            {user.email}
                                        </span>
                                        <span 
                                            className={`px-3 py-1 text-xs rounded text-white ${
                                                user.read_at
                                                    ? 'bg-blue-500' 
                                                    : 'bg-red-500'
                                            }`}
                                        >
                                            {user.read_at ? 'Viewed' : 'Unviewed'}
                                        </span>
                                    </div>
                                ))}
                            </div>

                            {notification.users?.list?.length > 0 && (
                                <div className="mt-4">
                                    <CursorPaginate
                                        onFirstPage={notification.users.current_page === 1}
                                        onLastPage={notification.users.current_page === notification.users.last_page}
                                        nextPageUrl={route('notification.management.users', [
                                            notification.id,
                                            { page: notification.users.current_page + 1 }
                                        ])}
                                        previousPageUrl={route('notification.management.users', [
                                            notification.id,
                                            { page: notification.users.current_page - 1 }
                                        ])}
                                        itemCount={notification.users.list.length}
                                        total={notification.users.count}
                                        itemName="user"
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
} 