<?php

namespace App\Modules\Setting\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Setting\Constants\FeeType;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Validator as ValidationValidator;

class ExtensionFeeService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance()
    {
        $extensionFeeService = new self;

        return $extensionFeeService;
    }

    public function store($userId)
    {
        if (! DB::client()->table('users')->where('id', $userId)->exists()) {
            throw new FailedRequestException(400, 'User not found', 'Bad request');
        }

        $template = [
            'extension_id' => 0,
            'fee_id' => 0,
            'value' => 0,
            'is_default' => false,
            'user_id' => $userId,
        ];

        // dd($template);
        $fees = DB::client()->table('fees')->get()->all();
        $extensions = DB::client()->table('extensions')->get()->all();

        foreach ($extensions as $key => $value) {
            $template['extension_id'] = $value->id;
            foreach ($fees as $key => $value) {
                $template['fee_id'] = $value->id;
                $template['created_at'] = now();
                $template['updated_at'] = now();
                DB::client()->table('extension_fees')->insert($template);
            }
        }
    }

    public function getDefault()
    {
        $list = ExtensionFees::instance()->getDefaultQuery();
        $settings = [];

        // dd($list);
        foreach ($list as $value) {
            if (in_array($value->type, FeeType::EXTENSION_FEES)) {
                $value->nameType = $value->type === FeeType::RENEW ? FeeType::RENEWAL : $value->type;
                $extension = strtolower($value->extension_name);
                $settings[$extension][$value->type] = (array) $value;
            }
        }

        // ddd($settings);

        return $settings;
    }

    public function getUsersFeeByUserIds($userIds)
    {
        // dd($userIds);
        $list = ExtensionFees::instance()->getUserCustomFeesByUserIds($userIds);

        $fees = [];
        foreach ($list as $value) {
            if (in_array($value->type, FeeType::EXTENSION_FEES)) {
                $extension = strtolower($value->extension_name);
                $fees[$value->user_id][$extension][$value->type] = (array) $value;
            }
        }

        // dd($fees);
        return $fees;
    }

    public function update($id, $ext, $type, $value, $extensionTotal, $isDefault)
    {
        $validator = self::validateUpdateValues($id, $ext, $type, $value, $extensionTotal, $isDefault);

        if ($validator && $validator->errors()) {
            return $validator;
        }

        $data = [
            'value' => $value,
            'updated_at' => now(),
        ];

        DB::client()->table('extension_fees')
            ->whereNull('extension_fees.deleted_at')
            ->where('id', $id)
            ->update($data);

        app(AuthLogger::class)->info('update ' . $ext . ' extension fee of ' . $type . ' to ' . $value);

        return $validator;
    }

    public function resetAllExtensionFees(int $defaultId): void
    {
        $fee_id = DB::client()->table('extension_fees')->where('id', $defaultId)->where('is_default', true)->value('fee_id');
        if ($fee_id) {
            DB::client()->table('extension_fees')->where('fee_id', $fee_id)->where('is_default', false)->update(['value' => 0]);
        }
    }

    public function getCustomFeeByUserId($id)
    {
        $defaultFees = $this->getDefault();

        $customFees = ExtensionFees::instance()->getUserCustomFeeById($id);

        $settings = [];

        foreach ($customFees as $key => $value) {
            if (in_array($value->type, FeeType::EXTENSION_FEES)) {
                $extension = strtolower($value->extension_name);

                $value->extensionValue = $defaultFees[$extension][$value->type]['value'];
                $value->extensionTotal = $value->fee_value + $value->extensionValue;
                $value->nameType = $value->type == FeeType::RENEW ? 'RENEWAL' : $value->type;

                $settings[$extension][$value->type] = (array) $value;
            }
        }

        // ddd($settings);

        return $settings;
    }

    public function getCustomFeeByUserIdOld($id)
    {
        $list = DB::client()->table('extension_fees')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('extensions', 'extensions.id', '=', 'extension_fees.extension_id')
            ->join('tlds', 'tlds.extension_id', '=', 'extension_fees.extension_id')
            ->where('user_id', $id)
            ->whereNull('extension_fees.deleted_at')
            ->select(
                'extension_fees.*',
                'fees.type',
                'fees.value as fee_value',
                'extensions.name as extension_name',
                'tlds.id as tld_id'
            )
            ->orderby('extensions.id', 'asc')
            ->orderby('fees.type', 'asc')
            ->get()->all();

        $settings = [];

        //dd($list);
        foreach ($list as $value) {
            if (in_array($value->type, FeeType::EXTENSION_FEES)) {
                $value->nameType = $value->type == FeeType::RENEW ? 'RENEWAL' : $value->type;
                $extension = strtolower($value->extension_name);
                $settings[$extension][$value->type] = (array) $value;
            }
        }

        return $settings;
    }

    public function softDelete(array $userIds)
    {
        foreach ($userIds as $id) {
            $this->deleteItems(
                $id,
                [
                    'deleted_at' => now(),
                    'updated_at' => now(),
                ]
            );
        }

        app(AuthLogger::class)->info('deleted extension fees ' . implode(',', $userIds));
    }

    private function deleteItems($id, array $fields)
    {
        return DB::client()->table('extension_fees')
            ->whereNull('deleted_at')
            ->where('user_id', $id)
            ->update($fields);
    }

    public function resetFees($userId)
    {
        $value = 0;

        $data = [
            'value' => $value,
            'updated_at' => now(),
        ];
        DB::client()->table('extension_fees')
            ->whereNull('extension_fees.deleted_at')
            ->where('user_id', $userId)
            ->update($data);

        app(AuthLogger::class)->info('reset extension fee of user ' . $userId);
    }

    private function validateUpdateValues(int $id, string $extension, string $type, float $value, float $extensionTotal, bool $isDefault = false)
    {
        $validator = Validator::make([], []);

        if (! FeeSettingService::validateFeeType($type)) {
            $validator->errors()->add('message', 'Fee type name does not exists.');

            return $validator;
        }

        if (! FeeSettingService::validateExtensionName($extension)) {
            $validator->errors()->add('message', 'Extension name does not exists');

            return $validator;
        }

        if (! self::validateExtensionTotal($extensionTotal, $value)) {
            $validator->errors()->add('message', 'Value not valid.');

            return $validator;
        }

        if ($isDefault) {
            return $this->validateClientFeeValue($validator, $type, floatval($extensionTotal + $value));
        }
    }

    private function validateExtensionTotal(float $extensionTotal, float $value)
    {
        $computedValue = $extensionTotal + $value;

        return FeeSettingService::validateFeeValue($computedValue);
    }

    private function validateClientFeeValue(ValidationValidator $validator, string $type, float $value)
    {
        $isMinimum = FeeSettingService::validateMinimumExtensionValue($type, floatval($value));

        if (array_key_exists('status', $isMinimum) && ! $isMinimum['status']) {
            $validator->errors()->add('message', $isMinimum['message']);

            return $validator;
        }
    }
}
