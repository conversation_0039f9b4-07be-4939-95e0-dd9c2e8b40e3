<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Services\ClientDomainLogService;
use App\Modules\Client\Requests\DomainLogRequest;
use Inertia\Response;
use Inertia\Inertia;
use Illuminate\Http\JsonResponse;

class ClientDomainLogController extends Controller
{
    protected $domainLogService;

    public function __construct(ClientDomainLogService $domainLogService)
    {
        $this->domainLogService = $domainLogService;
    }

    public function index(DomainLogRequest $request, ?int $userId = null): Response
    {
        return Inertia::render('Client/ClientLogs/Activities/DomainLogs', 
            $this->domainLogService->getDomainLogsData($userId, $request)
        );
    }
    
    public function getPayload(DomainLogRequest $request): JsonResponse
    {
        $payload = $this->domainLogService->getLogPayload($request);
        return response()->json([
            'payload' => $payload
        ]);
    }
}
