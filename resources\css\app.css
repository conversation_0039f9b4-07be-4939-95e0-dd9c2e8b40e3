@tailwind base;
@tailwind components;
@tailwind utilities;

h1,
h2,
h3,
h4,
h5,
h6,
label {
    @apply text-gray-700;
}

.hide-transition {
    animation: hideitem 2s;
    @apply absolute invisible overflow-hidden;
}

@keyframes hideitem {
    from {
        @apply relative bg-green-200;
    }
    to {
        @apply absolute;
    }
}

/* scrollbar */
::-webkit-scrollbar {
    width: 8px;
    border-radius: 10px;
}

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    border-radius: 2rem;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2rem;
}

.fade-in-out {
    @apply transition hover:ease-in ease-out delay-100 duration-200;
}

.tooltip {
    @apply invisible absolute;
  }
  
  .has-tooltip:hover .tooltip {
    @apply visible z-50;
  }