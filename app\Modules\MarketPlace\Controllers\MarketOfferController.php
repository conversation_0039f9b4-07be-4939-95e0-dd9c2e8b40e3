<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Constants\AfternicOfferConstants;
use App\Modules\MarketPlace\Requests\OfferHistoryRequest;
use App\Modules\MarketPlace\Requests\OfferUpdateRequest;
use App\Modules\MarketPlace\Services\MarketOfferService;
use Illuminate\Support\Collection;
use Inertia\Inertia;
use Inertia\Response;

class MarketOfferController extends Controller
{
    public function index() : Response
    {
        return Inertia::render('MarketPlace/Offers/ShowOffers', ['myoffers' => MarketOfferService::instance()->getOffers(), 'options' => AfternicOfferConstants::All]);
    }

    public function update(OfferUpdateRequest $request) : void
    {
        $request->update();
    }

    public function history(OfferHistoryRequest $request) : Collection
    {
        return $request->getHistory();
    }
}
