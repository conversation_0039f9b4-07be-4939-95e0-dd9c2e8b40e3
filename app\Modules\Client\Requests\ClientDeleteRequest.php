<?php

namespace App\Modules\Client\Requests;

use App\Modules\Client\Services\ClientService;
use Illuminate\Foundation\Http\FormRequest;

class ClientDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'data.*' => ['required', 'array'],
        ];
    }

    public function softDelete()
    {
        ClientService::softDelete($this->all());
    }
}
