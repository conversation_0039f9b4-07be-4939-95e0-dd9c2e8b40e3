<?php

namespace App\Modules\Ip\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Ip\Requests\IpUpdateForm;
use App\Modules\Ip\Requests\ShowListRequest;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class IpAddressController extends Controller
{
    public function index(ShowListRequest $request)
    {
        // $ips = DB::client()->table('ips')->orderBy('id', 'desc')->get()->all();
        return Inertia::render('Ip/Index', $request->show());
    }

    public function update(IpUpdateForm $request)
    {
        $request->updateStatus();

        return redirect()->back();
    }
}
