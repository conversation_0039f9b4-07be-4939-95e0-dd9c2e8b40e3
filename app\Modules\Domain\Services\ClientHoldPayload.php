<?php

namespace App\Modules\Domain\Services;

use App\Modules\Epp\Constants\EppDomainStatus;

class ClientHoldPayload
{
    private static ?self $instance = null;
    private object $domain;
    private array $eppInfo;
    private string $action;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function initialize(object $domain, array $eppInfo, string $action): self
    {
        $this->domain = $domain;
        $this->eppInfo = $eppInfo;
        $this->action = $action;
        return $this;
    }

    public function get(): array
    {
        $payload = [
            'name' => $this->domain->name
        ];

        if ($this->action === 'client_unhold') {
            $payload['statusRemove'] = [EppDomainStatus::CLIENT_HOLD];
        } else {
            $payload['status'] = [EppDomainStatus::CLIENT_HOLD];
        }

        return $payload;
    }
} 