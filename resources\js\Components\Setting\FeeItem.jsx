//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdMode, MdOutlinePendingActions } from "react-icons/md";

//* COMPONENTS
import InputLabel from "@/Components/InputLabel";
import PrimaryButton from "@/Components/PrimaryButton";
import SecondaryButton from "@/Components/SecondaryButton";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function FeeItem(
    {
        item
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);
    const [updateValue, setUpdateValue] = useState(item.value);
    const [itemValue, setItemValue] = useState(item.value);


    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const onHandleChange = (event) => setUpdateValue(getEventValue(event));

    const handleUpdate = () => {
        setShow(false);

        var itemValue = parseFloat(updateValue);
        if (!itemValue || itemValue < 0) {
            toast.error("Input Not Accepted.");
        } else {
            router.patch(route("setting.fee-update"), {
                type: item.type,
                value: updateValue,
            }, {
                onError: (errors) => toast.error(errors.message, { autoClose: 8000 }),
                onSuccess: () => {
                    toast.success("Value updated.");
                    setItemValue(updateValue);
                },
            });
        }
    };

    return (
        <div className="hover:bg-gray-100 p-2 rounded-sm">
            <InputLabel forInput="" value={item.nameType} />
            <div className="flex justify-between pb-4">
                <span className="font-semibold">${parseFloat(itemValue).toFixed(2)}</span>

                {
                    hasPermission("setting.fee-update") 
                        ?
                            !show 
                                ? 
                                    (
                                        <button
                                            className="inline-flex items-center text-gray-500 hover:text-primary"
                                            onClick={() => setShow(!show)}
                                        >
                                            <MdMode /> Edit
                                        </button>                                    )
                                :
                                    (
                                        <span className="inline-flex items-center text-gray-500 hover:text-primary cursor-pointer">
                                            <MdOutlinePendingActions /> draft
                                        </span>
                                    )
                        : 
                            null
                }
            </div>

            <div className={`${!show && "hidden"}`}>
                <TextInput
                    type="number"
                    name="name"
                    placeholder="new value"
                    className="w-full mb-3"
                    value={updateValue}
                    handleChange={onHandleChange}
                />
                <div className="inline-flex space-x-4">
                    <PrimaryButton onClick={() => handleUpdate()}>
                        Save
                    </PrimaryButton>
                    <SecondaryButton onClick={() => setShow(false)}>
                        Cancel
                    </SecondaryButton>
                </div>
            </div>
        </div>
    );
}
