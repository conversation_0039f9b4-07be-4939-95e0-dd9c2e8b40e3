import React, { useRef, useState } from "react";
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useOutsideClick from "@/Util/useOutsideClick";

export default function Filter({ onFilter, transactionTypes }) {
    const { email, type, date, status } = route().params;
    const containerRef = useRef();
    
    const [emailInput, setEmailInput] = useState(email || "");

    const config = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            type: {
                active: false,
                value: type ? [type.replace(/_/g, " ")] : [],
                type: "option",
                items: Object.values(transactionTypes).map((item) =>
                    item.replace(/_/g, " ")
                ),
                name: "Last Activity",
            },
            status: {
                active: false,
                value: status ? [`Client ${status.charAt(0).toUpperCase() + status.slice(1)}`] : [],
                type: "option",
                items: ["Client Hold", "Client Unhold"],
                name: "Status",
            },
            email: {
                active: false,
                value: email ? [email] : [],
                type: "text",
                name: "Last Action By",
                tempValue: emailInput,
            },
            date: {
                active: false,
                value: date ? [date] : [],
                type: "option",
                items: ["today", "yesterday", "last 7 days", "last 30 days"],
                name: "Date",
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        const { email, type, date, status } = updatedFilter.field;
        let payload = {};

        if (email.value.length > 0) payload.email = email.value[0];
        if (type.value.length > 0) {
            payload.type = type.value[0].toUpperCase().replace(/ /g, "_");
        }
        if (date.value.length > 0) payload.date = date.value[0];
        if (status.value.length > 0) {
            payload.status = status.value[0].toLowerCase().replace('client ', '').trim();
        }

        if (onFilter) {
            onFilter(payload);
        } else {
            router.get(route("domain.history"), payload, {
                preserveState: true,
                preserveScroll: true,
            });
        }
    };

    const handleDisplayToggle = (newObject) => {
        const updated = { ...filter, ...newObject };
        setFilter(updated);
    };

    const handleFieldUpdateValue = (key, value, forceReload = true) => {
        if (key === "email") {
            setEmailInput(value);
            
            if (!value || value === emailInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false, reload: forceReload },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload: forceReload },
            field: {
                ...filter.field,
                [key]: { ...newValue, active: false },
            },
        };

        setFilter(updatedFilter);
        submit(updatedFilter);
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={field.type}
                    fieldKey="type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    placeholder="Search email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
                <OptionFilter
                    fieldProp={field.date}
                    fieldKey="date"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
