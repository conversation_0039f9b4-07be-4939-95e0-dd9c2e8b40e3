import React from 'react';

function DomainTransferRequestInitiated({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">{data.greeting}</p>
                <p className="mt-4">We want to inform you that a transfer request has been initiated for your domain <strong>{data.domain_name}</strong>.  The domain status has been updated to "In Process" while we handle the transfer request. If you did not authorize this request, please take immediate action to secure your domain.</p>
                <p className="mt-2">Please note that this process may take some time to complete. We will keep you updated on any significant changes to your transfer status.</p>
                <ul className="mt-4 list-disc list-inside pl-4">
                  <li><strong>Domain Name:</strong> {data.domain_name}</li>
                  <li><strong>Date:</strong> {data.request_date}</li>
                </ul>
                <p className="mt-4">You can view this transaction in your account dashboard by visiting <a className="text-blue-600 underline italic">this link</a>.</p>
                <p className="mt-4">If you have any questions or need assistance, feel free to contact us at <a className="text-blue-600 underline italic">StrangeDomains.com</a>. Or call us at <strong>+**********</strong>.</p>
                <p className="mt-4">Thank you for choosing Strange Domains.</p>

                <p className="mt-4 font-bold mb-2">Sincerely,</p>
                <p className="font-bold">{data.sender_name}</p>
            </div>
        </div>
    );
}

export default DomainTransferRequestInitiated;