<?php

namespace App\Modules\BillingClient\Services\Interfaces;

use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Modules\BillingClient\Contracts\PaymentSummaryInterface;
use App\Modules\BillingClient\Services\MarketInvoiceService;
use App\Modules\CustomLogger\Services\UserLoggerTrait;

class MarketPlacePaymentSummary implements PaymentSummaryInterface
{
    use UserLoggerTrait;

    public function getPaymentbyId(int $id, int $userId)
    {
        $data = MarketInvoiceService::instance()->getMarketPlaceInvoice($id, $userId);

        return [
            'data' => $data,
            'icann_total' => 0,
            'summary_type' => PaymentSummaryType::MARKETPLACE_INVOICE,
        ];
    }

    public function getRefundbyId(int $id, int $userId)
    {
        return MarketInvoiceService::instance()->getPaymentReimbursement($id, $userId);
    }
}
