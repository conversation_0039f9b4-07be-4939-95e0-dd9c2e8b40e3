<?php

namespace App\Modules\Guest\Requests;

use App\Modules\Guest\Constants\RequestStatus;
use App\Modules\Guest\Services\AdminResponseService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AdminResponseForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'ids' => ['required', 'array'],
            'status' => ['required', Rule::in(RequestStatus::all())],
        ];
    }

    public function update(): void
    {
        AdminResponseService::updateStatusAll($this->ids, $this->status);
        AdminResponseService::queueUpdate($this->ids, $this->status);
    }
}
