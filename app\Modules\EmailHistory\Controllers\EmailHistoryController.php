<?php

namespace App\Modules\EmailHistory\Controllers;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use App\Modules\EmailHistory\Services\EmailHistoryService;
use Illuminate\Http\Request;

class EmailHistoryController extends Controller
{
    protected $emailHistoryService;

    public function __construct(EmailHistoryService $emailHistoryService)
    {
        $this->emailHistoryService = $emailHistoryService;
    }

    public function index(Request $request)
    {
        $data = $this->emailHistoryService->getEmailsForIndex($request);

        return Inertia::render('EmailHistory/Index', $data);
    }

    public function downloadAttachment($id)
    {
        return $this->emailHistoryService->getAttachmentResponse($id);
    }
}
