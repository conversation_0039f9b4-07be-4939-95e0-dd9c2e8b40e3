import LoaderSpinner from "@/Components/LoaderSpinner";
import {
    ImSortAlphaAsc,
    ImSortAlphaDesc,
} from 'react-icons/im';
import {
    TbSortAscending2,
    TbSortDescending2
} from 'react-icons/tb';

import "react-toastify/dist/ReactToastify.css";
import Item from "../../../Components/Billing/Client/Item";
import { _BillingClient } from "../../../Constant/_BillingClient";

export default function IndexTable({ hasSpinner, orderby, items, orderToggle }) {
    return (
        <div className="relative">
            <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                <thead className=" bg-gray-50 text-sm">
                    <tr>
                        <th>
                            <label className="flex items-center pl-2 space-x-2">
                                <span className="">Date</span>
                                <button
                                    onClick={() => orderToggle(orderby === _BillingClient.sortTypes.DATE_ASC ? _BillingClient.sortTypes.DATE_DESC : _BillingClient.sortTypes.DATE_ASC)}
                                    disabled={items.length === 0}
                                >
                                    {orderby === _BillingClient.sortTypes.DATE_ASC ? <TbSortAscending2 /> : <TbSortDescending2 />}
                                </button>
                            </label>
                        </th>
                        <th>
                            <label className="flex items-center pl-2 space-x-2">
                                <span className="">Name</span>
                                <button
                                    onClick={() => orderToggle(orderby === _BillingClient.sortTypes.NAME_ASC ? _BillingClient.sortTypes.NAME_DESC : _BillingClient.sortTypes.NAME_ASC)}
                                    disabled={items.length === 0}
                                >
                                    {orderby === _BillingClient.sortTypes.NAME_ASC ? <ImSortAlphaAsc /> : <ImSortAlphaDesc />}
                                </button>
                            </label>
                        </th>
                        <th>
                            <label className="flex items-center pl-2 space-x-2">
                                <span>Email</span>
                                <button
                                    onClick={() => orderToggle(orderby === _BillingClient.sortTypes.EMAIL_ASC ? _BillingClient.sortTypes.EMAIL_DESC : _BillingClient.sortTypes.EMAIL_ASC)}
                                    disabled={items.length === 0}
                                >
                                    {orderby === _BillingClient.sortTypes.EMAIL_ASC ? <ImSortAlphaAsc /> : <ImSortAlphaDesc />}
                                </button>
                            </label>

                        </th>
                        <th>
                            <label className="flex items-center pl-2 space-x-2">
                                <span className="">Type</span>
                                <button
                                    onClick={() => orderToggle(orderby === _BillingClient.sortTypes.TYPE_ASC ? _BillingClient.sortTypes.TYPE_DESC : _BillingClient.sortTypes.TYPE_ASC)}
                                    disabled={items.length === 0}
                                >
                                    {orderby === _BillingClient.sortTypes.TYPE_ASC ? <ImSortAlphaAsc /> : <ImSortAlphaDesc />}
                                </button>
                            </label>
                        </th>
                        <th>
                            <label className="flex items-center pl-2 space-x-2">
                                <span>Source</span>
                            </label>
                        </th>
                        <th>
                            <label className="flex items-center pl-2 space-x-2">
                                <span>Amount ($)</span>
                            </label>
                        </th>
                        {/* <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th> */}
                    </tr>
                </thead>
                <tbody className="text-sm">
                    {hasSpinner ? (
                        <tr>
                            <td colSpan={7}>
                                <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                            </td>
                        </tr>
                    ) : (
                        <>
                            {items.map((user, index) => (
                                <Item
                                    item={user}
                                    key={"dl-" + index}
                                />
                            ))}
                        </>
                    )
                    }
                </tbody>
            </table>
        </div>

    );
}
