//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdAppRegistration, MdAssignmentReturn } from "react-icons/md";

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { convertDateUtil, convertDateToTimeUtil } from '@/Util/DateTimeHelperUtils';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferPaymentInvoice(
    {
        payment, 
        invoiceOrders, 
        classNames
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    const STATUS_REFUND           = 'REFUNDED';
    const STATUS_REFUND_REQUESTED = 'Refund Requested';

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const convertNameFormat = (str) =>
    {
        str = str.toLowerCase();

        const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));

        return words.join(' ');
    }

    return (
        <div
            className={`
                flex flex-col gap-8
                ${classNames}
            `}
        >
            <div
                className=''
            >
                {convertDateUtil(payment.created_at, 1)} {convertDateToTimeUtil(payment.created_at)}
            </div>
            
            {
                invoiceOrders.map(
                    (invoiceOrder, invoiceOrderIndex) => 
                    {
                        return (
                            <div
                                key={invoiceOrderIndex}
                                className='flex flex-col gap-4'
                            >
                                <div
                                    className='flex flex-col gap-2'
                                >
                                    <div 
                                        className="flex justify-between"
                                    >
                                        <div
                                            className="font-semibold flex payments-center gap-2"
                                        >
                                            <span>
                                                {invoiceOrder.name}
                                            </span>
                                            <span
                                                className="bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex payments-center"
                                            >
                                                Premium
                                            </span>
                                        </div>
                                        <div>
                                            ${invoiceOrder.price}
                                        </div>
                                    </div>
                                    <div 
                                        className="flex justify-between"
                                    >
                                        <div
                                            className=""
                                        >
                                            Transfer Fee
                                        </div>
                                        <div>
                                            ${invoiceOrder.total_domain_amount} / {invoiceOrder.year_length} year 
                                        </div>
                                    </div>
                                    <div 
                                        className="flex justify-between"
                                    >
                                        <div
                                            className=""
                                        >
                                            ICANN Fee
                                        </div>
                                        <div>
                                            ${invoiceOrder.total_icann_fee}
                                        </div>
                                    </div>
                                    <div 
                                        className="flex justify-between font-semibold"
                                    >
                                        <div
                                            className=""
                                        >
                                            Sub Total 
                                        </div>
                                        <div>
                                            ${invoiceOrder.gross_amount}
                                        </div>
                                    </div>
                                </div>
                                <hr />
                                <div
                                    className="flex items-center"
                                >
                                    <MdAppRegistration className=" text-lg" />
                                    <span className="ml-2 text-md text-gray-600">
                                        {/* {item.node_type} */}
                                        {(invoiceOrder.node_type) && convertNameFormat(invoiceOrder.node_type)}
                                    </span>
                                    <span className="ml-2 text-md text-gray-600">
                                        {invoiceOrder.node_status && ((invoiceOrder.node_status === STATUS_REFUND) ? STATUS_REFUND_REQUESTED : convertNameFormat(invoiceOrder.node_status))}
                                    </span>
                                </div>
                            </div>
                        )
                    }
                )
            }
            <div 
                className="flex justify-between font-bold"
            >
                <div
                    className="flex payments-center gap-2"
                >
                    Total Amount
                </div>
                <div>
                    ${payment.amount}
                </div>
            </div>
        </div>
    );
}
