<?php

namespace App\Modules\Activity\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Activity\Services\AdminActivityService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdminActivityController extends Controller
{
    public function index()
    {
        return Inertia::render('Activity/Admin', ['log' => AdminActivityService::get(0)]);
    }

    public function more(Request $request)
    {
        $day = $request->has('page') ? $request->page : 0;

        return response()->json(['log' => AdminActivityService::get($day)], 200);
    }
}
