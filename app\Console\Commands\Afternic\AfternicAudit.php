<?php

namespace App\Console\Commands\Afternic;

use Exception;
use Illuminate\Console\Command;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\MarketAuditService;

class AfternicAudit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:afternic-audit';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'AfternicAudit: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('AfternicAudit: Running...');
        
        MarketAuditService::instance()->generateAudit();
        // MarketAuditService::instance()->sendEmail("Dec-01-2024_Dec-31-2024", 'code', 'test');

        app(AuthLogger::class)->info('AfternicAudit: Done');
    }
}
