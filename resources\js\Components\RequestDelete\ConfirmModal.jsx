import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";

export default function ConfirmModal({ isOpen, onClose, onConfirm, deletionRequest }) {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-40" onClose={onClose}>
        <div className="fixed inset-0 bg-black/40" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="bg-white p-6 rounded-lg w-full max-w-sm shadow-xl">
            <Dialog.Title className="text-lg font-bold">
              Confirm Submission
            </Dialog.Title>
            <div className="mt-4">
              <p>Are you sure you want to submit this support note?</p>
            </div>
            <div className="mt-6 flex justify-end gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 rounded"
              >
                Cancel
              </button>
              <button
                // onClick={() => {
                //     if (typeof onConfirm === "function") {
                //         onConfirm(deletionRequest.domainName);
                //     } else {
                //         console.warn("onConfirm is not a function");
                //     }
                //     }}
                onClick={() => onConfirm(deletionRequest.domainName)}
                className="px-4 py-2 bg-green-600 text-white rounded"
              >
                Yes, Confirm - {deletionRequest.domainName}
              </button>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </Transition>
  );
}
