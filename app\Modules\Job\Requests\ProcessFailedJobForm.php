<?php

namespace App\Modules\Job\Requests;

use App\Modules\Job\Services\FailedJobService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProcessFailedJobForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'option' => ['string'],
            'source' => ['required', 'string', Rule::in(['admin', 'client'])],
        ];
    }

    public function retry()
    {
        FailedJobService::retry($this->source, $this->has('option') ? $this->option : '');
    }

    public function forget()
    {
        FailedJobService::forget($this->source, $this->has('option') ? $this->option : '');
    }

    public function flush()
    {
        FailedJobService::flush($this->source);
    }
}
