import DropDownContainer from "@/Components/DropDownContainer";
import RadioButton from "@/Components/RadioButton";
import { useRef } from "react";

export default function ExpireDateFilter({
    fieldProp,
    fields,
    dateInput,
    range,
    setRange,
    fieldKey,
    handleDateFieldValue,
    placeholder = "YYYY-MM-DD",
    offFilter,
}) {
    const {active, items} = fieldProp;
    const containerRef = useRef();

    const dateCheck = (e, key) => {
        let checkerArr = e.target.value.split("-");

        if(checkerArr.length === 3 && checkerArr[0].length === 4 && checkerArr[1].length !== 0 && checkerArr[2].length !== 0){
            let inputDate = new Date(e.target.value);

            if(isNaN(inputDate)) return false;

            if(key === "maxExpireDate" && fields['minExpireDate'] && fields['minExpireDate']['value'].length > 0){
                let minDate = new Date(fields['minExpireDate']['value'][0]);
                if(!isNaN(minDate) && minDate.getTime() > inputDate.getTime()) return false;
            }

            if(key === "minExpireDate" && fields['maxExpireDate'] && fields['maxExpireDate']['value'].length > 0){
                let maxDate = new Date(fields['maxExpireDate']['value'][0]);
                if(!isNaN(maxDate) && maxDate.getTime() < inputDate.getTime()) return false;
            }

            return true;
        }

        return false;
    }

    const handleSubmit = () => {
        if (active) {
            offFilter();
        }
    };

    const handleExpirySelect = (valueField, value) => {
        Object.keys(range).map((rangeVar => {
            if(valueField[0] === rangeVar){
                setRange(prev => ({
                    ...prev,
                    [rangeVar] : !prev[rangeVar]
                }))
            }
        }))
        if(value)  handleDateFieldValue(valueField, "", false)
    }

    return (
        <div ref={containerRef}>
            <DropDownContainer show={active} className="left-full top-0 px-3">
                {items.map((item, index) => {
                const valueField = item.split(':');
                return (
                    <div
                        key={`${fieldKey}-${index}`}
                        className="flex space-x-2 py-1"
                    >
                        <div
                            key={`filter-radio-${valueField[1]}`}
                            className="flex w-full items-center space-x-2 cursor-pointer py-1 text-left leading-5 text-gray-700 min-w-[8rem] hover:bg-gray-100"
                            onClick={() => handleExpirySelect(valueField, range[valueField[0]])}
                        >
                            <RadioButton
                                key={`sort-expiry-${valueField[1]}`}
                                checked={range[valueField[0]]}
                                className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                handleChange={(e) => {}}
                            />
                            <span>{valueField[2]}</span>
                        </div>

                        <input
                            type="text"
                            name="search"
                            maxLength={10}
                            placeholder={placeholder}
                            className={`${range[valueField[0]] || "hidden"} border-gray-300 focus:border-gray-200 focus:ring-gray-200 rounded-md shadow-sm placeholder-gray-200`}
                            value={dateInput[valueField[0]]}
                            onChange={(e) => handleDateFieldValue(valueField, e.target.value, dateCheck(e, valueField[1]))}
                            onKeyDown={(e) => {
                                if (e.code === "Enter") {
                                    e.preventDefault();
                                    handleSubmit();
                                }
                            }}
                        />
                    </div>
                );
            })}
            </DropDownContainer>
        </div>
    );
}
