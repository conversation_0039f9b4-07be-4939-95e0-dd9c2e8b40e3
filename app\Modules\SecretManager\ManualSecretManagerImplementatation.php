<?php

namespace App\Modules\SecretManager;

class ManualSecretManagerImplementatation implements SecretManagerInterface
{
    private $secretManager;

    public function __construct()
    {

        if (strcmp(env('APP_ENV'), 'production') == 0) {
            $this->secretManager = new RemoteSecretManager();
        } else {
            $this->secretManager = new LocalSecretManager();
        }
    }

    public function getPayload(string $key): string
    {
        return $this->secretManager->getPayload($key);
    }

    public function getMeta(string $metadata): string
    {
        return $this->secretManager->getMeta($metadata);
    }
}
