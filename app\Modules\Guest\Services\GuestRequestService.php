<?php

namespace App\Modules\Guest\Services;

use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;

class GuestRequestService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function get($request)
    {
        $sortType = 'created_at';
        $sortOrder = 'desc';

        if ($request->has('orderby')) {
            $parts = explode(':', $request->orderby);
            switch ($parts[0]) {
                case 'email':
                    $sortType = 'email';
                    break;
                case 'created_at':
                    $sortType = 'created_at';
                    break;
                default:
                    $sortType = 'created_at';
            }
            $sortOrder = strcmp($parts[1], 'asc') === 0 ? 'asc' : 'desc';
        }

        $builder = DB::client()
            ->table('guest_requests')
            ->leftJoin('users', 'guest_requests.email', '=', 'users.email')
            ->select('guest_requests.*', 'users.email as user_email');

        $request->has('status') ? self::whenHasStatus($builder, $sortType, $request) : self::hasNoDeleted($builder);

        $builder = $builder->orderBy('guest_requests.'.$sortType, $sortOrder)
            ->orderBy('guest_requests.id', 'desc')  // cursorPagination requires unqique column
            ->paginate(self::$pageLimit);

        return CursorPaginate::cursor($builder, self::paramAdd($request));
    }

    public static function whenHasStatus(&$builder, &$sortType, $request)
    {
        if (strcmp($request->status, 'DELETED') == 0) {
            $builder->whereNotNull('guest_requests.deleted_at');
            $sortType = 'deleted_at';

            return;
        }

        $builder->where('status', $request->status);
        $builder->whereNull('guest_requests.deleted_at');
    }

    public static function hasNoDeleted(&$builder)
    {
        $builder->whereNull('guest_requests.deleted_at');
    }

    public static function paramAdd($request)
    {
        $query = [];

        if ($request->has('status')) {
            array_push($query, 'status='.$request->status);
        }
        if ($request->has('orderby')) {
            array_push($query, 'orderby='.$request->orderby);
        }

        return $query;
    }
}
