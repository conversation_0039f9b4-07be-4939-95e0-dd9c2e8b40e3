<?php

namespace App\Modules\Guest\Requests;

use App\Modules\Guest\Constants\RequestStatus;
use App\Modules\Guest\Services\GuestRequestService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'status' => ['string', Rule::in([...RequestStatus::all(), 'DELETED'])],
        ];
    }

    public function show()
    {
        return GuestRequestService::get($this);
    }
}
