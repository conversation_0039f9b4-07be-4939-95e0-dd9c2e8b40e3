<?php

namespace App\Modules\UserManagement\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\UserManagement\Services\UserManagementSettingsService; 

use Inertia\Inertia;

class UserManagementSettingsController extends Controller
{
    public function index()
    {
        return Inertia::render(
            'UserManagement/Settings/UserManagementSettingsIndex',
            [
            ]
        );
    }

    public function syncPermissions()
    {
        (new UserManagementSettingsService())->syncPermissions();
    }

    public function loadDefaultCategories()
    {
        (new UserManagementSettingsService())->loadDefaultCategories();
    }

    public function loadDefaultRoles()
    {
        (new UserManagementSettingsService())->loadDefaultRoles();
    }
}
