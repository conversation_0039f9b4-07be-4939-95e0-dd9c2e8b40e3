import React from 'react'
import Dropdown from "@/Components/Dropdown";
import { useState } from 'react';
import getRecentTime from '@/Util/getRecentTime';
import { useEffect } from 'react';

const NotificationDropdownItem = ({ item }) => {
    const [more, setMore] = useState(false);
    const [isRead, setIsRead] = useState(!!item.read_at);
    // Check sessionStorage on first render
    useEffect(() => {
        const readMap = JSON.parse(sessionStorage.getItem('readNotificationsMap')) || {};
        const alreadyRead = readMap[item.id] || !!item.read_at;
        setIsRead(alreadyRead);
    }, [item.id, item.read_at]);

    const getNotificationColor = (importance) => {
        switch (importance) {
            case 'critical':
                return {
                    bg: 'bg-red-700',
                    text: 'text-red-700'
                };
            case 'important':
                return {
                    bg: 'bg-yellow-700',
                    text: 'text-yellow-700'
                };
            case 'medium':
                return {
                    bg: 'bg-blue-700',
                    text: 'text-blue-700'
                };
            case 'low':
                return {
                    bg: 'bg-green-700',
                    text: 'text-green-700'
                };
            default:
                return {
                    bg: 'bg-gray-400',
                    text: 'text-gray-400'
                };
        }
    };

    const handleRead = () => {
        const readMap = JSON.parse(sessionStorage.getItem('readNotificationsMap')) || {};

        // Only process if this notification hasn't been marked read yet
        if (!readMap[item.id]) {
            readMap[item.id] = true;
            sessionStorage.setItem('readNotificationsMap', JSON.stringify(readMap));
            setIsRead(true);

            // Update unread counter
            let unreadCount = parseInt(sessionStorage.getItem('UnreadCounter'), 10) || 0;
            if (unreadCount > 0) {
                unreadCount -= 1;
                sessionStorage.setItem('UnreadCounter', unreadCount);
            }
        }
    };
    return (
        <Dropdown.Link
            key={item.id}
            href={route('notification.update.read', {id : item.id})}
            method="patch"
            as="button"
        >
            <div className="flex w-full justify-center gap-6" onClick={handleRead}>
                <span className='rounded-full h-16 w-16 bg-gray-200'></span>
                <span className={`w-3/4 ${isRead && 'text-gray-400'}`}>
                    <div className='flex items-center gap-2 pb-1'>
                    <div className={`w-1 h-5 rounded-full ${getNotificationColor(item.importance).bg.toLowerCase()}`}></div>
                        <span className='text-base font-bold'>{item.title}</span>
                        <span className={`text-[.4rem] ${isRead && 'opacity-40'}`}>&#9899;</span>
                        <span>{getRecentTime(item.created_at, true)}</span>
                    </div>
                    <div className={`${more || 'flex justify-start'}`}>
                        {item.message.length > 60 ? (  
                            <>
                                <div className={`${more || "truncate"} max-w-[22rem]`}>
                                    <span>{item.message}</span>
                                </div>
                                <span
                                    className="text-link flex justify-start"
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        setMore(!more);
                                    }}
                                >
                                    {more ? "show less" : "more"}
                                </span>
                            </>
                        ) : (
                            <div className='max-w-[22rem]'>{item.message}</div>
                        )}
                    </div>
                </span>
            </div>
        </Dropdown.Link>
    )
}
 
export default NotificationDropdownItem;