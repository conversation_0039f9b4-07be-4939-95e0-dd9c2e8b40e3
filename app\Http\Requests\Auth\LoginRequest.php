<?php

namespace App\Http\Requests\Auth;

use App\Modules\Admin\Constants\AdminStatusConstants;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        if (! Auth::attempt($this->only('email', 'password'), $this->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => trans('auth.failed'),
            ]);
        }

        $this->checkIfUserIsActive(Auth::user());

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Check if User is Active
     *
     * @param $user
     * 
     * @throws \Illuminate\Validation\ValidationException
     */
    public function checkIfUserIsActive($user): void
    {
        switch ($user->status)
        {
            case AdminStatusConstants::STATUS_ACTIVE: 
                //...
                break;

            case AdminStatusConstants::STATUS_INACTIVE:
                //...
                break;

            case AdminStatusConstants::STATUS_PENDING:
                Auth::logout(); 
                throw ValidationException::withMessages(
                    [
                        'email' => 'Account needs to be first setup. Check your email.',
                    ]
                );
                break;

            case AdminStatusConstants::STATUS_DISABLED:
                Auth::logout();
                throw ValidationException::withMessages(
                    [
                        'email' => 'Account is disabled. Please contact administrators.',
                    ]
                );
                break;
        }
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('email')).'|'.$this->ip());
    }
}
