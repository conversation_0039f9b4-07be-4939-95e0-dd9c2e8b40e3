import { useState } from "react";

import NavLink from "@/Components/NavLink";
import {
    MdExpandMore,
    MdExpandLess,
    MdOutlineCorporateFare,
} from "react-icons/md";

import { FaBalanceScaleLeft } from "react-icons/fa";

import { BiTransfer } from "react-icons/bi";

import { AiOutlineAudit } from "react-icons/ai";

export default function MarketPlaceNav({ postRouteName }) {

    const currentRoute = route().current() || postRouteName;

    const routes = {
        domains: route().current("domains") || currentRoute.includes('domains'),
        audits: route().current("audits") || currentRoute.includes('audits'),
        market_manuals: route().current("market_manuals") || currentRoute.includes('market_manuals'),
        offers: route().current("offers") || currentRoute.includes('offers'),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };

    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Marketplace</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("domains")}
                active={routes.domains}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdOutlineCorporateFare className="text-2xl " />
                    <span className=" text-inherit">Domains</span>
                </span>
                
            </NavLink>
            <NavLink
                href={route("audits")}
                active={routes.audits}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <AiOutlineAudit className="text-2xl " />
                    <span className=" text-inherit">Audits</span>
                </span>
                
            </NavLink>
            <NavLink
                href={route("market_manuals")}
                active={routes.market_manuals}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <BiTransfer className="text-2xl " />
                    <span className=" text-inherit">Manual Transfer</span>
                </span>
                
            </NavLink>
            <NavLink
                href={route("offers")}
                active={routes.offers}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <FaBalanceScaleLeft className="text-2xl " />
                    <span className=" text-inherit">Market Offers</span>
                </span>
                
            </NavLink>
        </>
    );
}
