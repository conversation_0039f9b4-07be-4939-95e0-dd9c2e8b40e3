import React from "react";
import AdminLayout from "@/Layouts/AdminLayout";
import { IoMdArrowBack } from "react-icons/io";
import { router, usePage } from '@inertiajs/react';
import ActivitiesNav from "@/Components/Client/ClientLogs/ActivitiesNav";

export default function Index() {
    const { email, user_id, activeTab } = usePage().props;

    const onBackClick = () => {
        router.visit(route("client.logs"), {
            preserveState: true,
            preserveScroll: true,
        });
    };

    return (
        <AdminLayout>
            <div className="flex flex-col">
                <div className="flex items-center space-x-2 mb-4 pl-6">
                    <button onClick={onBackClick} className="text-2xl pt-1">
                        <IoMdArrowBack />
                    </button>
                    <h2 className="text-2xl font-bold">
                        Client Activity Logs for : {email}
                    </h2>
                </div>
                <div className="flex flex-col md:flex-row">
                    <ActivitiesNav 
                        activeTab={activeTab}
                        user_id={user_id}
                    />
                    <div className="w-full md:w-5/6">
                        {/* Content will be rendered by the specific log components */}
                    </div>
                </div>
            </div>
        </AdminLayout>  
    );
}
