//* PACKAGES
import React, { useState, useEffect, useRef } from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import { MdRestartAlt } from "react-icons/md";

//* COMPONENTS
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Item({ item, userId, email, updateAdjustment }) {
    //! PACKAGE
    //...
    //! HOOKS
    //! VARIABLES
    //...
    //! STATES
    //! USE EFFECTS
    //...
    //! FUNCTIONS
    const onHandleChange = (event) => {
        updateAdjustment(item.transactionId, getEventValue(event));
    };

    const reset = () => {
        updateAdjustment(item.transactionId, 0);
    };

    const handleUpdate = () => {
        router.patch(route("client.setting.transaction.threshold-update"), {
            transactionId: item.transactionId,
            userId: userId,
            adjustment: item.adjustment,
            email: email
        }, {
            onError: (errors) => toast.error(errors.message, { autoClose: 8000 }),
            onSuccess: () => {
                toast.success("Updated " + item.nameType.toLowerCase() + " user limit to " + item.userLimit + item.customLimit);
                setItemValue(value);
            },
        });
    };

    const formatTransactionName = (str) => {
        return str.toLowerCase().replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
    }

    return (
        <tr className="hover:bg-gray-50">
            <td className="w-1/5">
                <label className="flex items-center pl-2 space-x-2">
                    <span className="cursor-pointer">
                        {formatTransactionName(item.name)}
                    </span>
                </label>
            </td>
            <td className="w-1/5">
                <span className=" lowercase">{item.userLimit}</span>
            </td>
            <td className="w-1/5">
                <span>{item.customLimit}</span>
            </td>
            <td className="w-1/5">
                <TextInput
                    type="number"
                    name="name"
                    placeholder="0"
                    value={item.adjustment}
                    handleChange={onHandleChange}
                    className="w-1/2"
                />
            </td>
            <td className="w-1/5">
                <span>{item.userLimit + item.customLimit}</span>
            </td>
            <td className="w-1/5">
                <span className="flex justify-center space-x-1">
                    <button
                        className={`border border-gray-300 rounded-md px-4 py-2 ${item.adjustment != 0 ? "block px-4 py-1 bg-primary border border-transparent rounded-md text-sm text-white hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150" : "opacity-25"}`}
                        disabled={item.adjustment == 0}
                        onClick={handleUpdate}
                    >
                        Update
                    </button>
                    <button
                        className={`flex border items-center border-gray-300 rounded-md px-4 py-2 text-sm text-gray-700 ${item.adjustment != 0 ? "focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150" : "opacity-25"}`}
                        disabled={item.adjustment == 0}
                        onClick={reset}
                    >
                        Reset
                        <MdRestartAlt className='text-2xl pl-1' />
                    </button>
                </span>
            </td>

        </tr>
    );
}
