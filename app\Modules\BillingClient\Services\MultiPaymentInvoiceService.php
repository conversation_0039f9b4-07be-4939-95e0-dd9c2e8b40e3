<?php

namespace App\Modules\BillingClient\Services;

use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;

class MultiPaymentInvoiceService
{
    use CursorPaginate, UserLoggerTrait;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $multiPaymentInvoiceService = new self;

        return $multiPaymentInvoiceService;
    }

    public function getDataBySummaryId(int $summaryId, int $userId)
    {
        $domainInvoiceData = [];
        $marketInvoiceData = [];
        $allData = [];
        $allData['data'] = [];

        $summaryData = BillingClientService::instance()->getPaymentSummaryById($summaryId);

        // get registered domains
        if ($summaryData->payment_invoice_id) {
            $domainInvoiceData = PaymentInvoiceService::instance()->getPaymentInvoice($summaryData->payment_invoice_id, $userId);
        }
        // get marketplace domains
        if ($summaryData->payment_market_place_invoice_id) {
            $marketInvoiceData = MarketInvoiceService::instance()->getMarketPlaceInvoice($summaryData->payment_market_place_invoice_id, $userId);
        }

        if (! empty($domainInvoiceData)) {
            $allData['data'] = $domainInvoiceData['data'];
        }

        if (! empty($marketInvoiceData)) {
            $allData['data'] = array_merge($allData['data'], $marketInvoiceData);
        }

        // dd($domainInvoiceData, $marketInvoiceData);

        $allData['node_type'] = $summaryData->name;
        $allData['paid_amount'] = $summaryData->paid_amount;
        $allData['paymentIntent'] = $this->getStripePaymentIntent($allData['data'][0]);
        $allData['summary_type'] = PaymentSummaryType::MULTI_CHECKOUT_INVOICE;

        return $allData;
    }

    private function getStripePaymentIntent(object $invoice)
    {
        if (! $invoice->stripe_id) {
            return null;
        }

        $stripeTransaction = DB::client()->table('stripe_transactions')
            ->where('id', $invoice->stripe_id)
            ->get()->first();

        return $stripeTransaction->payment_intent ?? null;
    }
}
