<?php

namespace App\Modules\ClientPaymentService\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\ClientPaymentService\Constants\PaymentServiceType;
use App\Modules\ClientPaymentService\Contracts\PaymentServicePickerInterface;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;

class PaymentServiceHelper
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $paymentServiceHelper = new self;

        return $paymentServiceHelper;
    }

    public function getPaymentServiceById(int $id, int $userId)
    {
        $paymentService = DB::client()->table('payment_services')->where('id', $id)->where('user_id', $userId)->first();

        if (! $paymentService) {
            throw new FailedRequestException(404, 'Payment service not found', 'Error');
        }

        $type = $this->getPaymentServiceType($paymentService);

        if (! $type) {
            throw new FailedRequestException(404, 'Payment service not found', 'Error');
        }

        $helper = app(PaymentServicePickerInterface::class)->getType($type);

        return $helper->getPaymentService($paymentService);
    }

    public function getPaymentServiceType(object $paymentService)
    {
        if (
            $paymentService->account_credit_id &&
            ($paymentService->bank_transfer_id || $paymentService->system_credit_id || $paymentService->stripe_id)
        ) {
            return PaymentServiceType::ACCOUNT_DEBIT;
        } elseif ($paymentService->account_credit_id) {
            return PaymentServiceType::ACCOUNT_CREDIT;
        } elseif ($paymentService->bank_transfer_id && ! $paymentService->account_credit_id) {
            return PaymentServiceType::BANK_TRANSFER;
        } elseif ($paymentService->system_credit_id && ! $paymentService->account_credit_id) {
            return PaymentServiceType::SYSTEM_CREDIT;
        } elseif ($paymentService->stripe_id && ! $paymentService->account_credit_id) {
            return PaymentServiceType::STRIPE;
        } else {
            return null;
        }
    }
}
