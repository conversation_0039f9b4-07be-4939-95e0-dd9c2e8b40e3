import { useRef, useState, useEffect, useMemo, useCallback } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import Checkbox from "@/Components/Checkbox";
import RadioButton from "@/Components/RadioButton";
import { debounce } from "lodash";
import {
    MdMoreVert,
    MdOutlineLock,
    MdOutlinePause,
    MdOutlineCancel,
} from "react-icons/md";
import _FilterSetting from "@/Constant/_FilterSetting";

let FILTER_STATE = null;

export default function Filter({ query, updateQueryParameter }) {
    const ref = useRef();

    const initQueryFilter = (filterPropertyTemplate, initQuery) => {
        let { search, status, tld, sortby, sortDirection, seeker } = initQuery;
        let fieldSelected = [];

        //if has existing search keyword, stop other filter
        if (search != undefined) {
            filterPropertyTemplate.search.value = search;
            filterPropertyTemplate.search.active = true;
            return filterPropertyTemplate;
        }
        // Start of updating field status

        if (status != undefined) fieldSelected = status;
        if (tld != undefined) fieldSelected = fieldSelected.concat(tld);
        if (fieldSelected.length > 0) {
            filterPropertyTemplate.field.active = true;
            filterPropertyTemplate.field.selected = fieldSelected;
        }
        //End of updating field status

        //Start of updating sort

        if (sortby != undefined && sortDirection != undefined) {
            let { sort } = filterPropertyTemplate;

            sort.value = sortby + " " + sortDirection;

            if (seeker != undefined) sort.seeker = seeker;

            sort.active = true;
            filterPropertyTemplate.sort = sort;
        }

        return filterPropertyTemplate;
    };

    const [filter, setFilter] = useState(
        initQueryFilter(_FilterSetting, query)
    );

    useOutsideClick(ref, () => {
        closeSubFilterContainer();
    });

    const updateRootState = () => {
        let queryParameter = {};
        let { search, field, sort } = FILTER_STATE;

        if (search.active) queryParameter.search = search.value;
        if (field.active) queryParameter.filter = field.selected;
        if (sort.active) {
            const sortValue = sort.value.split(" ");

            queryParameter.sortby = sortValue[0];
            queryParameter.orderby = sortValue[1];
            queryParameter.limit = sort.limit;
        }

        updateQueryParameter(queryParameter);
    };

    const httpRequestDomain = useCallback(debounce(updateRootState, 2000), []);

    const closeSubFilterContainer = () => {
        let cloneFilter = { ...filter };
        Object.keys(cloneFilter).forEach((key) => {
            cloneFilter[key].open = false;
        });
        cloneFilter.searchBar.open = true;

        setFilter({ ...cloneFilter });
    };

    const handleFilterChange = (userInput) => {
        let { searchBar, container } = filter;
        searchBar.value = userInput;
        container.open = true;

        setFilter({
            ...filter,
            searchBar: { ...searchBar },
            container: { ...container },
        });
    };

    const handleSearchFilter = () => {
        if (!filter.search.open) return;

        const acceptSearchInput = () => {
            let { search, searchBar } = filter;
            search.active = true;
            search.open = false;
            searchBar.open = true;
            searchBar.value = "";

            FILTER_STATE = {
                ...filter,
                search: { ...search },
                searchBar: { ...searchBar },
            };
            setFilter({
                ...filter,
                search: { ...search },
                searchBar: { ...searchBar },
            });

            httpRequestDomain();
        };

        const handleSearchFilterChange = (value) => {
            let { search } = filter;
            search.value = value;
            search.open = true;
            setFilter({ ...filter, search: { ...search } });
        };

        return (
            <div className="flex items-center">
                <label className="shadow-md rounded-l-full  border-gray-200 text-sm px-3 py-1 bg-primary text-white">
                    <span className="text-inherit">Name :</span>
                </label>
                <div className="text-inherit font-medium pl-1">
                    <input
                        autoFocus
                        placeholder="search domain name"
                        type="text"
                        name="domain"
                        value={filter.search.value}
                        className={` border-none border-transparent focus:border-transparent focus:ring-0  placeholder-gray-200 `}
                        onChange={(e) =>
                            handleSearchFilterChange(e.target.value)
                        }
                        onKeyDown={(e) => {
                            e.code === "Enter" && acceptSearchInput();
                        }}
                    />
                </div>
            </div>
        );
    };

    const handleFieldFilter = () => {
        if (!filter.field.open) return;

        const { status, extension } = filter.field.option;

        const toggleFieldFilter = (name, isSelected) => {
            let { field } = filter;
            let { selected } = field;
            if (isSelected) selected.splice(selected.indexOf(name), 1);
            else selected.push(name);

            field.selected = selected;
            field.active = selected.length > 0;

            FILTER_STATE = { ...filter, field: { ...field } };
            setFilter({ ...filter, field: { ...field } });

            httpRequestDomain();
        };

        const renderOption = (field, prefix = "") => {
            return field.map((name) => {
                const isSelected = filter.field.selected.includes(name);

                return (
                    <div
                        key={"filter-field-" + name}
                        className="flex items-center space-x-2 cursor-pointer p-2"
                        onClick={(e) => toggleFieldFilter(name, isSelected)}
                    >
                        <Checkbox
                            name={name}
                            value={name}
                            checked={isSelected}
                            handleChange={(e) =>
                                toggleFieldFilter(name, isSelected)
                            }
                        />
                        <span>
                            {prefix + " : "} {name}
                        </span>
                    </div>
                );
            });
        };

        return (
            <>
                <label className="shadow-md rounded-l-full  border-gray-200 text-sm px-3 py-1 bg-primary text-white">
                    <span className="text-inherit">Field :</span>
                </label>
                <DropDownContainer
                    show={filter.field.open}
                    className=" left-full"
                >
                    {renderOption(status, "Status")}
                    {renderOption(extension, "TLD")}
                </DropDownContainer>
            </>
        );
    };

    const handleSortFilter = () => {
        if (!filter.sort.open) return;

        const { value } = filter.sort;

        const handleSelectedSortOption = (newValue) => {
            let { sort } = filter;
            sort.value = newValue;
            FILTER_STATE = { ...filter, sort: { ...sort } };
            setFilter({ ...filter, sort: { ...sort } });

            httpRequestDomain();
        };

        return (
            <>
                <label className="shadow-md rounded-l-full  border-gray-200 text-sm px-3 py-1 bg-primary text-white">
                    <span className="text-inherit">Sort by :</span>
                </label>

                <DropDownContainer
                    show={filter.sort.open}
                    className=" left-full p-2"
                >
                    {filter.sort.option.map((option) => {
                        return (
                            <div
                                key={"filter-sort-option-" + option}
                                className="flex items-center space-x-2 cursor-pointer"
                                onClick={(e) =>
                                    handleSelectedSortOption(option)
                                }
                            >
                                <RadioButton
                                    key={"sort-option-" + option}
                                    checked={value == option}
                                    name={option}
                                    value={option}
                                    className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                    handleChange={(e) =>
                                        handleSelectedSortOption(option)
                                    }
                                />
                                <span>{option}</span>
                            </div>
                        );
                    })}
                </DropDownContainer>
            </>
        );
    };

    const mainFilterContainer = () => {
        const toggleFilterOption = (selected) => {
            let clonedFilter = { ...filter };
            Object.keys(clonedFilter).forEach((key) => {
                clonedFilter[key].open = key == selected;
            });

            setFilter({ ...filter, clonedFilter });
        };

        return (
            <DropDownContainer show={filter.container.open} className="left-0">
                <span
                    className="hover:bg-gray-100 px-5 py-1"
                    onClick={() => toggleFilterOption("search")}
                >
                    Search
                </span>
                <span
                    className="hover:bg-gray-100 px-5 py-1"
                    onClick={() => toggleFilterOption("field")}
                >
                    Filter field
                </span>
                <span
                    className="hover:bg-gray-100 px-5 py-1"
                    onClick={() => toggleFilterOption("sort")}
                >
                    Sort
                </span>
            </DropDownContainer>
        );
    };

    const renderActiveFilter = (type, key, value) => {
        const removeValue = (type) => {
            const clonedFilter = { ...filter };
            switch (type) {
                case "seach":
                    clonedFilter.search.value = "";
                    break;
                case "field":
                    clonedFilter.field.selected = [];
                    break;
                case "sort":
                    clonedFilter.sort.value = "";

                    break;
                default:
                    break;
            }
            clonedFilter[type].active = false;
            FILTER_STATE = { ...clonedFilter };
            setFilter(clonedFilter);

            httpRequestDomain();
        };

        return (
            <div className="flex items-center shadow-md rounded-full border-gray-200 text-sm px-3 py-1 bg-primary text-white space-x-2">
                <label className="text-white">
                    <span className="text-inherit">{key} : </span>
                    <span className="text-inherit font-medium pl-1">
                        {value}
                    </span>
                </label>
                <button onClick={(e) => removeValue(type)}>
                    <MdOutlineCancel className="text-white cursor-pointer text-lg" />
                </button>
            </div>
        );
    };

    return (
        <div className="flex items-center space-x-1 ">
            {filter.search.active &&
                renderActiveFilter("search", "Name", filter.search.value)}

            {filter.field.active &&
                renderActiveFilter(
                    "field",
                    "Only",
                    filter.field.selected.join(" | ")
                )}

            {filter.sort.active &&
                renderActiveFilter("sort", "Order by", filter.sort.value)}

            <div ref={ref} className="flex flex-col items-start relative  ">
                <input
                    placeholder="Select filter"
                    type="text"
                    name="filter"
                    value={filter.searchBar.value}
                    className={` border-none border-transparent focus:border-transparent focus:ring-0  placeholder-gray-200 ${
                        !filter.searchBar.open && " hidden "
                    } `}
                    onChange={(e) => handleFilterChange(e.target.value)}
                    onFocus={(e) => handleFilterChange(filter.searchBar.value)}
                />

                <div>{handleSortFilter()}</div>
                <div>{handleFieldFilter()}</div>
                <div>{handleSearchFilter()}</div>
                <div>{mainFilterContainer()}</div>
            </div>
        </div>
    );
}
