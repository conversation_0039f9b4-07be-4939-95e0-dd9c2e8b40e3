<?php

namespace App\Modules\Setting\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class CommissionSettingService
{
    private static $pageLimit = 20;

    public static function get($request)
    {

        $builder = DB::client()->table('commission_settings')
            ->join('fees', 'fees.id', '=', 'commission_settings.fee_id')
            ->when($request->has('status'), function (Builder $query) use ($request) {
                $query->where('commission_settings.fee_id', $request->status);
            })
            ->select('commission_settings.*', 'fees.type as fee_type')
            ->orderby('commission_settings.id', 'desc')
            ->paginate(self::$pageLimit);

        return CursorPaginate::cursor($builder, $request->has('status') ? ['status='.$request->status] : []);
    }

    public static function updateStatus(array $id, bool $isActive)
    {
        $hasNoActive = [];
        if ($isActive) {
            DB::client()->table('commission_settings')->whereIn('id', $id)->update(['is_active' => true]);

            return;
        }

        $currentComms = DB::client()->table('commission_settings')->whereIn('id', $id)->where('is_active', true)->select('id', 'fee_id')->get();

        if ($currentComms == null) {
            throw ValidationException::withMessages([
                'message' => 'selected id does not exists',
            ]);
        }

        $catCurrentComm = [];
        $currentComms->each(function ($item, $key) use (&$catCurrentComm) {
            $catCurrentComm[$item->fee_id][] = $item->id;
        });

        foreach ($catCurrentComm as $key => $value) {
            $validId = [];
            $hasActive = DB::client()->table('commission_settings')->whereNotIn('id', $value)->where('is_active', true)->where('fee_id', $key)->exists();

            if ($hasActive) {
                $validId = $value;
            } elseif (count($value) > 1) {
                $removed = array_pop($value);
                $validId = $value;
                $hasNoActive[] = $removed;
            } else {
                $hasNoActive = array_merge($hasNoActive, $value);
            }

            if (count($validId) > 0) {
                DB::client()->table('commission_settings')->whereIn('id', $validId)->update(['is_active' => false]);
            }
        }

        if (count($hasNoActive) > 0) {
            throw ValidationException::withMessages([
                'message' => ' cannot change ('.count($hasNoActive).') commission status. must have active value',
            ]);
        }

        app(AuthLogger::class)->info('update Commission id '.implode(',', $id).' to '.$isActive);
    }

    public static function store($request)
    {
        $newCommission = [
            'fee_id' => $request->id,
            'minimum_total_node' => $request->minimum_total_node,
            'fix_rate' => $request->fix_rate,
            'percent_rate' => $request->percent_rate,
            'is_active' => true,
        ];

        DB::client()->table('commission_settings')->insert(
            $newCommission
        );

        app(AuthLogger::class)->info('create new commission setting  with value'.implode(',', $newCommission));
    }

    public static function isUnique($feeId, $minimum_domain)
    {
        $exist = DB::client()->table('commission_settings')->where([['fee_id', $feeId], ['minimum_total_node', $minimum_domain]])->first();
        if ($exist == null) {
            return;
        }

        throw ValidationException::withMessages([
            'message' => 'minimum domain in the same type exists',
        ]);
    }
}
