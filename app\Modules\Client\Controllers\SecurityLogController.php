<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Services\SecurityLogService;
use App\Modules\Client\Requests\ShowListRequest;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\JsonResponse;

class SecurityLogController extends Controller
{
    protected $securityLogService;

    public function __construct(SecurityLogService $securityLogService)
    {
        $this->securityLogService = $securityLogService;
    }

    public function index(ShowListRequest $request, ?int $userId = null): Response
    {
        return Inertia::render(
            'Client/ClientLogs/Activities/AccountSecurityLogs',
            $this->securityLogService->getSecurityLogsData($userId, $request)
        );
    }


    public function getPayload(int $id): JsonResponse
    {
        $payload = $this->securityLogService->getLogPayload($id);
        
        return response()->json([
            'payload' => $payload
        ]);
    }
} 