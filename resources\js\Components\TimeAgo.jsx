import { useState, useEffect } from "react";

const TimeAgo = ({ timestamp, children }) => {
    const [time, setTime] = useState("");

    useEffect(() => {
        const updateTime = () => {
            const now = Date.now();
            const diffInSeconds = Math.floor(now / 1000 - timestamp);

            const minutes = Math.floor(diffInSeconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (minutes < 1) setTime("just now");
            else if (minutes < 60) setTime(`${minutes} minute${minutes > 1 ? "s" : ""} ago`);
            else if (hours < 24) setTime(`${hours} hour${hours > 1 ? "s" : ""} ago`);
            else setTime(`${days} day${days > 1 ? "s" : ""} ago`);
        };

        updateTime();
        const interval = setInterval(updateTime, 60000); // Update every 60 seconds

        return () => clearInterval(interval);
    }, [timestamp]);

    return <span>{time}{children}</span>;
};

export default TimeAgo;