<?php

namespace App\Modules\Setting\Services;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class UserTransaction
{
    public static function instance(): self
    {
        $userTransaction = new self;

        return $userTransaction;
    }

    public function getQuery()
    {
        return DB::client()->table('user_transactions')
            ->join('transactions', 'transactions.id', '=', 'user_transactions.transaction_id')
            ->orderby('extensions.id', 'asc')
            ->orderby('fees.type', 'asc');
    }

    public function selectFees(Builder &$builder)
    {
        $builder->select(
            'extension_fees.*',
            'fees.type',
            'fees.value as fee_value',
            'extensions.name as extension_name',
            'tlds.id as tld_id'
        );
    }

    public function getDefaultQuery()
    {
        $builder = $this->getQuery();
        $this->selectFees($builder);

        return $builder->where('is_default', true)->get()->all();
    }

    public function getUserCustomFeesByUserIds(array $userIds)
    {
        $builder = $this->getQuery();
        $this->selectFees($builder);

        return $builder->whereIn('user_id', $userIds)->get()->all();
    }

    public function getUserCustomFeeById(int $id)
    {
        $builder = $this->getQuery();
        $this->selectFees($builder);

        return $builder->where('user_id', $id)->get()->all();
    }
}
