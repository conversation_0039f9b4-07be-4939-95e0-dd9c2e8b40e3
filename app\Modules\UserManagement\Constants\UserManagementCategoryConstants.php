<?php

namespace App\Modules\UserManagement\Constants;

final class UserManagementCategoryConstants
{
        /**
         * @var array
         */
        public const PREDEFINED_DEFAULTS =
        [
            [
                'name'            => 'Audit',
                'searchRouteName' => 'audit'
            ],
            [
                'name'            => 'EPP Registry', 
                'searchRouteName' => 'epp'
            ],
            [
                'name'            => 'Offers',
                'searchRouteName' => 'offers'
            ],
            // [
            //     'name'            => 'User Management',
            //     'searchRouteName' => 'user-management'
            // ],
            [
                'name'            => 'Billing', 
                'searchRouteName' => 'billing'
            ],
            [
                'name'            => 'Client Management',
                'searchRouteName' => 'client'
            ],
            [
                'name'            => 'Domain Management',
                'searchRouteName' => 'domain'
            ],
            [
                'name'            => 'Job Queues',
                'searchRouteName' => 'job'
            ],
            [
                'name'            => 'Marketplace',
                'searchRouteName' => 'market'
            ],
            [
                'name'            => 'Notification Management',
                'searchRouteName' => 'notification'
            ],
            [
                'name'            => 'Settings',
                'searchRouteName' => 'setting'
            ],
            [
                'name'            => 'System Credits',
                'searchRouteName' => 'system'
            ],
            [
                'name'            => 'System Emails',
                'searchRouteName' => 'email'
            ],
            [
                'name'           => 'System Activity Monitor',
                'searchRouteName' => 'activity'
            ],
        ];
}
