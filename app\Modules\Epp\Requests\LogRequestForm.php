<?php

namespace App\Modules\Epp\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Epp\Services\LogService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LogRequestForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'registry' => [Rule::in(['pir', 'verisign'])],
            'page' => ['integer'],
        ];
    }

    public function log()
    {
        if (! $this->has('registry')) {
            return '';
        }

        return LogService::get($this->registry, $this->has('page') ? $this->page : 1);
    }

    public function refresh()
    {
        if (! $this->has('registry')) {
            throw new FailedRequestException(400, 'Registry not specified', 'Bad request');
        }

        LogService::refresh($this->registry);
    }
}
