//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import LoaderSpinner from "@/Components/LoaderSpinner";
import PrimaryLink from "@/Components/PrimaryLink";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import EmptyResult from "@/Components/Util/EmptyResult";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index(
        {
        registries,
        balance,
        items,
        onFirstPage,
        onLastPage,
        nextPageUrl,
        previousPageUrl,
        itemCount = 0,
        total = 0,
        itemName = "item",
    }
)
{

    //! PACKAGE
    const { registry_id } = route().params;
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [hasSpinner, setSpinner] = useState(false);
    const [hasActive, setActivetab] = useState(null);

    //! USE EFFECTS
    //...

    //! FUNCTIONS        
    const getRegistryText = (str) => {
        return (str == 'verisign') ? 'Verisign' :
            (str == 'pir') ? 'PIR' : '';
    }

    const toDollar = (number) => {
        const floatValue = parseFloat(number);
        if (isNaN(floatValue)) return 0;

        // return floatValue.toFixed(2);
        return floatValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    };

    const removeactivetab = () => {

        const parent = document.getElementById("parentElement");
        const children = parent.querySelectorAll(".child.bg-gray-100");
        children.forEach(child => {
            child.classList.remove("bg-gray-100");
        });
    };

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col justify-between">
                <div id="parentElement" className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    {registries.map((registry, i) => {
                        return (
                            <Link
                                key={"r-" + registry.id}
                                as="button"
                                href={route("epp.account", {
                                    registry_id: registry.id,
                                })}
                                onClick={() => {
                                    setSpinner(true);
                                    setActivetab(registry.name);
                                    removeactivetab();
                                }}
                            >
                                <div>
                                    <span
                                        className={`child px-5 py-1 rounded-sm ${
                                            registry.id == registry_id || hasActive == registry.name
                                                ? "bg-gray-100 text-gray-700"
                                                : "hover:bg-gray-100 hover:text-link"
                                        }`}
                                    >{getRegistryText(registry.name)}</span>
                                </div>
                            </Link>
                        );
                    })}
                </div>
                {registry_id == undefined ? (
                    <EmptyResult message="Please select an account to show" />
                ) : (
                    <>
                        {/* <div>
                            <label className="text-gray-600 text-3xl font-semibold py-5 uppercase">
                                {
                                    registries.find(
                                        (registry) => registry.id == registry_id
                                    ).name
                                }
                            </label>
                        </div> */}
                        <div className="flex flex-col space-y-10">
                            <div className="flex  bg-gray-50 p-10 rounded-sm space-y-1 justify-between bg-[url('/assets/images/wave.svg')] bg-cover bg-center ">
                                <div className="flex flex-col">
                                    <span className="text-gray-600 text-lg font-semibold uppercase">
                                        {
                                            registries.find(
                                                (registry) =>
                                                    registry.id == registry_id
                                            ).name
                                        }
                                    </span>

                                    <label className="text-lg text-gray-400">
                                        Balance
                                    </label>

                                    <span className=" text-4xl  text-gray-700">
                                        <span className=" pr-2 text-gray-400">
                                            $
                                        </span>
                                        {toDollar(balance.balance)}
                                    </span>
                                    <span className="text-gray-400 text-sm">
                                        {balance.created_at}
                                    </span>
                                </div>
                                <div className=" inline-flex items-center pr-2">
                                    {
                                        hasPermission("epp.account.adjust-balance") && hasPermission("epp.account.adjust-balance.store")
                                            ?
                                                <PrimaryLink
                                                    href={route(
                                                        "epp.account.adjust-balance",
                                                        {
                                                            registry_id:
                                                                balance.registry_id,
                                                            registry: registries.find(
                                                                (registry) =>
                                                                    registry.id == registry_id
                                                            ).name,
                                                        }
                                                    )}
                                                >
                                                    Adjust Account Balance
                                                </PrimaryLink>
                                            :
                                                null
                                    }
                                
                                </div>
                            </div>
                            <div className="flex flex-col space-y-4 ">
                                <span className="text-gray-700 text-md font-semibold">
                                    Recent activities
                                </span>
                                <div className="w-full">
                                    <table className="w-full text-left border-separate border-spacing-y-3.5  text-gray-600">
                                    {hasSpinner ?
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                                        :
                                        ""
                                    }
                                        <tbody className="">
                                            <tr>
                                                <th>Type</th>
                                                <th>Balance</th>
                                                <th>Change</th>
                                                <th>Purpose</th>
                                            </tr>
                                            {items.map((transaction, e) => {
                                                // console.log(transaction.credit);

                                                return (
                                                    <tr
                                                        key={
                                                            "tr-" +
                                                            transaction.id
                                                        }
                                                        className=" hover:bg-gray-50"
                                                    >
                                                        <td className="border-b border-gray-200 pb-2">
                                                            <div className=" flex flex-col">
                                                                <span>
                                                                    {
                                                                        transaction.type
                                                                    }
                                                                </span>
                                                                <span className=" text-sm text-gray-400">
                                                                    {
                                                                        transaction.created_at
                                                                    }
                                                                </span>
                                                            </div>
                                                        </td>
                                                        <td className="border-b border-gray-200">
                                                            <span>
                                                                {
                                                                    toDollar(transaction.balance)
                                                                }
                                                            </span>
                                                        </td>

                                                        <td className="border-b border-gray-200">
                                                            {parseFloat(
                                                                transaction.credit
                                                            ) > 0 && (
                                                                    <span>
                                                                        -
                                                                        {
                                                                            toDollar(transaction.credit)
                                                                        }
                                                                    </span>
                                                                )}
                                                            {parseFloat(
                                                                transaction.debit
                                                            ) > 0 && (
                                                                    <span>
                                                                        +
                                                                        {
                                                                            toDollar(transaction.debit)
                                                                        }
                                                                    </span>
                                                                )}
                                                        </td>
                                                        <td className="border-b border-gray-200">
                                                            <span>
                                                                {
                                                                    transaction.description
                                                                }
                                                            </span>
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </>
                )}
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                />
            </div>
        </AdminLayout>
    );
}
