import { useState } from "react";

import NavLink from "@/Components/NavLink";
import {
    MdExpandLess,
    MdExpandMore,
    MdSupervisedUserCircle,
    MdOutlineCorporateFare,
} from "react-icons/md";
import { FaMoneyCheckAlt } from "react-icons/fa";

export default function BillingNav({ postRouteName }) {
    const currentRoute = route().current() || postRouteName;

    const routes = {
        billing_client: route().current("billing.client") || currentRoute.includes('billing.client'),
        billing_registrar: route().current("billing.registrar") || currentRoute.includes('billing.registrar'),
        billing_wire_transfer: route().current("billing.wire.transfer") || currentRoute.includes('billing.wire.transfer'),
    };

    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Billing</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>
            <NavLink
                href={route("billing.wire.transfer")}
                active={routes.billing_wire_transfer}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <FaMoneyCheckAlt className="text-2xl " />
                    <span className=" text-inherit">Wire Transfer</span>
                </span>
                
            </NavLink>
            <NavLink
                href={route("billing.client")}
                active={routes.billing_client}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdSupervisedUserCircle className="text-2xl " />
                    <span className=" text-inherit">Client Payment Summary</span>
                </span>
                
            </NavLink>
            {/* TODO */}
            {/* <NavLink
                // href={route("billing.registrar")}
                active={routes.billing_registrar}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdOutlineCorporateFare className="text-2xl " />
                    <span className=" text-inherit">Registrar Payment Summary</span>
                </span>
                
            </NavLink> */}
        </>
    );
}
