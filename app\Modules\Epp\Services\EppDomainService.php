<?php

namespace App\Modules\Epp\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Helper\DomainParser;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Exception;

class EppDomainService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $eppDomainService = new self;

        return $eppDomainService;
    }

    public function updateEppDomain(array $payload): array
    {
        $registry = DomainParser::getRegistryName($payload['name']);

        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::domain($registry)->put(Config::get('domain.base'), $payload);
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function databaseSyncExpiry(string $domainName, string $email = 'Unauthorized'): array
    {
        $registry = DomainParser::getRegistryName($domainName);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $payload = ['name' => $domainName];
            $request = Http::domain($registry)->post(Config::get('domain.database_sync_expiry'), $payload);
        } catch (RequestException $e) {
            $error = $e->response->json();

            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $email));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }

        return $request->json();
    }

    public function callEppDomainInfo(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::domain($registry)->post(
                Config::get('domain.info'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callDatastoreDomainInfo(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::domain($registry)->post(
                Config::get('domain.database_info'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callEppDomainDelete(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::domain($registry)->delete(
                Config::get('domain.epp_delete'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }

    public function callDatastoreDomainDelete(string $domain): array
    {
        $registry = DomainParser::getRegistryName($domain);
        $error['status'] = Config::get('domain.status.error');
        $error['errors'] = Config::get('domain.status.error');

        if (is_null($registry)) return $error;

        try {
            $request = Http::domain($registry)->delete(
                Config::get('domain.database_delete'),
                ['name' => $domain]
            );

            return $request->json();
        } catch (RequestException $e) {
            $error['status'] = $e->response->json();
            return $error;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage()));
            throw new FailedRequestException(520, 'Error Unknown', 'Unexpected response');
        }
    }     
}
