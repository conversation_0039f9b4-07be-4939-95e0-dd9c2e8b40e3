<?php

namespace App\Providers;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\GoogleCloudLogger;
use App\Modules\CustomLogger\Services\LocalLogger;
use App\Modules\SecretManager\ManualSecretManagerImplementatation;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class AuthLoggerProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(AuthLogger::class, function (Application $app) {

            $ENV = env('APP_ENV');
            $URL = env('APP_URL');

            if (strcmp($ENV, 'production') == 0) {
                $secretManager = new ManualSecretManagerImplementatation();
                $PROJECT_ID = $secretManager->getMeta('PROJECT_ID');

                if (!str_contains($URL, 'strangedomains.com')) {
                    $ENV = 'dev-' . $ENV;
                }

                return AuthLogger::instance(new GoogleCloudLogger($PROJECT_ID, $ENV . '-admin.log'));
            } else {
                return AuthLogger::instance(new LocalLogger('admin'));
            }
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // app(AuthLogger::class);
    }
}
