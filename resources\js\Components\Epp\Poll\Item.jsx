//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import {
    MdMoreVert,
    MdOutlineStopCircle,
    MdOutlineCheckCircleOutline,
} from "react-icons/md";

//* COMPONENTS
import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import { normalizeText } from "@/Components/Util/TextParser";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import convertToDatetime from "@/Util/convertToDatetime";
import useOutsideClick from "@/Util/useOutsideClick";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Item(
    {
        item,
        registry
    }
)
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);
 
    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(ref, () => {
        setShow(false);
    });

    return (
        <tr className="hover:bg-gray-100">
            <td>
                {
                    hasPermission('epp.poll.view')
                        ?
                            <Link
                                as="button"
                                method="post"
                                href={route("epp.poll.view", {
                                    id: item.id,
                                    _query: {
                                        type: item.type,
                                    },
                                })}
                                data={{ item: item, registry: registry }}
                            >
                                <span className=" font-semibold cursor-pointer text-link">{normalizeText(item.type)}</span>
                            </Link>
                        :
                            <span className="font-semibold">{normalizeText(item.type)}</span>
                }
            </td>
            <td>
                <span>{item.summary.name}</span>
            </td>
            <td>
                <span>{item.message}</span>
            </td>
            <td>
                <span>{new Date(item.created).toLocaleString()}</span>
            </td>
        </tr>
    );
}
