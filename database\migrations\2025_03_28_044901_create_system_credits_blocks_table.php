<?php

use App\Models\Admin;
use App\Modules\AdminCredit\Services\AdminCreditBlockService;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_credits_blocks', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Admin::class);
            $table->unsignedBigInteger('block_index');
            $table->string('type'); // credit - debit
            $table->double('running_balance');
            $table->double('amount');
            $table->string('previous_hash');
            $table->string('hash');
            $table->string('created_at');
            $table->timestamp('updated_at')->nullable();
        });

        AdminCreditBlockService::instance()->createGenesis();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_credits_blocks');
    }
};
