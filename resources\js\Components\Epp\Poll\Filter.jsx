import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { offFilter, updateFieldValue } from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function Filter() {
    const { orderby, domain, type, registry, isPrev, cursor } = route().params;
    const config = {
        container: {
            active: false,
            reload: false,
        },

        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [
                    "received:desc", "received:asc",
                    "domain:desc", "domain:asc",
                ],
                name: "Order By",
            },
            domain: {
                active: false,
                value: domain ? [domain] : [],
                type: "text",
                name: "Domain",
            },
            type: {
                active: false,
                value: type ? [type] : [],
                type: "option",
                items: [
                    'Domain Transfer',
                    'Domain Renew',
                    'Notification'
                ],
                name: "Type",
            }
        },
    };

    const [filter, setFilter] = useState(config);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, () => {
        const updatedFilter = offFilter(filter);
        setFilter({ ...updatedFilter });

        if (!updatedFilter.container.reload) return;

        toast.info("Reloading Data, Please Wait...");
        updatedFilter.container.reload = false;
        submit({ ...updatedFilter });
    });

    const submit = (updatedFilter) => {
        let { orderby, domain, type } = updatedFilter.field;
        let payload = {
            cursor: cursor || null,
            registry,
            isPrev
        };

        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (domain.value.length > 0) payload.domain = domain.value[0];
        if (type.value.length > 0) payload.type = type.value[0];

        router.get(route("epp.poll", payload));
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");

        setFilter({
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        });
    };

    return (
        <div className="flex items-center relative">
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />
                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.domain}
                    fieldKey="domain"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => setFilter(offFilter(filter))}
                    placeholder="Search Domain"
                />
                <OptionFilter
                    fieldProp={field.type}
                    fieldKey="type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
