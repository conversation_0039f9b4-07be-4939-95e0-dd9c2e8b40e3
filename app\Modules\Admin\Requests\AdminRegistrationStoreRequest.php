<?php

namespace App\Modules\Admin\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\StrongPasswordRule; 

class AdminRegistrationStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'          => ['required', 'min:2', 'max:100'],
            'email'         => ['required', 'email:rfc,dns', 'max:100'],
            'password'      => ['required', 'min:8', 'max:100', new StrongPasswordRule(), 'confirmed']
        ];
    }

    public function messages()
    {
        return [
            //...
        ];
    }
}
