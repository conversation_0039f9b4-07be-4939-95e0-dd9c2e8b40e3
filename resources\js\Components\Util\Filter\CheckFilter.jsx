import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";

export default function CheckFilter({
    fieldKey,
    fieldProp,
    handleFieldUpdateValue,
}) {
    const { active, items, value } = fieldProp;
    return (
        <DropDownContainer show={active} className="left-full top-0">
            {items.map((i) => {
                return (
                    <div
                        key={"filter-check-" + i}
                        className="flex items-center space-x-2 cursor-pointer px-3 py-1 text-left leading-5 text-gray-700 min-w-[8rem] hover:bg-gray-100"
                        onClick={() => handleFieldUpdateValue(fieldKey, i)}
                    >
                        <Checkbox
                            key={"check-option-" + i}
                            checked={value.includes(i)}
                            name={i}
                            value={i}
                            className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                            handleChange={(i) => {}}
                        />
                        <span>{i}</span>
                    </div>
                );
            })}
        </DropDownContainer>
    );
}
