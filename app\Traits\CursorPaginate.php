<?php

namespace App\Traits;

trait CursorPaginate
{
    public static function cursor(object $cursor, array $param = [], array $others = [])
    {
        if (count($param) > 0) {
            $param = '&'.implode('&', $param);
        } else {
            $param = '';
        }

        $total = $cursor->total() ? $cursor->total() : 0;
        $currentPage = $cursor->currentPage();
        $perPage = $cursor->perPage();
        $itemCount = min($currentPage * $perPage, $total);

        return [
            'onFirstPage' => $cursor->onFirstPage(),
            'onLastPage' => $cursor->onLastPage(),
            'nextPageUrl' => $cursor->nextPageUrl().$param,
            'previousPageUrl' => $cursor->previousPageUrl().$param,
            'items' => $cursor->items(),
            'itemCount' => $itemCount,
            'total' => $cursor->total(),
            'others' => $others,
        ];
    }
}
