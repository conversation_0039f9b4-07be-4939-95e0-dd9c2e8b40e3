<?php

namespace App\Listeners;

use App\Events\AdminActionEvent;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;

class AdminActionListener
{
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(AdminActionEvent $event): void
    {
        try {
            DB::table('admin_transaction_histories')->insert([
                'admin_id' => $event->adminId,
                'type' => $event->type,
                'message' => $event->message,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } catch (Exception $e) {
            app(AuthLogger::class)->error('Failed to log admin action: ' . $e->getMessage());
        }
    }
}
