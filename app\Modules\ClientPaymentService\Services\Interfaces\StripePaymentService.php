<?php

namespace App\Modules\ClientPaymentService\Services\Interfaces;

use App\Modules\ClientPaymentService\Constants\PaymentServiceType;
use App\Modules\ClientPaymentService\Contracts\PaymentServiceInterface;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use Illuminate\Support\Facades\DB;

class StripePaymentService implements PaymentServiceInterface
{
    use UserLoggerTrait;

    public function getPaymentService(object $paymentService)
    {
        $data = DB::client()->table('payment_services')
            ->join('stripe_transactions', 'stripe_transactions.id', '=', 'payment_services.stripe_id')
            ->join('payment_summaries', 'payment_summaries.payment_service_id', '=', 'payment_services.id')
            ->where('payment_services.id', $paymentService->id)
            ->where('payment_services.user_id', $paymentService->user_id)
            ->where('stripe_transactions.id', $paymentService->stripe_id)
            ->select(
                'payment_services.*',
                'stripe_transactions.gross_amount',
                'stripe_transactions.net_amount',
                'stripe_transactions.service_fee',
                'payment_summaries.paid_amount as paid_amount',
                'payment_summaries.total_amount as total_amount',
                'payment_summaries.name as summary_name',
                'payment_summaries.type as summary_type',
            )->get()->first();

        $data->payment_service_type = PaymentServiceType::STRIPE;

        return $data;
    }
}
