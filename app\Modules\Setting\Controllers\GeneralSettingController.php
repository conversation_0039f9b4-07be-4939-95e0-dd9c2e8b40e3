<?php

namespace App\Modules\Setting\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Setting\Services\GeneralSettingService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GeneralSettingController extends Controller
{
    public function index()
    {
        return Inertia::render('Setting/General', ['settings' => GeneralSettingService::get()]);
    }

    public function update(Request $request)
    {
        GeneralSettingService::update($request->key, $request->value, $request->type);

        return redirect()->back();
    }
}
