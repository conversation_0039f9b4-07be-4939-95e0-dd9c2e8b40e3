//* PACKAGES
import React, {useState, useEffect} from 'react'
import { router } from "@inertiajs/react";

//* ICONS
import { MdOutlineFilterAlt } from 'react-icons/md';
import { MdArrowBackIos, MdArrowForwardIos } from "react-icons/md";

//* COMPONENTS
import SecondaryLink from "@/Components/SecondaryLink";
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppTableComponent(
    {
        pageRoute     = '',
        tableName     = 'rows',
        columnHeaders = [],
        columnRows    = [],
        paginationInfo,
        shouldPreserveState = false, 
        filterComponent = null, 
    }
)
{
    //! PACKAGE
    //...

    //! VARIABLES
    const showItemsSelectOptions = [10, 20, 50, 100];

    //! STATES
    const [stateInputShowItems, setStateInputShowItems] = useState(route().params.showItems ?? 10); 
    const [stateIsPageLoading, setStateIsPageLoading] = useState(false); 

    //! FUNCTIONS
    function handleChangeShowItems(showItems)
    {
        router.get(
            route(pageRoute),
            {
                ...route().params,
                showItems: showItems,
            }, 
            {
                preserveState: true, 
                replace : true
            }
        );
    }

    router.on(
        "start",
        () =>
        {
            setStateIsPageLoading(true);
        }
    );

    router.on(
        "finish",
        () =>
        {
            setStateIsPageLoading(false);
        }
    );

    //! USE EFFECTS 
    //...

    return (
        <div
            className='flex flex-col gap-8'
        >

            {/* SECTION HEADER */}
            <section
                className="flex flex-col gap-4"
            >
                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={stateInputShowItems}
                        onChange={(e) =>
                            {
                                setStateInputShowItems(e.target.value)
                                handleChangeShowItems(e.target.value)
                            }
                        }
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {
                        
                            showItemsSelectOptions.map((val) =>
                                (
                                    <option key={val} value={val}>
                                        {val}
                                    </option>
                                )
                            )
                        }
                    </select>
                </div>

                {
                    filterComponent == null 
                        ?
                            null
                        :
                            <div
                                className="flex items-center space-x-2 flex-wrap min-h-[2rem]"
                            >
                                <label className="flex items-center">
                                    <MdOutlineFilterAlt />
                                    <span className="ml-2 text-sm text-gray-600"> Filter: </span>
                                </label>
                                {filterComponent}
                            </div>
                }
            </section>

            {/* SECTION BODY */}
            <section>
                <table
                    className='w-full'
                >
                    <thead
                        className='bg-gray-100'
                    >
                        <tr
                            className='border-b border-gray-200'
                        >
                            {
                                columnHeaders.map(
                                    (item, index) => 
                                    {
                                        return (
                                            <th
                                                key={index}
                                                className='sticky top-0 z-10 bg-gray-100 border-b border-gray-200 text-left capitalize py-4 px-4 font-medium text-sm'
                                            >
                                                {item.label}
                                            </th>
                                        )
                                    }
                                )
                            }
                        </tr>
                    </thead>
                    <tbody>
                        {
                            stateIsPageLoading == true 
                                ?
                                    <tr>
                                        <td
                                            colSpan={columnHeaders.length}
                                            className='py-5'
                                        >
                                            <div
                                                className="flex flex-col items-center mx-auto container gap-4 rounded-lg"
                                            >
                                                <LoaderSpinner h='h-12' w='w-12' />
                                                <span className=" text-primary duration-200 ease-linear animate-pulse">
                                                    Loading Data...
                                                </span>
                                            </div>
                                        </td>
                                    </tr> 
                                :
                                    columnRows.length == 0 
                                        ?
                                            <tr
                                            >
                                                <td 
                                                    colSpan={columnHeaders.length}
                                                    className='py-5 font-medium  text-center'
                                                >
                                                    No Records Found
                                                </td>    
                                            </tr>
                                        :
                                            columnRows.map(
                                                (row, rowIndex) => 
                                                {
                                                    return (
                                                        <tr
                                                            key={rowIndex}
                                                        >
                                                            {
                                                                row.map(
                                                                    (item, itemIndex) =>
                                                                    {
                                                                        return (
                                                                            <td
                                                                                key={itemIndex}
                                                                                className='py-4 px-4 font-normal text-sm'
                                                                            >
                                                                                {item.value}
                                                                            </td>
                                                                        ); 
                                                                    }
                                                                )
                                                            }
                                                        </tr>
                                                    )
                                                }
                                            )
                        }
                    </tbody>
                </table>
            </section>

            {/* SECTION FOOTER */}
            <section
                className='flex justify-between items-center'
            >
                <aside>
                    {
                        (paginationInfo.itemCount > 0 && paginationInfo.total > 0) &&
                            <span
                                className="text-sm text-gray-600 capitalize"
                            >
                                Showing {paginationInfo.itemCount} of {paginationInfo.total} {tableName}
                            </span>
                    }
                </aside>
                <aside>
                    <SecondaryLink
                        href={paginationInfo.previousPageUrl}
                        processing={paginationInfo.onFirstPage}
                        shouldPreserveState={shouldPreserveState}
                    >
                        <MdArrowBackIos />
                    </SecondaryLink>
                    <SecondaryLink
                        href={paginationInfo.nextPageUrl}
                        processing={paginationInfo.onLastPage}
                        shouldPreserveState={shouldPreserveState}
                    >
                        <MdArrowForwardIos />
                    </SecondaryLink>
                </aside>
            </section>
        </div>
    );
}
