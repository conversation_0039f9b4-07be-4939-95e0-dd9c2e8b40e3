<?php

namespace App\Modules\DomainHistory\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\DomainHistory\Services\DomainHistoryService;
use App\Modules\DomainHistory\Requests\ShowListRequest;
use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
class DomainHistoryController extends Controller
{
    protected $domainHistoryService;

    public function __construct(DomainHistoryService $domainHistoryService)
    {
        $this->domainHistoryService = $domainHistoryService;
    }

    public function index(ShowListRequest $request)
    {
        $data = $this->domainHistoryService->prepareDomainHistoryData($request);

        return Inertia::render('DomainHistory/Index', $data);
    }
}
