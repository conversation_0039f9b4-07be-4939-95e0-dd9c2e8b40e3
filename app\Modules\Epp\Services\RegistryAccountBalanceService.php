<?php

namespace App\Modules\Epp\Services;

use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\GeneralSettingService;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use stdClass;

use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class RegistryAccountBalanceService
{
    use CursorPaginate;

    private static $pageLimit = 50;

    private static $minimum_balance;

    public static function allRegistry()
    {
        return DB::client()->table('registries')->get();
    }

    public static function getMinimumBalance()
    {
        return floatval(GeneralSettingService::getValueByKey(SettingKey::MINIMUM_REGISTRY_BALANCE));
    }

    public static function debit(stdClass $balance, float $amount, string $transactionType, string $description)
    {

        $validator = Validator::make(['balance' => $balance->balance, 'amount' => $amount], [
            'balance' => 'required|numeric',
            'amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $validator;
        }

        $afterDeduction = self::fillTransactionDetail($balance, $transactionType, $description);
        $afterDeduction['balance'] = $balance->balance + $amount;
        $afterDeduction['debit'] = $amount;
        $afterDeduction['credit'] = 0;

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::PAYMENT_CREATED,
            "Registry Account Debit: $" . number_format($amount, 2) . "$ by " . auth()->user()->email
        ));

        return DB::client()->table('registry_account_balances')->insert($afterDeduction);
    }

    public static function credit(stdClass $balance, float $amount, string $transactionType, string $description)
    {

        $validator = Validator::make(['balance' => $balance->balance, 'amount' => $amount], [
            'balance' => 'required|numeric',
            'amount' => 'required|numeric|min:0|lte:balance',
        ]);

        if ($validator->fails()) {
            return $validator;
        }

        $afterDeduction = self::fillTransactionDetail($balance, $transactionType, $description);
        $afterDeduction['balance'] = $balance->balance - $amount;
        $afterDeduction['debit'] = 0;
        $afterDeduction['credit'] = $amount;

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::PAYMENT_CREATED,
            "Registry Account Credit: $" . number_format($amount, 2) . "$ by " . auth()->user()->email
        ));

        return DB::client()->table('registry_account_balances')->insert($afterDeduction);
    }

    private static function fillTransactionDetail(stdClass $balance, string $transactionType, string $description)
    {
        $afterDeduction = [];
        $afterDeduction['previous_id'] = $balance->id;
        $afterDeduction['registry_id'] = $balance->registry_id;
        $afterDeduction['type'] = $transactionType;
        $afterDeduction['description'] = $description;
        $afterDeduction['updated_at'] = now();
        $afterDeduction['created_at'] = now();

        return $afterDeduction;
    }

    public static function balance(int $registryId)
    {

        $balance = DB::client()->table('registry_account_balances')->where('registry_id', $registryId)->orderBy('id', 'desc')->first();

        if (! $balance) {
            throw new \Exception('registry id does not exists');
        }

        return $balance;
    }

    public static function transactions(int $registryId)
    {
        $builder = DB::client()->table('registry_account_balances')
            ->where('registry_id', $registryId)
            ->orderBy('id', 'desc')
            ->paginate(self::$pageLimit);

        return CursorPaginate::cursor($builder, ['registry_id='.$registryId]);
    }

    public static function checkMinimumRegistryBalanceBulk()
    {
        $result = [];
        $registries = DB::client()->table('registries')->pluck('name', 'id')->toArray();

        foreach ($registries as $key => $registry) {
            $account = self::balance($key);
            self::$minimum_balance = self::getMinimumBalance();
            $is_less_minimum = false;

            if ($account->balance <= self::$minimum_balance) {
                $is_less_minimum = true;
            }

            $result[] = [
                'id' => $key,
                'name' => $registry,
                'is_less_minimum' => $is_less_minimum,
            ];
        }

        return $result;
    }

    public static function checkBalanceFlagBulk()
    {
        $result = [];
        $registries = DB::client()->table('registries')->pluck('name', 'id')->toArray();

        foreach ($registries as $key => $registry) {
            $account = self::balance($key);

            $result[] = [
                'id' => $key,
                'name' => $registry,
                'is_flag' => $account->is_flag,
            ];
        }

        return $result;
    }
}
