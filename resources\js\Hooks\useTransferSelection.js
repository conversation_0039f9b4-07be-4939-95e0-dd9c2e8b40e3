import { useState, useCallback } from 'react';

export function useTransferSelection(transfers = []) {
    const [selectedItems, setSelectedItems] = useState([]);
    const [selectAll, setSelectAll] = useState(false);

    const handleSelectAllChange = useCallback((checked) => {
        setSelectAll(checked);
        
        if (checked) {
            const allIds = transfers.map((item) => item.id);
            setSelectedItems(allIds);
        } else {
            setSelectedItems([]);
        }
    }, [transfers]);

    const handleItemCheckboxChange = useCallback((itemId, checked) => {
        setSelectedItems((prev) => {
            let updated;
            if (checked) {
                updated = [...prev, itemId];
            } else {
                updated = prev.filter((id) => id !== itemId);
            }

            setSelectAll(updated.length === transfers.length && transfers.length > 0);
            
            return updated;
        });
    }, [transfers.length]);

    const clearSelection = useCallback(() => {
        setSelectedItems([]);
        setSelectAll(false);
    }, []);

    const isItemSelected = useCallback((itemId) => {
        return selectedItems.includes(itemId);
    }, [selectedItems]);

    const getSelectedCount = useCallback(() => {
        return selectedItems.length;
    }, [selectedItems.length]);

    const hasSelection = useCallback(() => {
        return selectedItems.length > 0;
    }, [selectedItems.length]);

    const selectItems = useCallback((ids) => {
        setSelectedItems(ids);
        setSelectAll(ids.length === transfers.length && transfers.length > 0);
    }, [transfers.length]);

    const toggleItemSelection = useCallback((itemId) => {
        const isSelected = selectedItems.includes(itemId);
        handleItemCheckboxChange(itemId, !isSelected);
    }, [selectedItems, handleItemCheckboxChange]);

    return {
        selectedItems,
        selectAll,
        handleSelectAllChange,
        handleItemCheckboxChange,
        clearSelection,
        isItemSelected,
        getSelectedCount,
        hasSelection,
        selectItems,
        toggleItemSelection,
    };
}
