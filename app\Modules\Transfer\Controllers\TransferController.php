<?php

namespace App\Modules\Transfer\Controllers;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use App\Modules\Transfer\Requests\ShowListRequest;
use App\Modules\Transfer\Services\TransferDomainService;
use Inertia\Response;

class TransferController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('Transfer/Index', $request->show());
    }

    public function sendResponse(string $action, string $ids): Response
    {
        $message = TransferDomainService::instance()->sendResponse(explode(',', $ids), $action);

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => $message,
            'postRouteName' => 'transfer.view',
            'redirect' => [['route' => route('transfer.view'), 'label' => 'Go back to Outbound Transfer Request']],
        ]);
    }
}
