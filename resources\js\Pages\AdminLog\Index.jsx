//* PACKAGES
import React, {useState, useRef} from 'react'
import { router } from '@inertiajs/react';

//* ICONS
import {
    MdOutlinePersonOutline,
    MdExitToApp,
    MdSecurity,
    MdOutlineCheckCircle,
    MdOutlineSystemUpdateAlt,
    MdFilterList,
    MdAutoDelete,
    MdPersonRemove,
    MdOutlineSend,
    MdVerified,
    MdNotificationsActive,
} from "react-icons/md";
import { VscUnverified } from "react-icons/vsc";
import { HiOutlinePencilSquare } from "react-icons/hi2";
import { TbCategoryPlus } from "react-icons/tb";

//* COMPONENTS
import AdminLayout from "@/Layouts/AdminLayout";
import Item from "@/Components/AdminLog/Item";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import Filter from "@/Components/AdminLog/Filter";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Index({
    logs,
    nextPageUrl,
    previousPageUrl,
    itemCount,
    total = 0,
    onFirstPage,
    onLastPage,
    itemName
})
{
    const { limit = 20 } = route().params ?? {};
    const dropdownRefs = useRef({});
    const dropdownContainerRef = useRef(null);

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    //...

    //! STATES
    const [isLoading, setIsLoading]               = useState(false);
    const [activeDropdown, setActiveDropdown]     = useState(null);
    const [loadingPayload, setLoadingPayload]     = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(dropdownContainerRef, () => {
        setActiveDropdown(null);
    });

    const handleView = (logId) => {
        setLoadingPayload(true);
        // For now, just show the log data since we're doing frontend only
        console.log('Viewing log:', logs.find(log => log.id === logId));
        setLoadingPayload(false);
        setActiveDropdown(null);
    };

    const toggleDropdown = (logId) => {
        setActiveDropdown(activeDropdown === logId ? null : logId);
    };

    const typeIcons = {
        ADMIN_LOGIN: <MdOutlineCheckCircle />,
        ADMIN_LOGOUT: <MdExitToApp />,
        USER_MANAGEMENT: <MdOutlinePersonOutline />,
        DOMAIN_UPDATE: <HiOutlinePencilSquare />,
        DOMAIN_DELETED: <MdAutoDelete />,
        CLIENT_DELETE: <MdPersonRemove />,
        CLIENT_INVITE: <MdOutlineSend />,
        BANK_TRANSFER_VERIFIED: <MdVerified />,
        BANK_TRANSFER_REJECTED: <VscUnverified />,
        NOTIFICATION_CREATED: <MdNotificationsActive />,
        SECURITY_UPDATE: <MdSecurity />,
        SYSTEM_UPDATE: <MdOutlineSystemUpdateAlt />,
        SETTINGS_UPDATE: <TbCategoryPlus />,
    };

    const getStatusLabel = (type) => {
        const normalizedType = type.replace(/ /g, "_").toUpperCase();
        const icon = typeIcons[normalizedType] || <MdOutlinePersonOutline />;
        return { label: type, icon };
    };

    const groupedLogs =
        logs?.length > 0
            ? logs.reduce((acc, log) => {
                const date = log.formatted_created_at;
                if (!acc[date]) {
                    acc[date] = [];
                }
                acc[date].push(log);
                return acc;
            }, {})
            : {};

    const LoadingSkeleton = () => (
        <div className="animate-pulse">
            {[1, 2, 3].map((group) => (
                <div key={group} className="mb-8">
                    {/* Date skeleton */}
                    <div className="flex items-center mb-4">
                        <div className="bg-gray-200 rounded-md w-32 h-8"></div>
                        <div className="flex-grow border-t border-gray-200 ml-2"></div>
                    </div>

                    {/* Table skeleton */}
                    <div className="bg-white mb-4">
                        {[1, 2, 3].map((row) => (
                            <div
                                key={row}
                                className="flex border-b border-gray-100 py-4"
                            >
                                <div className="w-2/5 px-4">
                                    <div className="flex items-center space-x-2">
                                        <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                                        <div className="w-24 h-4 bg-gray-200 rounded"></div>
                                    </div>
                                </div>
                                <div className="w-2/5 px-12">
                                    <div className="w-48 h-4 bg-gray-200 rounded"></div>
                                </div>
                                <div className="w-1/5 px-12">
                                    <div className="w-16 h-4 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );

    const loadMore = (cursor) => {
        router.visit(route("admin.logs"), {
            data: {
                cursor: cursor,
                type: router.page.url.searchParams.get('type'),
                date: router.page.url.searchParams.get('date'),
            },
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleLimitChange = (e) => {
        router.get(route("admin.logs"), {
            ...route().params,
            limit: e.target.value,
            page: 1,
        });
    };



    return (
        <AdminLayout>
            <div className="flex flex-col">
                {/* <div className="flex items-center space-x-2 mb-4 pl-6">
                    <h2 className="text-2xl font-bold">
                        Admin Activity Logs
                    </h2>
                </div> */}
                <div className="flex flex-col">
                    <div className="px-4 w-full">
                        <div className="mx-auto container max-w-[1200px] mt-4 flex flex-col justify-between">
                            <h2 className="text-4xl font-bold">
                                Admin Logs
                            </h2>
                            <div
                                className="flex justify-start"
                                style={{ position: "relative", top: "15px" }}
                            >
                                <label className="mr-2 text-sm pt-1 text-gray-600">
                                    Show
                                </label>
                                <select
                                    value={limit}
                                    onChange={handleLimitChange}
                                    className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                                >
                                    {[20, 25, 30, 40, 50, 100].map((val) => (
                                        <option key={val} value={val}>
                                            {val}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="flex justify-between items-center mt-4 pt-4 pb-4">
                                <div className="flex items-center space-x-2">
                                    <MdFilterList className="text-xl" />
                                    <span>Filter:</span>
                                    <Filter
                                        routeName="admin.logs"
                                    />
                                </div>
                            </div>

                            {/* Content Area */}
                            <div className="mt-4">
                                {isLoading ? (
                                    <LoadingSkeleton />
                                ) : Object.keys(groupedLogs).length === 0 ? (
                                    <div className="bg-white rounded-md p-8 text-center text-gray-500">
                                        <p className="text-lg">No Logs Found</p>
                                    </div>
                                ) : (
                                    Object.entries(groupedLogs).map(
                                        ([date, logs]) => (
                                            <React.Fragment key={date}>
                                                <div className="flex items-center mb-4">
                                                    <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                                        {date}
                                                    </div>
                                                    <div className="flex-grow border-t border-gray-300 ml-4"></div>
                                                </div>
                                                <div className="space-y-4 mb-8">
                                                    {logs.map(
                                                        (item, index) => (
                                                            <Item
                                                                key={index}
                                                                item={item}
                                                                getStatusLabel={getStatusLabel}
                                                                activeDropdown={activeDropdown}
                                                                toggleDropdown={toggleDropdown}
                                                                handleView={handleView}
                                                                loadingPayload={loadingPayload}
                                                                dropdownRefs={dropdownRefs}
                                                                dropdownContainerRef={dropdownContainerRef}
                                                                hasPermission={hasPermission}
                                                            />
                                                        )
                                                    )}
                                                </div>
                                            </React.Fragment>
                                        )
                                    )
                                )}
                            </div>

                            {!isLoading && (
                                <div className="mt-6">
                                    <CursorPaginate
                                        onFirstPage={onFirstPage}
                                        onLastPage={onLastPage}
                                        nextPageUrl={nextPageUrl}
                                        previousPageUrl={previousPageUrl}
                                        itemCount={itemCount}
                                        total={total}
                                        itemName={itemName}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}