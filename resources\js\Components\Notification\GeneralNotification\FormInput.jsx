import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const FormInput = ({ label, type, value, onChange, error, placeholder, minDate, className }) => {
    return (
        <div className="space-y-2">
            <label className="block font-medium">{label}</label>
            {type === 'date' ? (
                <DatePicker
                    selected={value ? new Date(value) : null}
                    onChange={onChange}
                    className={`w-full px-4 py-2 border rounded-md ${error ? 'border-red-500' : ''}`}
                    minDate={minDate}
                    placeholderText={placeholder}
                />
            ) : (
                <input
                    type={type}
                    value={value || ''}
                    onChange={onChange}
                    placeholder={placeholder}
                    className={`w-full px-4 py-2 border rounded-md ${error ? 'border-red-500' : ''} ${className || ''}`}
                />
            )}
            {error && <p className="text-sm text-red-600">{error}</p>}
        </div>
    );
};

export default FormInput; 