import React from "react";
import _DateTimeFormat from "@/Constant/_DateTimeFormat";
import { MdKeyboardBackspace } from "react-icons/md";
import PaymentItem from "./PaymentItem";
import AccountBalanceItem from "./AccountBalanceItem";

export default function AccountBalanceContainer({ data, icann_total }) {
    const formatAmount = (amount) => {
        return amount ? parseFloat(amount).toFixed(2) : "0.00";
    }

    const convertDateTime = (time, format) => {
        switch(format){
            case 1:
                // time is in Datetime format
                return new Date(time + 'Z').toDateString();

                // time is in Timestamp format
                //return new Date(time).toDateString();
            case 2:
                return getRecentTime(time, true);
            default:
                // time is in Datetime format
                return new Date(time + 'Z').toDateString();

                // time is in Timestamp format
                //return new Date(time).toDateString();
        };

    }

    // console.log(data);

    return (
        <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
            <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                <a href="#" onClick={() => window.history.back()}>
                    <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                </a>
                <span className="text-inherit">
                    Account Balance Invoice
                </span>
                <span className="text-gray-500">{data.length} {(data.length == 1) ? "item" : "items"}</span>
            </div>
            <span>{convertDateTime(data[0].created_at, _DateTimeFormat.STRING)} {new Date(data[0].created_at + 'Z').toLocaleTimeString()}</span>
            <div className="flex flex-col space-y-8 pt-8">
                {data.map((e, index) => {
                    return (
                        <>
                            {e.rate ? (
                                <PaymentItem
                                    key={"pi-" + index}
                                    index={index}
                                    item={e}
                                />
                            ) : (
                                <AccountBalanceItem
                                    key={"abi-" + index}
                                    index={index}
                                    item={e}
                                />
                            )

                            }

                        </>
                    );
                })}
            </div>
            <div className="flex items-center justify-between space-y-8 pt-8 text-gray-600 border-b border-gray-200 pb-2 mb-4 ">
            </div>
            <div className="flex flex-col text-lg text-gray-700 font-semibold">
                <div className="flex items-center justify-between">
                    <span className=" text-inherit">Total Amount</span>
                    <span className=" text-inherit">${formatAmount(data[0].net_amount ?? data[0].amount)}</span>
                </div>
                <div className="flex items-center justify-between">
                    <span className=" text-inherit">Total Paid</span>
                    <span className=" text-inherit">${formatAmount(data[0].paid_amount ?? data[0].amount)}</span>
                </div>
            </div>
        </div>
    );
}
