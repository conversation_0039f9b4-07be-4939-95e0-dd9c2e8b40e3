<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Mail\AuditMail;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class MarketAuditService
{
    public static function instance()
    {
        $marketauditservice = new self;

        return $marketauditservice;
    }

    public function generateAudit()
    {
        $firstprev = Carbon::now()->startOfMonth()->subMonthsNoOverflow()->toDateString();
        $lastprev = Carbon::now()->subMonthsNoOverflow()->endOfMonth()->toDateString();

        $res = DB::table('afternic_commissions')
        ->join('market_place_domains', 'afternic_commissions.marketplace_domain_id', 'market_place_domains.id')
        ->join('registered_domains', 'market_place_domains.id', 'registered_domains.id')
        ->join('domains', 'registered_domains.domain_id', 'domains.id')
        ->select('domains.name')
        ->selectRaw('afternic_commissions.*')
        ->whereDate('afternic_commissions.created_at', '>=', $firstprev)
        ->whereDate('afternic_commissions.created_at', '<=', $lastprev)
        ->get()
        ->toArray();

        $code = substr(md5(uniqid(rand(), true)), 6, 6);

        $gross = array_sum(array_column($res, 'price'));
        $commission = array_sum(array_column($res, 'commission'));

        $sale_id = DB::table('afternic_sale_audits')->insertGetId([
            'total_commission' => $commission,
            'total_price' => $gross,
            'total_domain' => count($res),
            'order_started_at' => $firstprev,
            'order_ended_at' => $lastprev,
            'status' => "pending verification",
            'confirm_code' => Hash::make($code)
        ]);

        $ids = array();
        $sales = array();

        foreach($res as $d) {
            array_push($ids, $d->id);
            array_push($sales, ['afternic_commission_id' => $d->id, 'afternic_sale_audit_id' => $sale_id, 'created_at' => Carbon::now()]);
        }

        DB::table('afternic_commissions')
        ->whereIn('id', $ids)
        ->update(['is_audited' => 1]);

        DB::table('afternic_sales')->insert($sales);

        $date = date_create($firstprev);
        $filename = date_format($date, 'M-d-Y_M-t-Y');

        $this->createCSV($res, $gross, $commission, $filename, $code);
    }

    public function createCSV($res, $gross, $commission, $filename, $code)
    {
        $micro_unit = MarketConstants::micro_unit;

        $audit = fopen('php://temp', 'w');
        $tally = array('Total', 'Gross', 'Commission', 'Net');
        $headers = array('domain','price','commission');

        fputcsv($audit, $headers);

        foreach ($res as $data) {
            $row = array(
                $data->name,
                ($data->price / $micro_unit),
                ($data->commission / $micro_unit),
            );

            fputcsv($audit, $row);
        }

        fputcsv($audit, array("\t","\t","\t"));
        fputcsv($audit, $tally);
        fputcsv($audit, array("\t", ($gross / $micro_unit), ($commission / $micro_unit), ($gross - $commission) / $micro_unit));
        rewind($audit);

        $output = stream_get_contents($audit);

        Storage::disk('local')->put("audits/$filename.csv", $output);

        $this->sendEmail($filename, $code, "Strange Marketplace Audit Report $filename");
    }

    public function sendEmail($file_path, $code, $title)
    {
        $arr = explode('_', $file_path);

        $mailData = [
            'title' => $title,
            'auth_code' => $code,
            'time' => $arr[0] . ' to ' . $arr[1],
            'file_path' => "audits/$file_path.csv"
        ];

        Mail::to(Env('AUDIT_EMAIL'))->send(new AuditMail($mailData));
    }

    public function getCommissions() : \Illuminate\Support\Collection
    {
        return DB::table('public.afternic_commissions AS am')
        ->join('public.market_place_domains AS mpd', 'am.marketplace_domain_id', '=', 'mpd.id')
        ->join('public.registered_domains AS rd', 'mpd.registered_domain_id', '=', 'rd.id')
        ->join('public.domains AS d', 'rd.domain_id', '=', 'd.id')
        ->join('public.users AS u', 'mpd.user_id', '=', 'u.id')
        ->select('d.id AS domain_id', 'rd.id AS reg_domain_id', 'mpd.id AS market_domain_id', 'u.first_name', 'u.last_name', 'mpd.status', 'am.price', 'am.commission', 'am.is_audited', 'am.started_at', 'd.name AS domain', 'am.created_at', 'am.updated_at')
        ->get();
    }
}