<?php

namespace App\Modules\Guest\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Guest\Services\AdminResponseService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateGuestRequest implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $id;

    private $status;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    public function __construct($id, $status)
    {
        $this->id = $id;
        $this->status = $status;
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp . $this->id;
    }

    public function middleware(): array
    {
        // throttle the jobs if it has 3 exceptions within 10minutes
        return [(new ThrottlesExceptions(3, 10))->backoff(30 * 60)];
    }

    public function handle(): void
    {
        // Log::info('UpdateGuestRequest : id='.$this->id.' status='.$this->status);
        try {
            AdminResponseService::update($this->id, $this->status);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            app(AuthLogger::class)->error($e->getMessage());
            $this->fail();
        }
    }
}
