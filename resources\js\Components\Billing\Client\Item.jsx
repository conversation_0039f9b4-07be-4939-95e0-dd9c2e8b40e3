//* PACKAGES
import React, {useState, useRef, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";

//* ENUMS
//...

//* CONSTANTS
import _PaymentSummary from "../../../Constant/_PaymentSummary";

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Item(
    {
        item
    }
)
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(ref, () => {
        setShow(false);
    });

    const capitalizeFirstLetter = str => {
        if (!str.includes('_')) return str;

        str = str.toLowerCase();
        const words = str.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        const newWords = words.join(' ');
        return newWords.replace(/\b\w/g, char => char.toUpperCase());
    }

    const setDate = date => {
        return new Date(date + 'Z').toDateString() + ' ' + new Date(date + 'Z').toLocaleTimeString();
    }

    const getAmount = item => {
        // ${(parseInt(item.paid_amount) != 0) ? item.paid_amount : item.total_amount}
        var text = '';
        if (item.type == _PaymentSummary.TYPES.PAYMENT_INVOICE ||
            item.type == _PaymentSummary.TYPES.MARKETPLACE_INVOICE ||
            item.type == _PaymentSummary.TYPES.MULTI_CHECKOUT_INVOICE
        ) {
            text = '-';
        }

        var amount = parseFloat(item.paid_amount) != 0 ? item.paid_amount : item.total_amount;

        text += '$' + amount;
        return text;
    }

    return (
        <tr className="hover:bg-gray-100 ">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    {
                        hasPermission("billing.client.view") 
                            ?
                                <Link
                                    href={route("billing.client.view", { id: item.id })}
                                    method="get"
                                    as="button"
                                    type="button"
                                >
                                    <span className="text-link cursor-pointer">
                                        {setDate(item.created_at)}
                                    </span>
                                </Link>
                            :
                                setDate(item.created_at)

                    }
                </label>
            </td>
            <td>
                <span>{item.first_name} {item.last_name}</span>
            </td>
            <td>
                <span>{item.email}</span>
            </td>
            <td>
                <span>{capitalizeFirstLetter(item.name)}</span>
            </td>
            <td>
                <span className="flex items-center pl-2 space-x-2">
                    {item.source ? _PaymentSummary.SERVICE_TYPE_TEXT[item.source] : 'N/A'}
                </span>
            </td>
            <td>
                <span className="pl-4 space-x-4">{getAmount(item)}</span>
            </td>
        </tr>
    );
}
