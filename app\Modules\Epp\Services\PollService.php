<?php

namespace App\Modules\Epp\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Traits\Epp\PollCursorPaginate;
use App\Util\Helper\HttpResponse;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PollService
{
    use PollCursorPaginate;

    private static $registry = [
        'verisign',
        'verisign2',
        'pir',
        'pir2',
    ];

    private static $sessionCursor = 'pollCursor';

    public static function get($param)
    {
        $payload = ['size' => self::$size, 'cursor' => $param['cursor'] ?? null];

        try {
            $request = Http::poll($param['registry'])->post(Config::get('epp.poll') . '/all', $payload);
        } catch (Exception $e) {
            app(AuthLogger::class)->error(json_encode($e->getMessage()));
            throw new FailedRequestException(500, 'Internal Server Error', 'INTERNAL SERVER ERROR');
        }

        $response = $request->json();
        $data = $response['data'] ?? [];
        $items = $data ?? [];
        $nextCursor = $data['cursor'] ?? null;
        $isPrev = filter_var($param['isPrev'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $items['polls'] = array_values(array_filter($items['polls'], fn($poll) => !empty($poll['summary'] ?? null)));
        $onFirstPage = empty($param['cursor']);
        $onLastPage = empty($items['polls']) || empty($nextCursor);
        $items['polls'] = array_values(array_filter($items['polls'], fn($poll) => self::filterPoll($poll, $param)));
        if (!empty($param['orderby'])) {
            $items['polls'] = self::sortPolls($items['polls'], $param['orderby']);
        }
        $previousCursor = self::trackCursor($param['cursor'] ?? null, $isPrev);

        return [
            'onFirstPage' => $onFirstPage,
            'onLastPage' => $onLastPage,
            'nextPageCursor' => $nextCursor,
            'previousPageCursor' => $previousCursor,
            'items' => $items,
        ];
    }

    private static function filterPoll(array $poll, array $param): bool
    {
        if (!empty($param['domain'])) {
            $name = $poll['summary']['name'] ?? '';
            if (stripos($name, $param['domain']) === false) return false;
        }

        if (!empty($param['type'])) {
            $param['type'] = strtoupper(str_replace(' ', '_', $param['type']));
            if (($poll['type'] ?? '') !== $param['type']) return false;
        }

        return true;
    }

    private static function sortPolls(array $polls, string $orderBy): array
    {
        [$field, $direction] = explode(':', $orderBy);

        return collect($polls)->sort(function ($a, $b) use ($field, $direction) {
            $aValue = null;
            $bValue = null;

            switch ($field) {
                case 'received':
                    $aValue = $a['created'] ?? '';
                    $bValue = $b['created'] ?? '';
                    break;
                case 'domain':
                    $aValue = $a['summary']['name'] ?? '';
                    $bValue = $b['summary']['name'] ?? '';
                    break;
                default:
                    return 0;
            }

            return $direction === 'desc'
                ? strcmp($bValue, $aValue)
                : strcmp($aValue, $bValue);
        })->values()->all();
    }


    private static function trackCursor(?string $currentCursor, bool $isPrev): ?string
    {
        if (empty($currentCursor)) {
            session()->forget(self::$sessionCursor);
            return null;
        }

        $stack = session()->get(self::$sessionCursor, []);

        if ($isPrev) {
            $index = array_search($currentCursor, $stack);
            if ($index !== false) $stack = array_slice($stack, 0, $index + 1);
            session()->put(self::$sessionCursor, $stack);
            $previousCursor = $stack[count($stack) - 2] ?? null;
        } else {
            $previousCursor = end($stack);

            if (!in_array($currentCursor, $stack)) {
                $stack[] = $currentCursor;
                session()->put(self::$sessionCursor, $stack);
            }
        }

        return $previousCursor;
    }

    // public static function get($param, $url)
    // {
    //     $registryName = $param['registry'];
    //     $cursorKey = $param['cursor'];
    //     $isPrev = ($param['isPrev'] == '1') ? true : false;

    //     if (!$registryName) return PollCursorPaginate::cursor(['data' => [], 'url' => $url], []);

    //     if ($isPrev && (count(session()->get(self::$sessionCursor)) == 1)) {
    //         $cursorKey = null;
    //         PollCursorPaginate::getSessionCursorKey(true);
    //     }

    //     if ($isPrev && (count(session()->get(self::$sessionCursor)) > 1)) {
    //         $checkCursor = PollCursorPaginate::getSessionCursorKey();
    //         if (($cursorKey == $checkCursor)) {
    //             $cursorKey = PollCursorPaginate::getSessionCursorKey(true);
    //         }
    //     }

    //     $payload = ['size' => self::$size, 'cursor' => $cursorKey];

    //     try {
    //         $request = Http::poll($registryName)->post(Config::get('epp.poll') . '/all', $payload);
    //     } catch (Exception $e) {
    //         app(AuthLogger::class)->error(json_encode($e->getMessage()));
    //         throw new FailedRequestException(500, 'Internal Server Error', 'INTERNAL SERVER ERROR');
    //     }

    //     $data = $request->json();
    //     $cursorData = (array_key_exists('data', $data)) ? $data['data'] : [];

    //     $cursor = [
    //         'data' => $cursorData,
    //         'url' => $url
    //     ];

    //     $param['cursor'] = $cursorKey;

    //     // dd($cursor, $param);
    //     $response = PollCursorPaginate::cursor($cursor, $param);

    //     $response['items']['polls'] = array_values(array_filter(
    //         $response['items']['polls'],
    //         fn($poll) => !empty($poll['summary'] ?? null)
    //     ));

    //     return $response;
    // }


    public static function view(string $dbConnection, string $id, string $registry)
    {
        $poll = DB::connection($dbConnection)->table('polls')->where('server_id', $id)->get()->first();
        if ($poll != null) {
            return json_decode($poll->json_data, true);
        }


        // $request = Http::registry($registryName)->post(Config::get('epp.poll') . '/all', $payload);

        try {
            $poll = Http::registry($registry)->get(Config::get('epp.poll') . '/' . $id)->json();
            // dd($poll);

            // app(AuthLogger::class)->error('poll get from epp: '.json_encode($poll));
        } catch (Exception $e) {
            throw new FailedRequestException(500, 'Internal Server Error', 'INTERNAL SERVER ERROR');
        }

        if (array_key_exists('status', $poll) && $poll['status'] == 'OK') {
            // $data = PollXMLParser::xmlToJson($poll['data']['body'], $poll['data']['type']);

            // dd($data['json_data']);
            // return json_decode($data['json_data'], true);
            if (array_key_exists('data', $poll)) {
                // dd($poll['data']);
                return $poll['data'];
            }

            return [];
        }

        // throw new FailedRequestException(401, 'Failed poll with id '.$id.' does not exist', 'INVALID ID');

        $errorMsg = 'Failed poll with id ' . $id . ' does not exist';
        Log::error($errorMsg);
        app(AuthLogger::class)->error($errorMsg);

        return [];
    }

    public static function pop($registryName)
    {
        $payload = [
            'size' => self::$size,
        ];

        try {
            $request = Http::registry($registryName)->post(Config::get('epp.poll') . '/pop', $payload);
        } catch (Exception $e) {
            throw new FailedRequestException(500, 'Internal Server Error', 'INTERNAL SERVER ERROR');
        }

        return HttpResponse::filled($request);
    }
}
