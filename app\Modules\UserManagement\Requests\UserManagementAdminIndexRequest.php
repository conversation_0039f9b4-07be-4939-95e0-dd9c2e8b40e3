<?php

namespace App\Modules\UserManagement\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserManagementAdminIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'showItems' => ['nullable', 'integer'], 
            'orderBy'   => 
            [
                'nullable',
                'string', 
                Rule::in(
                    [
                        "name:desc",
                        "name:asc",
                        "email:desc",
                        "email:asc",
                        "status:desc",
                        "status:asc",
                        "permissions:desc",
                        "permissions:asc",
                        "createdAt:desc",
                        "createdAt:asc",
                    ]
                )
            ],
            'status'   =>
            [
                'nullable',
                'string',
                Rule::in(
                    [
                        'ACTIVE', 
                        'DISABLED', 
                        'PENDING'
                    ]
                )
            ],
            'name' => ['nullable', 'string'],
            'email' => ['nullable', 'string']
        ];
    }
}
