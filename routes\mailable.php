<?php

use Illuminate\Support\Facades\Route;

Route::get('/mailable', function () {
    $payload = [
        'subject' => 'Registration Access Approved',
        'greeting' => 'Greetings!',
        'body' => 'New IP:************** has been added <NAME_EMAIL>. Click the link below to login.',
        'url' => 'http://**************/login',
        'text' => 'This link will expire after 24 hours.',
        'sender' => config('mail.from.sd_name'),
    ];

    return new App\Mail\RequestConfirmation($payload);
});
