<?php

namespace App\Mail;

use App\Modules\BillingClient\Services\MarketInvoiceService;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class BankTransferPaymentRejectionMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $mailData;

    /**
     * Create a new message instance.
     * 
     * @param array $payload
     */
    public function __construct($payload)
    {
        $this->onConnection('email_jobs');
        $this->onQueue('default');

        $data = $payload;

        $user = DB::client()
            ->table('users')
            ->where('id', '=', $data['clientId'])
            ->select('id', 'email', 'first_name', 'last_name')
            ->first();

        $marketPlaceInvoice = DB::client()->table('market_place_payment_invoices')
            ->where('payment_service_id', '=', $data['paymentServiceId'])
            ->first();

        $marketPlaceInvoiceOrders = MarketInvoiceService::instance()->getMarketPlaceInvoice($marketPlaceInvoice->id, $user->id);

        $data['clientName']  = "{$user->first_name} {$user->last_name}";

        $orders = [];

        foreach ($marketPlaceInvoiceOrders as $invoiceOrders) 
        {
            array_push(
                $orders,
                [
                    "domain"      => $invoiceOrders->name,
                    "price"       => $invoiceOrders->price,
                    "transferFee" => $invoiceOrders->total_domain_amount,
                    "icannFee"    => $invoiceOrders->total_icann_fee,
                    "subTotal"    => $invoiceOrders->gross_amount,
                ]
            );
        }

        $data['totalAmount'] = $marketPlaceInvoice->paid_amount;
        $data['orders'] = $orders;

        $this->mailData = $data;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Bank Transfer Payment Rejected - StrangeDomains',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.BankTransferPaymentRejectionMail',
            with: 
            [
                'mailData' => $this->mailData,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
