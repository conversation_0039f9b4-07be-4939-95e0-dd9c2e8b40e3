<?php

use App\Modules\Admin\Controllers\AdminRegistrationController;
use App\Modules\Admin\Controllers\AdminVerificationController;
use App\Modules\AdminHistory\Controllers\AdminHistoryController;
use Illuminate\Support\Facades\Route;

Route::middleware(
        [
            'guest'
        ]
    )
    ->prefix('admin-registration')
    ->group(
        function () 
        {
            Route::get('setup/{token}', [AdminRegistrationController::class, 'renderSetupForm'])->name('admin-registration.setup');
            Route::post('register/{token}', [AdminRegistrationController::class, 'register'])->name('admin-registration.register');
        }
    );

Route::middleware(
        [
            'auth'
        ]
    )
    ->prefix('admin-verification')
    ->group(
        function ()
        {
            Route::post('password', [AdminVerificationController::class, 'verifyPassword'])->name('admin-verification.password');
        }
    );

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->group(function () {
    Route::get('/admin-logs', [AdminHistoryController::class, 'index'])->name('admin.logs');
});
