<?php

use App\Modules\BankTransfer\Controllers\BankTransferController;
use App\Modules\BankTransfer\Controllers\BankTransferPaymentController;
use App\Modules\BillingClient\Controllers\BillingClientController;

use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'auth.active', 'auth.permission.check'])
    ->prefix('billing')
    ->group(function () 
    {
        Route::prefix('client')
            ->group(
                function () 
                {
                    Route::get('/', [BillingClientController::class, 'index'])->name('billing.client');
                    Route::get('/view/{id}', action: [BillingClientController::class, 'view'])->name('billing.client.view');
                }
            );

        Route::prefix('wire-transfer')
            ->group(
                function () 
                {
                    Route::get('/', [BankTransferController::class, 'index'])->name('billing.wire.transfer');
                    Route::get('/verify/{id}', [BankTransferController::class, 'verifyEdit'])->name('billing.wire.transfer.verify-edit');
                    Route::patch('/verify', [BankTransferController::class, 'verifyUpdate'])->name('billing.wire.transfer.verify-update');
                    Route::patch('/reject', [BankTransferController::class, 'rejectUpdate'])->name('billing.wire.transfer.reject-update');

                    Route::prefix('payment')
                        ->group(
                            function ()
                            {
                                Route::patch('/approve/{id}', [BankTransferPaymentController::class, 'approve'])->name('billing.wire.transfer.payment.approve');
                                Route::patch('/reject/{id}', [BankTransferPaymentController::class, 'reject'])->name('billing.wire.transfer.payment.reject');
                            }
                        );
                }
            );
    }
);
