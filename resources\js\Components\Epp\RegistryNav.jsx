import { useState } from "react";

import NavLink from "@/Components/NavLink";
import { Link } from "@inertiajs/react";
import {
    MdExpandMore,
    MdTune,
    MdExpandLess,
    MdOutlineCorporateFare,
    MdOutlineImportExport,
    MdOutlineBugReport,
} from "react-icons/md";
export default function RegistryNav({ postRouteName }) {
    const currentRoute = route().current() || postRouteName;

    const routes = {
        account: route().current("epp.account") || currentRoute.includes('epp.account'),
        poll: route().current("epp.poll") || currentRoute.includes('epp.poll'),
        log: route().current("epp.log") || currentRoute.includes('epp.log'),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Registries</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("epp.account")}
                active={routes.account}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdOutlineCorporateFare className="text-2xl " />
                    <span className=" text-inherit">Account</span>
                </span>
                
            </NavLink>
            <NavLink
                href={route("epp.poll")}
                active={routes.poll}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdOutlineImportExport className="text-2xl " />
                    <span className=" text-inherit">Poll Request</span>
                </span>
                
            </NavLink>
        </>
    );
}
