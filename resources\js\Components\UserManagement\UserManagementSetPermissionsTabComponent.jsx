//* PACKAGES
import React, {useState, useEffect, useCallback} from 'react'
import { debounce } from 'lodash';

//* ICONS
import { CiSearch } from 'react-icons/ci';
import { FaChevronDown } from 'react-icons/fa';
import { FaChevronUp } from "react-icons/fa6";

//* COMPONENTS
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import TextInput from '@/Components/TextInput';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementSetPermissionsTabComponent(
    {
        //! VARIABLES
        category, 
        categoryPermissions, 

        //! STATES
        stateActiveCategoryTabs,
        stateInputSelectedPermissions,

        //! EVENTS
        handleToggleCategory = () => alert('toggleCategory'), 
        handleCheckPermission = () => alert('checkPermission'),
        handleCheckAllByCategory = () => alert('checkAllByCategory')
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    //... 
    
    //! VARIABLES
    //...

    //! STATES
    const [stateInputSearch, setStateInputSearch] = useState('');
    const [statedebouncedSearch, setStateDebouncedSearch]   = useState('');

    //! FUNCTIONS
    const debouncedSetSearch = useCallback(
        debounce(
            (value) =>
                {
                    setStateDebouncedSearch(value.trim().toLowerCase());
                },
                200
            ),
            []
    );

    //! USE EFFECTS
    useEffect(
        () =>
        {
            return () => {
                debouncedSetSearch.cancel();
            };
        },
        [
            debouncedSetSearch
        ]
    );

    return (
        <div
            className='flex flex-col gap-4 border py-4 px-4 rounded-lg'
        >
            <div
                className='flex justify-between items-center'
            
            >
                <div
                    className='capitalize font-semibold text-lg'
                >
                    {category.name}
                </div>
                <div
                    className='flex items-center gap-8'
                >
                    <div
                        className={`
                                relative
                                hidden lg:block
                        `}
                    >
                        <TextInput
                            type={ "text"}
                            name="search"
                            value={stateInputSearch}
                            placeholder="Search"
                            className="pl-10 pr-4 py-2" 
                            disabled={stateActiveCategoryTabs.includes(category.name) ? false : true}
                            handleChange={
                                (e) =>
                                {
                                    const value = e.target.value;
                                    setStateInputSearch(value);
                                    debouncedSetSearch(value);
                                }
                            }
                        />
                        <div
                            className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none"
                        >
                            <CiSearch className="h-6 w-6"/>
                        </div>
                    </div>
                    <div
                        className='cursor-pointer'
                        onClick={() =>
                            {
                                if (stateActiveCategoryTabs.includes(category.name))
                                {
                                    setStateInputSearch(''); 
                                    setStateDebouncedSearch('');
                                }
                                
                                handleToggleCategory(category.name);
                            }                        
                        }
                    >
                        {
                            stateActiveCategoryTabs.includes(category.name)
                                ?
                                    <FaChevronUp className="h-[17px] w-[17px]" />
                                :
                                    <FaChevronDown className="h-[17px] w-[17px]" />
                        }
                    </div>
                </div>
            </div>

            <div
                className={`
                    ${stateActiveCategoryTabs.includes(category.name) ? 'flex flex-col gap-4' : 'hidden'} 
                `}
            >
                <hr />
                <AppInputCheckboxComponent
                    id={`category_${category.id}`}
                    name={`category_${category.id}`}
                    value={category.id}
                    label={'Select All'}
                    isChecked={
                        categoryPermissions
                            .filter(cp => cp.categoryId === category.id)
                            .every(cp => stateInputSelectedPermissions.includes(cp.permissionId))
                    }
                    handleEventOnChange={(e) => handleCheckAllByCategory(category.id, e.target.checked)}
                />

                <div
                    className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 max-h-96 overflow-y-auto'
                >
                    {/* SELECT ALL ITEMS FROM THIS CATEGORY */}
                    {
                        categoryPermissions
                            .filter(
                                (permission) => 
                                {
                                    if (stateInputSearch.length > 0)
                                    {
                                        return permission.permissionName.toLowerCase().includes(statedebouncedSearch);
                                    }

                                    return true; 
                                }
                            )
                            .map(
                                (categoryPermission, categoryPermissionIndex) =>
                                {
                                    return (
                                        <AppInputCheckboxComponent
                                            key={categoryPermissionIndex}
                                            id={`categoryPermission_${categoryPermission.permissionId}`}
                                            name={`categoryPermission_${categoryPermission.permissionId}`}
                                            value={categoryPermission.permissionId}
                                            label={categoryPermission.permissionName}
                                            isChecked={stateInputSelectedPermissions.includes(categoryPermission.permissionId)}
                                            handleEventOnChange={() => handleCheckPermission(categoryPermission.permissionId)}
                                        />
                                    )
                                }
                            )
                    }
                </div>
            </div>
        </div>
    );
}
