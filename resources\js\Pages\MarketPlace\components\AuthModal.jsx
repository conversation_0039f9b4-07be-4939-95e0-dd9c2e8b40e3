import { evaluate } from '@/Util/AxiosResponseHandler';
import { getEventValue } from '@/Util/TargetInputEvent';
import { router, useForm } from '@inertiajs/react';
import React from 'react'
import { useEffect } from 'react';
import { useState } from 'react'
import { toast } from 'react-toastify';

import { IoMdEye, IoMdEyeOff } from "react-icons/io";

export default function AuthModal(props) {

    const {
        data,
        setData,
        setError,
        errors,
    } = useForm({
        name: "",
        auth: "",
        order_id: ""
    });

    const [isInput, setIsInput] = useState(false);

    const onHandleChange = (event) => {
        setData({ name: props.name, auth: event.target.value, order_id: props.order_id });
    };

    const handleClose = () => {
        setError({message: ''});
        props.setShowApproveModal(false);
    }

    const confirm = async (e) => {
        e.preventDefault();
        if((data.auth.trim()).length <= 0) return;

        setError({message: ''});

        let response = await axios
            .post(route("market_manuals_auth"), data)
            .then((response) => {
                return response;
            })
            .catch((error) => {
                return error.response;
            });

        response = evaluate(response);

        if(response.code == 403) {
            return setError({message: '403: Not Enough Permissions.'});
        } else if (response.errors) {
            return setError({message: response.errors.message});
        } 

        toast.success("Success.");
        props.setShowApproveModal(false);
        window.location.reload();
    }

    useEffect(() => {
        setData((data) => ({ ...data, auth: "", }));
    }, [props.show])

    return (
        <div className={`${props.show ? '' : 'hidden'}  fixed z-10 overflow-y-auto top-0 w-full left-0`} id="approvemodal">
            <div className="flex items-center justify-center min-height-100vh pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity">
                    <div className="absolute inset-0 bg-gray-900 opacity-75" />
                </div>
                <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                <div className="inline-block align-center bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full" role="dialog" aria-modal="true" aria-labelledby="modal-headline">
                    <div className="bg-white px-4 pt-5">
                        <label className="font-medium text-gray-800">Enter Auth Code for Domain: {props.name} </label>
                        <div className='relative'>
                            <input type={isInput ? 'input' : 'password'} name="auth" className="w-full outline-none border border-gray-400 rounded p-2 mt-2 mb-3 pr-12" value={data.auth} onChange={(e) => onHandleChange(e) } />
                            <button onClick={() => { setIsInput(!isInput) }} className='absolute inset-y-0 right-0 pr-3 flex items-center'>
                                { isInput ? <IoMdEye className='h-[23px] w-[23px]'></IoMdEye> : <IoMdEyeOff className='h-[23px] w-[23px]'></IoMdEyeOff> }
                            </button>
                        </div>
                    </div>
                    <h6 className='pl-5 text-sm text-red-500'>{errors ? errors.message : 'hidden'}</h6>
                    <div className="px-4 py-3 pb-4 text-right">
                        <button type="button" className="py-2 px-4 bg-gray-500 text-white rounded hover:bg-gray-700 mr-2" onClick={(e) => { handleClose(e); }}><i className="fas fa-times"></i> Cancel </button>
                        <button disabled={data.auth.trim().length <= 0 ? true : false} type="button" className="py-2 px-4 bg-blue-500 text-white rounded font-medium hover:bg-blue-700 mr-2 transition duration-500 disabled:bg-gray-500 disabled:text-white" onClick={(e) => { confirm(e); }}><i className="fas fa-plus"></i> Confirm </button>
                    </div>
                </div>
            </div>
        </div>
    )
}
