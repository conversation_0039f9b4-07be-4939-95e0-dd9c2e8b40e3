//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { CiMail, CiUnlock, CiLock, CiNoWaitingSign, CiClock2, CiCircleCheck } from 'react-icons/ci';
import { FaGear } from 'react-icons/fa6';
import { TbSettingsQuestion, TbTrash } from 'react-icons/tb';

//* COMPONENTS
import AppInputCheckboxComponent from '@/Components/App/AppInputCheckboxComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';
import AppTableComponent from '@/Components/App/AppTableComponent';
import AppTableRowDropdownActionsComponent from '@/Components/App/AppTableRowDropdownActionsComponent';
import UserManagementAdminTableFilterComponent from '@/Components/UserManagement/UserManagementAdminTableFilterComponent';

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserManagementAdminTable(
    {
        allItems,
        stateTableItems,      
        setStateTableItems,   
        stateSelectedItems,   
        setStateSelectedItems, 
        stateSelectedItem,
        setStateSelectedItem,
        stateIsActiveModalViewPermissions,
        setStateIsActiveModalViewPermissions,
        paginationInfo
    }
)
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();

    //! STATES
    const [stateSelectedDeleteItem, setStateSelectedDeleteItem]                 = useState(null);
    const [stateSelectedActionItem, setStateSelectedActionItem]                 = useState(null);
    const [stateIsActiveModalVerification, setStateIsActiveModalVerification]   = useState(false);
    const [stateVerificationAction, setStateVerificationAction]                 = useState(null);

    //! FUNCTIONS
    function handleCheckAll(e)
    {
        if (e.target.checked == true)
        {
            setStateSelectedItems([]);
            setStateSelectedItems(allItems);
        }
        else 
        {
            setStateSelectedItems([]);
        }
    }

    function handleCheck(e)
    {
        const {value, checked } = e.target;

        if (checked)
        {
            setStateSelectedItems([...stateSelectedItems, parseInt(value)]);
        }
        else
        {
            setStateSelectedItems(stateSelectedItems.filter(item => parseInt(item) != parseInt(value)));
        }
    }

    function checkIfAllRowsSelected(requiredItems, selectedItems)
    {
        return requiredItems.every(p =>
            selectedItems.some(s => s === p)
        );
    }

    async function handleViewPermissions(adminId)
    {
        try 
        {
            const response = await axios.post(route("user-management.admin.view-permissions", { id: adminId }));

            if (response.status == 200)
            {
                setStateSelectedItem(response.data)
                setStateIsActiveModalViewPermissions(true);
            }
        }
        catch (error)
        {
            const response = error.response;
            
            toast.error('Could not fetch admin permissions');
        }
    }

    async function handleViewPermissionsRole(roleId)
    {
        try 
        {
            const response = await axios.post(route("user-management.role.view-permissions", { id: roleId }));

            if (response.status == 200)
            {
                setStateSelectedItem(response.data)
                setStateIsActiveModalViewPermissions(true);
            }
        }
        catch (error)
        {
            const response = error.response;
            
            toast.error('Could not fetch role permissions');
        }
    }

    function handleDelete(adminId)
    {
        router.delete(
            route('user-management.category.delete', {id : adminId}),
            {
                onSuccess: () =>
                {
                    toast.success(
                        'Category Deleted',
                        {
                            autoClose: 5000
                        }
                    )
                    
                    setStateSelectedItems([]);
                    setStateSelectedDeleteItem(null);
                },
                onError: () => toast.error('Something went wrong'),
            }
        );
    }

    function handleActionSubmission()
    {
        switch (stateVerificationAction)
        {
            case 'delete': 
                router.delete(
                    route('user-management.admin.delete', { id: stateSelectedActionItem.adminId ?? null}),
                    {
                        onSuccess: () => 
                        {
                            toast.success('User Deleted');
                        }, 
                        onError: () => 
                        {
                            toast.error('Could not delete User');
                        }
                    }
                );
    
                break; 
            
            case 'resend-invitation':
                router.post(
                    route('user-management.admin.invitation-resend', { id: stateSelectedActionItem.adminId ?? null }),
                    {},
                    {
                        onSuccess: () => 
                        {
                            toast.success('Invitation Mail Resent');
                        }, 
                        onError: () => 
                        {
                            toast.error('Could not resend email');
                        }
                    }
                );
                break; 
            
            case 'enable':
                router.patch(
                    route('user-management.admin.enable', { id: stateSelectedActionItem.adminId ?? null }),
                    {},
                    {
                        onSuccess: () => 
                        {
                            toast.success('User Enabled');
                        }, 
                        onError: () => 
                        {
                            toast.error('Could not enable user');
                        }
                    }
                );
                break; 
            
            case 'disable':
                router.patch(
                    route('user-management.admin.disable', { id: stateSelectedActionItem.adminId ?? null }),
                    {},
                    {
                        onSuccess: () => 
                        {
                            toast.success('User Disabled');
                        }, 
                        onError: () => 
                        {
                            toast.error('Could not disable user');
                        }
                    }
                );
                break; 
        }
    }

    function displayStatus(status)
    {
        const sizeClass = 'h-5 w-5'

        let color = 'text-yellow-500'; 
        let icon = <CiClock2 className={sizeClass} />;

        switch (status)
        {
            case 'PENDING':
                color = 'text-yellow-500';
                icon  = <CiClock2 className={sizeClass} />;
                break; 
            
            case 'ACTIVE':
                color = 'text-green-500'; 
                icon  = <CiCircleCheck className={sizeClass} />;
                break; 
            
            case 'INACTIVE':
                color = 'text-orange-500'; 
                icon  = <CiNoWaitingSign className={sizeClass} />;
                break; 
            
            case 'DISABLED': 
                color = 'text-red-500'; 
                icon  = <CiLock className={sizeClass} />;
                break; 
        }

        return (
            <div
                className={`${color} capitalize flex font-semibold items-center gap-1`}
            >
                <span>{icon}</span>
                <span className=''>{status}</span>
            </div> 
        )
    }

    //! VARIABLES
    const columnHeaders =
        [
            // {
            //     label    : (
            //         <AppInputCheckboxComponent
            //             id='checkAll'
            //             name="checkAll"
            //             value={'all'}
            //             isDisabled={stateTableItems.length == 0}
            //             isChecked={checkIfAllRowsSelected(allItems, stateSelectedItems) && stateTableItems.length != 0}
            //             handleEventOnChange={(e) => handleCheckAll(e)}
            //         />                    
            //     ),
            //     isSortable : false
            // }, 
            {
                label     : 'name',
                isSortable: true,
            }, 
            {
                label     : 'email',
                isSortable: true,
            }, 
            {
                label     : 'assigned role',
                isSortable: true,
            }, 
            {
                label     : 'permissions',
                isSortable: true,
            },
            {
                label     : 'status',
                isSortable: true,
            },
            {
                label     : 'created at',
                isSortable: true,
            },
            {
                label     : <FaGear />,
                isSortable: false,
            },
        ];
    
    const rowActionClass = 'w-6 h-6'; 

    const rowActions = 
    [
        {
            label           : 'update user', 
            icon            : <TbSettingsQuestion className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.admin.edit') && hasPermission('user-management.admin.update'),
            shouldDisplay   : (item) => { return true},
            handleEventClick: (item) =>
            {
                return router.get(
                    route('user-management.admin.edit', {id : item.adminId}),
                )            
            } 
        },
        {
            label           : 'delete user',
            icon            : <TbTrash className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.admin.delete'),
            shouldDisplay   : (item) => { return true},
            handleEventClick: (item) =>
            {
                setStateSelectedActionItem(item);
                setStateVerificationAction('delete');
                setStateIsActiveModalVerification(true);
            } 
        }, 
        {
            label           : 'resend invitation',
            icon            : <CiMail className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.admin.invitation-resend'),
            shouldDisplay   : (item) => item.adminStatus == 'PENDING',
            handleEventClick: (item) =>
            {
                setStateSelectedActionItem(item);
                setStateVerificationAction('resend-invitation');
                setStateIsActiveModalVerification(true);
            } 
        },
        {
            label           : 'disable account',
            icon            : <CiLock className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.admin.disable'),
            shouldDisplay   : (item) => item.adminStatus == 'ACTIVE',
            handleEventClick: (item) =>
            {
                setStateSelectedActionItem(item);
                setStateVerificationAction('disable');
                setStateIsActiveModalVerification(true);
            } 
        }, 
        {
            label           : 'enable account',
            icon            : <CiUnlock className={rowActionClass} />,
            hasAccess       : hasPermission('user-management.admin.enable'),
            shouldDisplay   : (item) => item.adminStatus == 'DISABLED',
            handleEventClick: (item) =>
            {
                setStateSelectedActionItem(item);
                setStateVerificationAction('enable');
                setStateIsActiveModalVerification(true);
            } 
        }
    ];

    const columnRows = stateTableItems.map(
        (row) =>
        {
            return [
                // {
                //     value: (
                //         <AppInputCheckboxComponent
                //             id={row.adminId}
                //             name={`check_${row.adminId}`}
                //             value={row.adminId}
                //             isDisabled={false}
                //             isChecked={stateSelectedItems.includes(row.adminId)}
                //             handleEventOnChange={(e) => handleCheck(e)}
                //         />                    
                //     )
                // }, 
                {
                    value: row.adminName,
                },
                {
                    value: row.adminEmail,
                },
                {
                    value: (
                        hasPermission('user-management.role.view-permissions') && row.roleName != null 
                                ? 
                                    <div
                                        className="text-primary cursor-pointer"
                                        onClick   = {() => { handleViewPermissionsRole(row.roleId) }}
                                    >
                                        {row.roleName}
                                    </div>
                                :
                                    <div
                                        className={`
                                            font-medium 
                                            ${row.roleName == null ? 'text-danger' : ''}
                                        `}
                                    >
                                        {row.roleName ?? 'N/A'}
                                    </div>
                        )
                },
                {
                    value: (
                        hasPermission('user-management.admin.view-permissions') 
                            ? 
                                <div
                                    className="text-primary underline cursor-pointer"
                                    onClick   = {() => { handleViewPermissions(row.adminId) }}
                                >
                                    {row.permissionCount}
                                </div>
                            :
                                <div
                                    className=""
                                >
                                    {row.permissionCount}
                                </div>
                    )
                },
                {
                    value: displayStatus(row.adminStatus),
                },
                {
                    value: row.adminCreatedAt,
                },
                {
                    value: <AppTableRowDropdownActionsComponent row={row} rowActions={rowActions}/>
                }
            ]
        }
    ); 

    return (
        <>
            {/* <div
                className='flex gap-2'
            >
                {
                    stateSelectedItems.map(
                        (item, index) =>
                        {
                            return(
                                <span
                                    key={index}
                                    className='text-sm'
                                >
                                    {item}
                                </span>
                            )
                        }
                    )
                }
            </div> */}

            <AppPromptPasswordVerificationComponent
                show={stateIsActiveModalVerification}
                onSubmitSuccess={handleActionSubmission}
                onClose={() => 
                    {
                        setStateSelectedItem(null);
                        setStateSelectedActionItem(null); 
                        setStateVerificationAction(null);
                        setStateIsActiveModalVerification(false);
                    }
                }
            />

            <AppTableComponent
                pageRoute={'user-management.admin'}
                tableName={'users'}
                columnHeaders={columnHeaders}
                columnRows={columnRows}
                paginationInfo={paginationInfo}
                shouldPreserveState={true}
                filterComponent={<UserManagementAdminTableFilterComponent pageRoute={'user-management.admin'}/>}
            />
        </>
    );
}
