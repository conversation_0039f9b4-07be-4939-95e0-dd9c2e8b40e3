import React from "react";
import Modal from "@/Components/Modal";
import SecondaryButton from '@/Components/SecondaryButton';
import DangerButton from '@/Components/DangerButton';

const ConfirmationModal = ({ isOpen, onClose, onConfirm, action }) => {
  if (!isOpen) return null;

  const getTitle = () => {
    if (action === 'update') return 'Hold Client Domain';
    if (action === 'remove') return 'Unhold Client Domain';
    return 'Confirm Action';
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <div className="p-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
            {getTitle()}
        </h2>

        <p className="text-base text-gray-600 leading-relaxed">
            {action === 'update' 
                ? 'Are you sure you want to put the selected domain(s) on client hold? This will prevent the domain(s) from being transferred or modified.'
                : 'Are you sure you want to remove the client hold from the selected domain(s)? This will allow the domain(s) to be transferred and modified again.'
            }
        </p>
        <div className="mt-8 flex justify-end gap-4">
            <SecondaryButton 
                onClick={onClose}
                className="px-4 py-2 text-sm"
            >
                Cancel
            </SecondaryButton>
            <DangerButton 
                onClick={onConfirm}
                className="px-4 py-2 text-sm"
            >
                Confirm
            </DangerButton>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmationModal;