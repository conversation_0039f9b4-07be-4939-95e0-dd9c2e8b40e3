<?php

namespace App\Modules\Client\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Client\Services\InviteUserService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class InviteUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust if needed
    }

    public function rules(): array
    {
        return [
            'email' => [
            'required',
            'email:rfc,dns',
                function ($attribute, $value, $fail) {
                    if (InviteUserService::checkIfEmailExists($value)) {
                        $fail("This $attribute address has already been invited.");
                    }
                }
            ], 
            'disable_verification' => 'nullable|boolean',
            'disable_deposit' => 'nullable|boolean',
            'balance' => 'nullable|numeric|gt:0',
        ];
    }

    public function handleInvite($dataRequest)
    {
        return InviteUserService::sendInvitation($dataRequest);
    }

    public function failed()
    {
        // throw new FailedRequestException(400, 'This email is already registered in the system or not enough system credit', 'Unable to send invitation.');
        throw ValidationException::withMessages([
            'default_balance' => ['The system credit is not sufficient to send an invitation.'],
        ]);
    }

    public function messages(): array
    {
        return [
            'email.required' => 'The email field is required.',
            'email.dns' => 'The email domain does not appear to exist or accept emails.',
        ];
    }

}
