//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdExpandLess,
    MdExpandMore,
    MdSupervisedUserCircle,
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationSystemLogComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        systemLogs       : route().current("system.logs")
    };

    const links = 
    [
        {
            routeName: 'system.logs',
            hasAccess: hasPermission('system.logs'),
            isActive : routes.systemLogs,
            label    : 'system logs'
        }, 
    ];

    //! STATES
    //...

    //! FUNCTIONS

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
