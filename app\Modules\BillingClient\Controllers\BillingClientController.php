<?php

namespace App\Modules\BillingClient\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BillingClient\Requests\ShowListRequest;
use App\Modules\BillingClient\Services\BillingClientService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class Billing<PERSON>lientController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('Billing/Client/Index', $request->index());
    }

    public function view(Request $request)
    {
        return BillingClientService::instance()->getView($request);
    }
}
