<?php

namespace App\Modules\Ip\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class IpService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function get($request)
    {
        $builder = DB::client()->table('ips');
        self::whenHasActive($builder, $request);
        self::whenHasOrderby($builder, $request);

        $builder = $builder->paginate(self::$pageLimit);

        return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    private static function whenHasActive(&$builder, $request)
    {
        $builder->when($request->has('is_active'), function (Builder $query) use ($request) {
            $query->where('is_active', $request->is_active);
        });
    }

    private static function whenHasOrderby(&$builder, $request)
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'created':
                        $query->orderBy('id', $orderby[1]);
                        break;
                    case 'name':
                        $query->orderBy('ip', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('id', 'desc');
                }
            } else {
                $query->orderBy('id', 'desc');
            }
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('id', 'desc');
            });
    }

    private static function paramToURI($request)
    {
        $param = [];

        if ($request->has('is_active')) {
            $param[] = 'is_active='.$request->is_active;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }

        return $param;
    }

    public static function updateStatus(array $ids, $is_active)
    {
        DB::client()->table('ips')->whereIn('id', $ids)->update(['is_active' => $is_active, 'updated_at' => now()]);
        app(AuthLogger::class)->info('update ip id'.implode(',', $ids).' to status '.$is_active);
    }
}
