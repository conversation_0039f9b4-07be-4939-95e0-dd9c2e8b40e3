const getRedemptionDays = (deletedAt) => {
    if (!deletedAt) {
        return "N/A";
    }

    const deletedDate = new Date(deletedAt);
    const currentDate = new Date();
    
    const diffInMs = currentDate.getTime() - deletedDate.getTime();
    
    const daysInRedemption = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
    
    if (daysInRedemption < 0) {
        return "N/A";
    }
    
    if (daysInRedemption === 0) {
        return "Today (Day 1)";
    }
    
    const dayText = daysInRedemption === 1 ? "day" : "days";
    
    if (daysInRedemption > 30) {
        return `${daysInRedemption} ${dayText} (Expired)`;
    } else if (daysInRedemption > 25) {
        return `${daysInRedemption} ${dayText} (Warning)`;
    }
    
    return `${daysInRedemption} ${dayText}`;
};

export default getRedemptionDays;
