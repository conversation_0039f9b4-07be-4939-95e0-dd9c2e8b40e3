import React from 'react';

export default function EmailHeader() {
    const headerStyle = {
        boxSizing: 'border-box',
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
        position: 'relative',
        padding: '0.75rem',
        backgroundColor: '#3490dc',
        borderRadius: '0.25rem',
        color: 'white',
        fontWeight: 'bold'
    };

    const domainStyle = {
        boxSizing: 'border-box',
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
        position: 'relative',
        marginTop: '0.5rem',
        fontSize: '0.8rem',
        fontWeight: 'bold'
    };

    return (
        <div className="header bg-slate-100 text-center py-6">
            <a href="/" className="inline-block">
                <div style={headerStyle}>
                    <span className="text-xl">SD</span>
                </div>
            </a>
            <div className="text-slate-500" style={domainStyle}>
                STRANGEDOMAINS
            </div>
        </div>
    );
}
