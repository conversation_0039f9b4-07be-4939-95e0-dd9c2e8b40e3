<?php

namespace App\Modules\BillingClient\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\BillingClient\Constants\PaymentNodeStatus;
use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;

class MarketInvoiceService
{
    use CursorPaginate;

    private $defaultpageLimit = 20;

    public static function instance(): self
    {
        $marketInvoiceService = new self;

        return $marketInvoiceService;
    }

    public function getPaymentReimbursement(int $id, int $userId): array
    {
        $refund = [];
        $charge = [];
        $intent = [];

        if (! $id) {
            return [];
        }

        $reimbursement = $this->getReimbursementById($id, $userId);

        $data = [
            'reimbursement' => $reimbursement,
            'charge' => [],
            'refund' => [],
            'status' => PaymentNodeStatus::REFUNDED,
        ];

        return $data;
    }

    public function getMarketPlaceInvoice(int $invoiceId, int $userId)
    {

        $data = DB::client()->table('market_place_payment_invoices')
            ->join('market_place_node_invoices', 'market_place_node_invoices.marketplace_payment_invoice_id', '=', 'market_place_payment_invoices.id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('payment_services', 'payment_services.id', '=', 'market_place_payment_invoices.payment_service_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->where('market_place_payment_invoices.id', $invoiceId)
            ->where('market_place_domains.user_id', $userId)
            ->select(
                'market_place_payment_invoices.total_amount as invoice_total_amount',
                'market_place_payment_invoices.paid_amount as invoice_paid_amount',
                'market_place_payment_invoices.status as invoice_status',
                'market_place_payment_invoices.total_payment_node',
                'market_place_payment_invoices.created_at',
                'market_place_node_invoices.marketplace_payment_node_id',
                'market_place_node_invoices.marketplace_payment_invoice_id',
                'market_place_domains.registered_domain_id',
                'market_place_domains.order_id',
                'market_place_domains.total_amount',
                DB::raw('market_place_domains.total_domain_amount + market_place_domains.total_icann_fee as node_total_amount'), // registry balance
                'market_place_domains.total_amount as gross_amount', // from old code
                'market_place_domains.total_amount as rate', // from old code
                'market_place_domains.status as node_status',
                'market_place_domains.total_domain_amount',
                'market_place_domains.vendor',
                'market_place_domains.total_icann_fee',
                'market_place_domains.price',
                'domains.id as domain_id',
                'domains.name as name',
                'domains.year_length',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'users.email',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.system_credit_id',
                'payment_services.bank_transfer_id',
            )
            ->get()->all();

        // $data[0]->node_type = PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_INVOICE];
        $data[0]->summary_type = PaymentSummaryType::MARKETPLACE_INVOICE;

        return $data;
    }

    public function getReimbursementById(int $id, int $userId): object
    {
        $reimbursement = DB::client()->table('market_place_reimbursements as pr')
            ->join('market_place_node_invoices', 'market_place_node_invoices.id', '=', 'pr.marketplace_node_invoice_id')
            ->join('market_place_payment_invoices as pi', 'pi.id', '=', 'market_place_node_invoices.marketplace_payment_invoice_id')
            ->join('market_place_domains', 'market_place_domains.id', '=', 'market_place_node_invoices.marketplace_payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'market_place_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('payment_services', 'payment_services.id', '=', 'pr.payment_service_id')
            ->join('users', 'users.id', '=', 'payment_services.user_id')
            ->where('pr.id', $id)
            ->where('payment_services.user_id', $userId)
            ->select(
                'pr.id',
                'pr.marketplace_node_invoice_id as payment_node_invoice_id',
                'pr.total_amount',
                'pr.total_amount as current_balance',
                'pr.status as reimbursement_status',
                'pr.payment_service_id as payment_service_id',
                'pr.created_at',
                'payment_services.user_id as user_id',
                'pi.id as payment_invoice_id',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.extension_id',
                'registered_domains.status as registered_domain_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'domains.name',
                'domains.root',
                'domains.registrant',
                'domains.expiry',
                'domains.contacts',
            )->get()->first();

        if (! $reimbursement) {
            throw new FailedRequestException(403, 'This action is not authorized.', 'Unauthorized');
        }

        $reimbursement->node_type = PaymentSummaryType::TEXT[PaymentSummaryType::MARKETPLACE_REIMBURSEMENT];

        return $reimbursement;
    }
}
