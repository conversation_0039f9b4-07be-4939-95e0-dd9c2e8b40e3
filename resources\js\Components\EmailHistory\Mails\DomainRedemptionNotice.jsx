import React from "react";

export default function({ emailBody, links }) {
    const parsedData = JSON.parse(emailBody);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'", color: "#1a202c" }}>
            <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full text-slate-500">
                <p className="mb-4 font-bold">{parsedData.greeting}</p>

                <p className="mb-4">
                    {parsedData.message} 
                </p>

                <div className="mb-4">
                    <p className="font-bold">Domain</p>
                </div>

                <p className="mb-4 text-blue-600">
                    {parsedData.domains}
                </p>

                <p className="mb-4">
                    {parsedData.textA} 
                </p>

                <p className="mb-6">
                    {parsedData.textB}
                </p>

                <div className="mb-4">
                    <p className="font-bold">Sincerely,</p>
                    <br />
                    <p className="font-bold">{parsedData.sender}</p>
                </div>
            </div>
        </div>
    );
}
