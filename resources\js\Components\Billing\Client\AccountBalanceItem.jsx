import { MdAppRegistration, MdAssignmentReturn } from "react-icons/md";


const STATUS_REFUND = 'REFUNDED';
const STATUS_REFUND_REQUESTED = 'Refund Requested';
const convertNameFormat = (str) => {
    str = str.toLowerCase();
    const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
    return words.join(' ');
}

const formatAmount = (amount) => {
    return '$'+ amount ? '$' + parseFloat(amount).toFixed(2) : "0.00";
}

const getSource = (item) => {
    if (item.bank_transfer_id) {
        return 'Bank Transfer';
    } else if (item.stripe_id) {
        return 'Payment Methods';
    } else if (item.system_credit_id) {
        return 'System Credit';
    } else {
        return 'Unknown';
    }
}

export default function AccountBalanceItem({ item }) {
    return (
        <div className={`" space-y-2" }`}>
            <div className="flex flex-col text-lg text-gray-700 font-semibold">
                <div className="flex items-center justify-between">
                    <span className=" text-inherit">{item.summary_name ?? 'Account Balance Invoice'}</span>
                </div>
                {(item.account_credit_id) &&
                    <>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Running Balance</span>
                            <span className=" text-inherit">{formatAmount(item.running_balance)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Type</span>
                            <span className=" text-inherit">{item.account_credit_type}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Source</span>
                            <span className=" text-inherit">{getSource(item)}</span>
                        </div>
                    </>
                }

                {(!item.account_credit_id && item.bank_transfer_id) &&
                    <>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Account Name</span>
                            <span className=" text-inherit">{item.account_name}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Company</span>
                            <span className=" text-inherit">{item.company}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Status</span>
                            <span className=" text-inherit">{item.verified_at ? 'Verified' : 'Pending'}</span>
                        </div>
                    </>
                }

                {(!item.account_credit_id && item.stripe_id) &&
                    <>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Service Fee</span>
                            <span className=" text-inherit">{formatAmount(item.service_fee)}</span>
                        </div>
                    </>
                }

                {(!item.account_credit_id && item.system_credit_id) &&
                    <>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Amount</span>
                            <span className=" text-inherit">{formatAmount(item.amount)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                            <span className=" text-inherit">Note</span>
                            <span className=" text-inherit">{item.note ?? 'None'}</span>
                        </div>
                    </>
                }

            </div>
        </div>
    );
}
