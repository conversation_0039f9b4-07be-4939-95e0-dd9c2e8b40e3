<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('afternic_sale_audits', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('total_commission');
            $table->bigInteger('total_price');
            $table->timestamp('order_started_at');
            $table->timestamp('order_ended_at');
            $table->string('confirm_code');
            $table->string('status');
            $table->timestamp('audited_at')->useCurrent();
            $table->integer('total_domain');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('afternic_sale_audits');
    }
};
