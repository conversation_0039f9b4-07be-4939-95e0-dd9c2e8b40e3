<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('schedule_notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('public.users');
            $table->string('title');
            $table->text('message');
            $table->string('link_name');
            $table->string('redirect_url');
            $table->timestamp('read_at')->nullable();
            $table->string('type');
            $table->string('status');
            $table->string('schedule_type');
            $table->time('time');
            $table->date('start_date');
            $table->integer('min_registration_period')->nullable();
            $table->integer('max_registration_period')->nullable();
            $table->timestamp('expiration')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('schedule_notifications');
    }
};
