<?php

namespace App\Modules\CustomLogger\Services;

use Illuminate\Support\Facades\Log;

class LocalLogger implements CustomLoggerInterface
{
    private $channel = 'stack';

    public function __construct(string $channel = 'stack')
    {
        $this->channel = $channel;
    }

    public function info($message)
    {
        Log::channel($this->channel)->info($message);
    }

    public function error($message)
    {
        Log::channel($this->channel)->error($message);
    }
}
