import SecondaryLink from "@/Components/SecondaryLink";
import { MdArrowBackIos, MdArrowForwardIos } from "react-icons/md";

export default function CursorPaginate({
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
    shouldPreserveState = false
}) {
    return (
        <div className="flex justify-between">
            <div className="flex space-x-5 pl-3">
                {(itemCount > 0 && total > 0) &&
                    <span className="text-sm text-gray-600">
                        {itemCount} of {total} {itemCount > 1 ? itemName + "s" : itemName}
                    </span>
                }
            </div>
            <div>
                <SecondaryLink
                    href={previousPageUrl}
                    processing={onFirstPage}
                    shouldPreserveState={shouldPreserveState}
                >
                    <MdArrowBackIos />
                </SecondaryLink>
                <SecondaryLink
                    href={nextPageUrl}
                    processing={onLastPage}
                    shouldPreserveState={shouldPreserveState}
                >
                    <MdArrowForwardIos />
                </SecondaryLink>
            </div>
        </div>
    );
}
