<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Services\SystemLogService;
use App\Modules\Client\Requests\SystemLogRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SystemLogController extends Controller
{
    protected $systemLogService;

    public function __construct(SystemLogService $systemLogService)
    {
        $this->systemLogService = $systemLogService;
    }

    /**
     * Display the system logs page.
     *
     * @param SystemLogRequest $request
     * @return \Inertia\Response
     */
    public function index(SystemLogRequest $request)
    {
        return Inertia::render('SystemLogs/Index', 
            $this->systemLogService->prepareSystemLogView($request)
        );
    }
}
