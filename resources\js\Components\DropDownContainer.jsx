import React from "react";

export default function DropDownContainer({
    children,
    show = true,
    className = "",
}) {
    if (className == "") {
        className = " right-0 ";
    }
    return (
        <div
            className={` z-10 absolute  py-2 border bg-white border-gray-100  rounded-sm shadow-md flex flex-col text-base cursor-pointer w-max  ${
                show || "hidden"
            } ${className} `}
        >
            {children}
        </div>
    );
}
