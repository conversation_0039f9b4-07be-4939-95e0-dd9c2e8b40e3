<?php

namespace App\Modules\BankTransfer\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BankTransfer\Requests\BankTransferPaymentApproveRequest;
use App\Modules\BankTransfer\Requests\BankTransferPaymentRejectRequest;
use App\Modules\BankTransfer\Services\BankTransferPaymentService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BankTransferPaymentController extends Controller
{
    public function approve(BankTransferPaymentApproveRequest $request, int $id)
    {
        (new BankTransferPaymentService())->approve(
            $request->only('paidAmount', 'note', 'purpose'), 
            $id
        );

        return redirect()->route('billing.wire.transfer')
            ->with('successMessage', 'Bank Transfer Payment Approved');
    }

    public function reject(BankTransferPaymentRejectRequest $request, int $id)
    {
        (new BankTransferPaymentService())->reject(
            $request->only('paidAmount', 'note', 'purpose'),
            $id
        );

        return redirect()->route('billing.wire.transfer')
            ->with('successMessage', 'Bank Transfer Payment Rejected');
    }
}
