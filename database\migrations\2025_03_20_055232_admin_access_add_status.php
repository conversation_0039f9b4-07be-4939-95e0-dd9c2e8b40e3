<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // check new columns
        if(Schema::hasColumn('admin_access', 'status')) {
            echo 'Column "status" of relation "admin_access" already exists...'.PHP_EOL;

            return;
        }

        Schema::table('admin_access', function (Blueprint $table) {
            $table->string('status')->default('pending')->after('access_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
