<?php

namespace App\Modules\DomainRedemption\Services;

use AdminHistoryType;
use App\Events\AdminActionEvent;
use App\Modules\DomainRedemption\Jobs\DomainRestoreApprovedJob;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Modules\AdminHistory\Constants\HistoryType;
use Carbon\Carbon;

class DomainRedemptionService
{

    public function __construct(
        private readonly DatabaseQueryService $queryService = new DatabaseQueryService()
    ) {}

    public static function instance(): self
    {
        return new self();
    }

}