
//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { MdKeyboardBackspace } from 'react-icons/md';

//* COMPONENTS
import AdminLayout from '@/Layouts/AdminLayout'

//* PARTIALS
import PartialUserManagementRoleFormSetPermissions from './Partials/PartialUserManagementRoleFormSetPermissions';

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementRoleCreate(props)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    const allPermissions      = props.allPermissions;
    const activePermissions   = props.activePermissions;
    const categories          = props.categories;
    const categoryPermissions = props.categoryPermissions

    //! STATES
    const [stateInputName, setStateInputName]                               = useState('');
    const [stateInputSelectedPermissions, setStateInputSelectedPermissions] = useState([]);
    const [stateErrorMessageName, setStateErrorMessageName]                 = useState(null);
    const [stateErrorMessagePermissions, setStateErrorMessagePermissions]   = useState(null);
    
    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleSubmit()
    {
        setStateErrorMessageName(null)
        setStateErrorMessagePermissions(null)
    
        router.post(
                route("user-management.role.store"),
            {
                name       : stateInputName,
                permissions: stateInputSelectedPermissions
            }, 
            {
                onError: (error) => 
                {

                    setStateErrorMessageName(error.name ?? null)
                    setStateErrorMessagePermissions(error.permissions ?? null)
                }
            }
        )
    }

    return (
        <AdminLayout>
            <div
                className="mx-auto container max-w-[1200px] flex flex-col gap-4 rounded-lg"
            >
                <div
                    className="flex items-center text-lg font-semibold"
                >
                    <Link
                        href={route("user-management.role")}
                        className=" hover:!shadow-none flex pl-0"
                    >
                        <MdKeyboardBackspace className="text-3xl rounded-full p-1 cursor-pointer hover:text-gray-700 text-gray-700" />
                        <span className="hover:text-gray-700 text-gray-700 pt-[1px] pl-1"> Back to Roles </span>
                    </Link>
                </div>

                <div
                    className="header"
                >
                    <div
                        className='text-2xl font-semibold'
                    >
                        Create Role
                    </div>
                </div>

                <PartialUserManagementRoleFormSetPermissions
                    categories={categories}
                    activePermissions={activePermissions}
                    allPermissions={allPermissions}
                    categoryPermissions={categoryPermissions}
                    stateInputName={stateInputName}
                    setStateInputName={setStateInputName}
                    stateInputSelectedPermissions={stateInputSelectedPermissions}
                    setStateInputSelectedPermissions={setStateInputSelectedPermissions}
                    stateErrorMessageName={stateErrorMessageName}
                    stateErrorMessagePermissions={stateErrorMessagePermissions}
                    handleEventSubmit={handleSubmit}
                />
            </div>
        </AdminLayout>
    )
}
