<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AdminActionEvent
{
    use Dispatchable, SerializesModels;

    public int $adminId;
    public string $type;
    public string $message;

    public function __construct(int $adminId, string $type, string $message)
    {
        $this->adminId = $adminId;
        $this->type = $type;
        $this->message = $message;
    }
}
