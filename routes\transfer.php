<?php

use Illuminate\Support\Facades\Route;
use App\Modules\Transfer\Controllers\TransferController;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('transfer')->group(function () {
    Route::get('/', [TransferController::class, 'index'])->name('transfer.view');
    Route::post('/outbound/{action}/{ids}', [TransferController::class, 'sendResponse'])->name('transfer.outbound.response');
});
