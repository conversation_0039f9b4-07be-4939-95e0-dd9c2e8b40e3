import { useState } from "react";

import NavLink from "@/Components/NavLink";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>d<PERSON>xpand<PERSON>ess,
    MdExpandMore,
    MdOutlineConstruction,
    MdPieChartOutlined,
    MdPercent,
    MdSettings,
    MdNotificationAdd,
} from "react-icons/md";
export default function SettingNav({ postRouteName }) {
    const routes = {
        general_setting: route().current("setting.general"),
        fee_setting: route().current("setting.fee"),
        extension_fee: route().current("setting.extension.fee"),
        notification_management: route().current("notification.management"),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Settings</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("setting.general")}
                active={routes.general_setting}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdOutlineConstruction className="text-2xl " />
                    <span className=" text-inherit">General</span>
                </span>
            </NavLink>
            <NavLink
                href={route("setting.fee")}
                active={routes.fee_setting}
                className={visible()}
            >   
                <span className="flex space-x-4">
                    <MdAttachMoney className="text-2xl " />
                    <span className=" text-inherit">Fees</span>
                </span>
            </NavLink>
            <NavLink
                href={route("setting.extension.fee", {
                    extension: "com",
                })}
                active={routes.extension_fee}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdAttachMoney className="text-2xl " />
                    <span className=" text-inherit">Extension Fees</span>
                </span>
            </NavLink>
            <NavLink
                href={route('notification.management')}
                active={routes.notification_management}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdNotificationAdd className="text-2xl " />
                    <span className="text-inherit text-md">General Notification</span>
                </span>
            </NavLink>
            {/* <NavLink
                href={route("setting.commission")}
                active={routes.commission_setting}
                Icon={<MdPercent className="text-2xl " />}
                className={visible()}
            >
                <span className=" text-inherit">Commissions</span>
            </NavLink> */}
        </>
    );
}
