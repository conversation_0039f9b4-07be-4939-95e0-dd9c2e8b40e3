import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";
import { getEventValue } from "@/Util/TargetInputEvent";
import useOutsideClick from "@/Util/useOutsideClick";
import { Link } from "@inertiajs/react";
import { useRef, useState } from "react";
import { MdMoreVert } from "react-icons/md";
import "react-toastify/dist/ReactToastify.css";

export default function Item({
    item,
    source,
    connection,
    isSelected,
    onCheckboxChange,
    handleRetry,
    handleForget,
}) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, getEventValue(e));
    };

    return (
        <tr className="hover:bg-gray-100" key={"queue-" + item.id}>
            <td className="px-2">
                <label className="flex items-center pl-2 space-x-5 ">
                    <Checkbox
                        className="cursor-pointer"
                        name="request"
                        value="request"
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    />
                    <Link
                        as="button"
                        href={route("job.failed-queue", { id: item.id, source, connection })}
                    >
                        <span className="text-link">{item.uuid}</span>
                    </Link>
                </label>
            </td>
            <td><span>{item.connection}</span></td>
            <td><span>{item.queue}</span></td>
            <td><span>{item.failed_at}</span></td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <button
                            className="hover:bg-gray-100 px-5 py-1 "
                            onClick={() => handleRetry(item.uuid)}
                        >
                            Retry
                        </button>

                        <button
                            className="hover:bg-gray-100 px-5 py-1 "
                            onClick={() => handleForget(item.uuid)}
                        >
                            Forget
                        </button>
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
