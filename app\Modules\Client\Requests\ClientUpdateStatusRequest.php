<?php

namespace App\Modules\Client\Requests;

use App\Modules\Client\Services\ClientService;
use Illuminate\Foundation\Http\FormRequest;

class ClientUpdateStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'ids' => ['required', 'array'],
            'status' => ['required', 'boolean'],
        ];
    }

    public function updateStatus()
    {
        ClientService::update($this->ids, $this->status, $this->is_invited);
    }
}
