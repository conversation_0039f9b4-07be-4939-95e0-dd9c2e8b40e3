<?php

namespace App\Modules\Client\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DomainLogRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => 'nullable|string',
            'date' => 'nullable|string|in:today,yesterday,last 7 days,last 30 days',
            'domain' => 'nullable|string'
        ];
    }

    public function filters(): array
    {
        return array_filter($this->only([
            'type',
            'date',
            'domain'
        ]));
    }
} 