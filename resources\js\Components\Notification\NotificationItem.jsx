import React from 'react'
import { useState } from 'react';
import { Link } from "@inertiajs/react";
import getRecentTime from '@/Util/getRecentTime';

const NotificationItem = ({ item }) => {
    const [more, setMore] = useState(false);

    return (
        <Link
            href={route('notification.update.read', {id : item.id})}
            method="patch"
            as="button"
        >
            <div className={`flex w-full justify-center rounded-lg gap-6 py-4 ${item.read_at ? 'hover:bg-gray-100' : 'bg-gray-100'}`}>
                <span className='rounded-full h-16 w-16 bg-gray-200'></span>
                <span className={`w-3/4 ${item.read_at && 'text-gray-400'} mt-1`}>
                    <div className='flex items-center gap-2 mb-1'>
                        <span className='font-bold'>{item.title}</span>
                        <span className={`text-[.4rem] ${item.read_at && 'opacity-40'}`}>&#9899;</span>
                        <span className='text-sm'>{getRecentTime(item.created_at, true)}</span>
                    </div>
                    <div className={`${more || 'flex justify-start'} text-left`}>
                        {item.message.length > 60 ? (
                            <>
                                <span className={`${more || "truncate"} w-full leading-5`}>
                                        {item.message}
                                    </span>
                                    <span
                                        className={`text-link ${more && 'flex justify-start'}`}
                                        onClick={(event) => {
                                            event.stopPropagation();
                                            setMore(!more);
                                        }}
                                    >
                                        {more ? "show less" : "more"}
                                </span>
                            </>
                        ) : (
                            <div className='w-full text-justify leading-5'>{item.message}</div>
                        )}
                    </div>
                </span>
            </div>
        </Link>
    )
}
 
export default NotificationItem;