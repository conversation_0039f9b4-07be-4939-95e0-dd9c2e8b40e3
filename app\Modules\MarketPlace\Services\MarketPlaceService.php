<?php

namespace App\Modules\MarketPlace\Services;

use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Mail\AuditMail;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class MarketPlaceService
{
    public static function instance()
    {
        $marketplaceservice = new self;

        return $marketplaceservice;
    }

    public function getDomains() : \Illuminate\Support\Collection
    {
        return DB::table('public.market_place_domains AS mpd')
        ->join('public.registered_domains AS rd', 'mpd.registered_domain_id', '=', 'rd.id')
        ->join('public.domains AS d', 'rd.domain_id', '=', 'd.id')
        ->join('public.users AS u', 'mpd.user_id', '=', 'u.id')
        ->select('d.id AS domain_id', 'rd.id AS reg_domain_id', 'mpd.id AS market_domain_id', 'u.first_name', 'u.last_name', 'mpd.status', 'd.name AS domain', 'mpd.total_amount as price')
        ->get();
    }

    public function setDomainStatus($id, $status) : void
    {
        DB::table('public.market_place_domains')
        ->where('id', $id)
        ->update(['status' => $status]);
    }
}