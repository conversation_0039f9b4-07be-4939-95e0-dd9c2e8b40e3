import { useState } from 'react';
import { router } from '@inertiajs/react';
import { toast } from 'react-toastify';

export function useTransferActions() {
    const [isProcessing, setIsProcessing] = useState(false);

    const handleSingleTransferAction = (transfer, action, onSuccess, onError) => {
        if (isProcessing) return;

        setIsProcessing(true);

        router.post(
            route("transfer.outbound.response", { action, ids: transfer.id }),
            {},
            {
                onSuccess: () => {
                    toast.success(`Transfer ${action}d for ${transfer.domain}`);
                    setIsProcessing(false);
                    if (onSuccess) onSuccess(transfer, action);
                },
                onError: (errors) => {
                    toast.error(`Failed to ${action} transfer`);
                    console.error(errors);
                    setIsProcessing(false);
                    if (onError) onError(errors, transfer, action);
                },
            }
        );
    };

    const handleBulkTransferAction = (selectedIds, action, onSuccess, onError) => {
        if (isProcessing || selectedIds.length === 0) return;

        setIsProcessing(true);
        const ids = selectedIds.join(",");

        router.post(
            route("transfer.outbound.response", { action, ids }),
            {},
            {
                onSuccess: () => {
                    toast.success(`${selectedIds.length} transfers ${action}d successfully`);
                    setIsProcessing(false);
                    if (onSuccess) onSuccess(selectedIds, action);
                },
                onError: (errors) => {
                    toast.error(`Failed to ${action} selected transfers`);
                    console.error(errors);
                    setIsProcessing(false);
                    if (onError) onError(errors, selectedIds, action);
                },
            }
        );
    };

    const approveTransfer = (transfer, onSuccess, onError) => {
        handleSingleTransferAction(transfer, 'approve', onSuccess, onError);
    };

    const rejectTransfer = (transfer, onSuccess, onError) => {
        handleSingleTransferAction(transfer, 'reject', onSuccess, onError);
    };

    const approveBulkTransfers = (selectedIds, onSuccess, onError) => {
        handleBulkTransferAction(selectedIds, 'approve', onSuccess, onError);
    };

    const rejectBulkTransfers = (selectedIds, onSuccess, onError) => {
        handleBulkTransferAction(selectedIds, 'reject', onSuccess, onError);
    };

    return {
        isProcessing,
        approveTransfer,
        rejectTransfer,
        approveBulkTransfers,
        rejectBulkTransfers,
        handleSingleTransferAction,
        handleBulkTransferAction,
    };
}
