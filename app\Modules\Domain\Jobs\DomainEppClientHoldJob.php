<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\Domain\Services\DomainEppClientHoldService;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DomainEppClientHoldJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private int $domainId;
    private string $domainName;
    private string $action;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Get the queue connection for the job
     */
    public function __construct(int $domainId, string $domainName, string $action)
    {
        $this->domainId = $domainId;
        $this->domainName = $domainName;
        $this->action = $action;

        $registry = strtoupper(DomainParser::getRegistryName($domainName));
        if ($registry) {
            $this->onQueue("{$registry}-CLIENT-HOLD");
        }
    }

    public function handle(): void
    {
        DomainEppClientHoldService::instance()->updateEppStatus(
            $this->domainId,
            $this->domainName,
            $this->action
        );
    }
} 