import React from 'react';
import { BsDatabaseFillLock } from "react-icons/bs";
import {
    MdOutlineLock,
    MdLockO<PERSON>,
    MdInfoOutline,
    MdOutlineEditOff,
    MdOutlineEdit,
    MdOutlineFlag,
    MdPauseCircleOutline
} from "react-icons/md";
import { TbTrashOff, TbTrash } from "react-icons/tb";

const STATUS_TYPES = {
    TRANSFER: 'clientTransferProhibited',
    UPDATE: 'clientUpdateProhibited',
    HOLD: 'clientHold',
    DELETE: 'clientDeleteProhibited',
    RENEW: 'clientRenewProhibited'
};


const parseClientStatus = (clientStatus) => {
    if (!clientStatus) return [];
    if (Array.isArray(clientStatus)) return clientStatus;
    if (typeof clientStatus === 'string') {
        try {
            return JSON.parse(clientStatus);
        } catch {
            return [];
        }
    }
    return [];
};

const isLockedIn = (lockedUntil) => {
    if (!lockedUntil) return false;
    return new Date(lockedUntil) > new Date();
};

const convertDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
};

const STATUS_CONFIG = {
    [STATUS_TYPES.TRANSFER]: {
        enabledIcon: MdLockOpen,
        disabledIcon: MdOutlineLock,
        enabledColor: "text-green-500",
        disabledColor: "text-red-500",
        enabledTitle: "Transfer Unlocked",
        disabledTitle: "Transfer Lock",
        enabledDesc: "Can now be reassigned or transferred.",
        disabledDesc: "Cannot be reassigned or transferred.",
        disabledDescWithDate: (date) => `Until ${date}, and cannot be reassigned or transferred.`
    },
    [STATUS_TYPES.UPDATE]: {
        enabledIcon: MdOutlineEdit,
        disabledIcon: MdOutlineEditOff,
        enabledColor: "text-green-500",
        disabledColor: "text-red-500",
        enabledTitle: "Update Enabled",
        disabledTitle: "Update Disabled",
        enabledDesc: "Unlocked and enabled for editing.",
        disabledDesc: "Locked and cannot be modified at this time."
    },
    [STATUS_TYPES.HOLD]: {
        enabledIcon: MdOutlineFlag,
        disabledIcon: MdOutlineFlag,
        enabledColor: "text-green-500",
        disabledColor: "text-red-500",
        enabledTitle: "Active",
        disabledTitle: "On Hold",
        enabledDesc: "Verified and ready for use.",
        disabledDesc: "Domain access and features are temporarily disabled."
    },
    [STATUS_TYPES.DELETE]: {
        enabledIcon: TbTrash,
        disabledIcon: TbTrashOff,
        enabledColor: "text-green-500",
        disabledColor: "text-red-500",
        enabledTitle: "Delete Enabled",
        disabledTitle: "Delete Disabled",
        enabledDesc: "You can delete this domain if needed.\nPlease proceed with caution.",
        disabledDesc: "Deletion is locked or restricted."
    },
    [STATUS_TYPES.RENEW]: {
        enabledIcon: BsDatabaseFillLock,
        disabledIcon: MdPauseCircleOutline,
        enabledColor: "text-green-500",
        disabledColor: "text-red-500",
        enabledTitle: "Client Hold Status",
        disabledTitle: "Renew Disabled",
        enabledDesc: (hasHold) => hasHold ? "Domain is currently on Client Hold." : "Domain is not on Client Hold",
        disabledDesc: "Domain's registry to reject requests to renew your domain.\nIt is an uncommon status that is usually enacted during legal disputes or when your domain is subject to deletion."
    }
};

const StatusIndicator = ({ statusType, isDisabled, lockedUntil, clientStatus }) => {
    const config = STATUS_CONFIG[statusType];
    const IconComponent = isDisabled ? config.disabledIcon : config.enabledIcon;
    const color = isDisabled ? config.disabledColor : config.enabledColor;
    const title = isDisabled ? config.disabledTitle : config.enabledTitle;

    let description = isDisabled ? config.disabledDesc : config.enabledDesc;

    if (statusType === STATUS_TYPES.TRANSFER && isDisabled && lockedUntil && isLockedIn(lockedUntil)) {
        description = config.disabledDescWithDate(convertDate(lockedUntil));
    }

    if (statusType === STATUS_TYPES.RENEW && !isDisabled) {
        const hasHold = clientStatus.includes(STATUS_TYPES.HOLD);
        description = config.enabledDesc(hasHold);
        const renewColor = hasHold ? "text-red-400 ml-0.5" : "text-green-500 ml-0.5";

        return (
            <div className="relative group inline-block tooltip-arrow">
                <IconComponent className={renewColor} />
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 min-w-max">
                    <div className="flex items-start gap-2">
                        <MdInfoOutline className="text-white mt-0.5" />
                        <div>
                            <div>{title}</div>
                            <div className="text-xs text-white mt-1">
                                {description}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="relative group inline-block tooltip-arrow">
            <IconComponent className={color} />
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10 min-w-max">
                <div className="flex items-start gap-2">
                    <MdInfoOutline className="text-blue-300 mt-0.5" />
                    <div>
                        <div>{title}</div>
                        <div className="text-xs text-gray-300 mt-1" style={{ whiteSpace: 'pre-line' }}>
                            {description}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};


export const Status = ({ domain, locked_until = null }) => {
    const clientStatus = parseClientStatus(domain.client_status);

    return (
        <div className="inline-flex justify-between gap-1">
            <StatusIndicator
                statusType={STATUS_TYPES.TRANSFER}
                isDisabled={clientStatus.includes(STATUS_TYPES.TRANSFER)}
                lockedUntil={locked_until}
                clientStatus={clientStatus}
            />

            <StatusIndicator
                statusType={STATUS_TYPES.UPDATE}
                isDisabled={clientStatus.includes(STATUS_TYPES.UPDATE)}
                lockedUntil={locked_until}
                clientStatus={clientStatus}
            />

            <StatusIndicator
                statusType={STATUS_TYPES.HOLD}
                isDisabled={clientStatus.includes(STATUS_TYPES.HOLD)}
                lockedUntil={locked_until}
                clientStatus={clientStatus}
            />

            <StatusIndicator
                statusType={STATUS_TYPES.DELETE}
                isDisabled={clientStatus.includes(STATUS_TYPES.DELETE)}
                lockedUntil={locked_until}
                clientStatus={clientStatus}
            />

            <StatusIndicator
                statusType={STATUS_TYPES.RENEW}
                isDisabled={clientStatus.includes(STATUS_TYPES.RENEW)}
                lockedUntil={locked_until}
                clientStatus={clientStatus}
            />
        </div>
    );
};

export { STATUS_TYPES };

export default Status;
