<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\DB; 

class ClientNotificationService
{
    /**
     * CREATE NOTIFICATION FOR CLIENT 
     * 
     * @param int    $userId 
     * @param string $title 
     * @param string $message 
     * @param string $importance 
     * 
     * @return void
     */
    PUBLIC function createClientNotification(
        int    $userId,
        string $title,
        string $message,
        string $importance,
        string $redirectUrl
    ) {
        $data = [
            'user_id'      => $userId,
            'title'        => $title,
            'message'      => $message,
            'importance'   => $importance,
            'redirect_url' => $redirectUrl,
            'read_at'      => null,
            'created_at'   => now(),
            'updated_at'   => now(),
        ];

        DB::client()->table('notifications')->insert($data);
    }
}