import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";

export default function Filter() {
    const { orderby, email, status } = route().params;

    const containerRef = useRef();

    const [emailInput, setEmailInput] = useState(email || "");

    const config = {
        container: {
            active: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [
                    "domain:desc",
                    "domain:asc",
                    "date_created:desc",
                    "date_created:asc",
                ],
                name: "Order By",
            },
            email: {
                active: false,
                value: email ? [email] : [],
                type: "text",
                name: "Email",
                tempValue: emailInput,
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        let { orderby, email, status } = updatedFilter.field;
        let payload = {};

        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (email.value.length > 0) payload.email = email.value[0];
        if (status.value.length > 0) payload.status = status.value[0];

        router.get(route("transfer.view"), payload);
    };

    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);
        setFilter({
            ...closedFilter,
            ...newObject,
        });
    };

    const handleFieldUpdateValue = (key, value) => {
        if (key === "email") {
            setEmailInput(value);

            if (!value || value === emailInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: {
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        submit(updatedFilter);
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
            </div>
        </div>
    );
}