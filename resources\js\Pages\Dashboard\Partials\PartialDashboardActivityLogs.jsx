//* PACKAGES
import React, { useState, useEffect } from "react";
import { <PERSON>, router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";
import axios from "axios";

//* ICONS
import {
    FiGlobe,
    Fi<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ist,
    <PERSON><PERSON>ser,
    FiFileText,
    <PERSON><PERSON>lock,
} from "react-icons/fi";
import {
    MdOutlinePersonOutline,
    MdExitToApp,
    MdSecurity,
    MdOutlineCheckCircle,
    MdOutlineLibraryAddCheck,
    MdOutlineContactPage,
    MdOutlineSystemUpdateAlt,
    MdOutlineVerifiedUser,
} from "react-icons/md";
import { HiOutlinePencilSquare } from "react-icons/hi2";
import { TbCategoryPlus } from "react-icons/tb";

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
import { usePermissions } from "@/Hooks/usePermissions";

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialDashboardActivityLogs(
    {
        //! PROPS
        //...
        //! STATES
        //...
        //! EVENTS
        //...
    }
) {
    //! PACKAGE
    //...

    //! VARIABLES
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const { logs } = usePage().props;

    const typeIcons = {
        SIGN_IN: <MdOutlineCheckCircle />,
        SIGN_OUT: <MdExitToApp />,
        REGISTER: <MdOutlineLibraryAddCheck />,
        PROFILE_UPDATE: <HiOutlinePencilSquare />,
        SECURITY_UPDATE: <MdSecurity />,
        CATEGORY_UPDATE: <TbCategoryPlus />,
        CONTACT_UPDATE: <MdOutlineContactPage />,
        DOMAIN_UPDATE: <MdOutlineSystemUpdateAlt />,
        IDENTITY_VERIFICATION: <MdOutlineVerifiedUser />,
    };

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function getStatusLabel(type) {
        const normalizedType = type.replace(/ /g, "_").toUpperCase();
        const label =
            (logs[0]?.statuses && logs[0].statuses[normalizedType]) || type;
        const icon = typeIcons[normalizedType] || <MdOutlinePersonOutline />;

        return { label, icon };
    }

    const groupedLogs =
        logs?.length > 0
            ? logs
                  .flatMap((entry) => entry.logs || []) // flatten all inner logs
                  .reduce((acc, log) => {
                      const date = log.formatted_created_at || "Unknown Date";
                      if (!acc[date]) {
                          acc[date] = [];
                      }
                      acc[date].push(log);
                      return acc;
                  }, {})
            : {};

    return (
        <div className="bg-gray-100 p-4 rounded-xl w-full">
            <div className="flex items-center gap-2 mb-4">
                <FiClock className="text-xl" />
                <h2 className="text-lg font-semibold">My Activity Logs</h2>
            </div>
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                {Object.keys(groupedLogs).length === 0 ? (
                    <div className="bg-white rounded-md p-8 text-center text-gray-500">
                        <p className="text-lg">No Logs Found</p>
                    </div>
                ) : (
                    Object.entries(groupedLogs).map(([date, logs]) => (
                        <React.Fragment key={date}>
                            <div className="text-sm font-semibold text-gray-800 mb-2 items-center">
                                <div className="flex items-center">
                                    <div className="bg-white rounded-md border border-gray-300 px-4 py-2 font-bold">
                                        {date}
                                    </div>
                                    <div className="flex-grow border-t border-gray-300 ml-2"></div>
                                </div>

                                <table className="w-full mb-4">
                                    <tbody className="text-md">
                                        {logs.map((lg, index) => {
                                            const { label, icon } =
                                                getStatusLabel(lg.type);
                                            return (
                                                <tr key={index} className="">
                                                    <td className="py-2 px-2 w-5/6">
                                                        <span className="text-gray-600 text-xs flex items-center gap-1">
                                                            <span className="text-base">
                                                                {icon}
                                                            </span>
                                                            <span className="">
                                                                {label} - {lg.message}{" "}{lg.email}
                                                            </span>
                                                            <span>
                                                                
                                                            </span>
                                                        </span>
                                                    </td>
                                                    <td className="py-2 px-2 w-1/6 text-gray-600" style={{ fontSize: "12px" }}>

                                                        {new Date(
                                                            lg.created_at
                                                        ).toLocaleTimeString(
                                                            "en-US",
                                                            {
                                                                hour: "2-digit",
                                                                minute: "2-digit",
                                                                hour12: true,
                                                            }
                                                        )}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                        </React.Fragment>
                    ))
                )}
            </div>

            {hasPermission("client.logs.security.all") ? (
                <div className="mt-6 flex justify-center">
                    <Link
                        href={route("client.logs.security.all")}
                        className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition text-xs-custom"
                    >
                        View My Activity Logs
                    </Link>
                </div>
            ) : null}
        </div>
    );
}
