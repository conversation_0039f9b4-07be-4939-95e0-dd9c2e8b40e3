import { useRef, useState } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import useOutsideClick from "@/Util/useOutsideClick";
import { Link } from "@inertiajs/react";
import Checkbox from "@/Components/Checkbox";

import { MdMoreVert, MdOutlineLock, MdOutlinePause } from "react-icons/md";

export default function DomainItems({ item }) {
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    <Checkbox />
                    <Link
                        href={route("domain.view", { id: item.id })}
                        method="get"
                        as="button"
                        type="button"
                    >
                        <span className="text-link cursor-pointer">
                            {item.name}
                        </span>
                    </Link>
                </label>
            </td>
            <td>
                <span>Uncategorized</span>
            </td>

            <td>
                <MdOutlineLock className="text-success" />
            </td>
            <td>
                <label className="flex items-center space-x-2">
                    <MdOutlineLock className="text-primary" />
                </label>
            </td>
            <td>
                <span>{new Date(item.expiry).toDateString()}</span>
            </td>
            <td>
                {/* <MdOutlinePlayCircleOutline className="text-primary" /> */}
                {/* <MdOutlinePause className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" /> */}
                <MdOutlinePause className="text-gray-400" />
                {/* <MdRefresh className="text-link  animate-spin-slow" /> */}
            </td>
            <td>
                <span>{item.status}</span>
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <span className="hover:bg-gray-100 px-5 py-1 text-gray-200">
                            Lock
                        </span>
                        <span className=" px-5 py-1 text-gray-200">Unlock</span>
                        <span className="hover:bg-gray-100 px-5 py-1 text-gray-200">
                            Get Whois privacy
                        </span>
                        <span className=" px-5 py-1 text-gray-200">
                            Enable auto renew
                        </span>
                        <span className="hover:bg-gray-100 px-5 py-1 text-gray-200">
                            Disable auto renew
                        </span>
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
