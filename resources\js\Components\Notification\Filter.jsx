import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import { router } from "@inertiajs/react";
import { offFilter, updateFieldValue } from "@/Components/Util/Filter/FilterMethod";

export default function Filter() {
    const { status, type, orderby, schedule_type, limit } = route().params;

    const filterConfig = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [
                    "next_date:desc",
                    "next_date:asc",
                    "created_at:desc",
                    "created_at:asc"
                ],
                name: "Order By",
            },
            status: {
                active: false,
                value: status ? [status] : [],
                type: "option",
                items: ["active", "pending", "disabled", "expired"],
                name: "Status",
            },
            type: {
                active: false,
                value: type ? [type] : [],
                type: "option",
                items: ["Important", "Normal"],
                name: "Type",
            },
            schedule_type: {
                active: false,
                value: schedule_type ? [schedule_type] : [],
                type: "option",
                items: ["one-time", "weekly", "monthly", "yearly"],
                name: "Schedule",
            },
            limit: {
                active: false,
                value: limit ? [limit] : [],
                type: "option",
                items: ["20", "30", "50", "100"],
                name: "Entries",
            }
        },
    };

    const [filter, setFilter] = useState(filterConfig);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);
        
        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    const handleFieldUpdateValue = (key, value) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        
        const payload = {
            ...route().params,
            [key]: value,
        };

        router.get(route("notification.management"), payload);
    };

    return (
        <div className="flex items-center relative">
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div ref={ref}>
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.status}
                    fieldKey="status"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.type}
                    fieldKey="type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.schedule_type}
                    fieldKey="schedule_type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <OptionFilter
                    fieldProp={field.limit}
                    fieldKey="limit"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
} 