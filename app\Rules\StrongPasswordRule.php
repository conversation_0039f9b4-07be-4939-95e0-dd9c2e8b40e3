<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StrongPasswordRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) < 8) 
        {
            $fail("Password must be at least a minimum of 8 characters.");
        }

        preg_match_all('/\d/', $value, $numbers);
        
        if (count($numbers[0]) < 1) 
        {
            $fail("Password must contain at least 1 digit.");
        }

        if (!preg_match('/[A-Z]/', $value)) 
        {
            $fail("Password must contain at least 1 uppercase letter.");
        }

        if (!preg_match('/[a-z]/', $value)) 
        {
            $fail("Password must contain at least 1 lowercase letter.");
        }

        if (!preg_match('/[\W_]/', $value)) 
        {
            $fail("Password must contain at least 1 special character.");
        }
    }
}
