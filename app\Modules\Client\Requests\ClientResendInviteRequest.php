<?php

namespace App\Modules\Client\Requests;

use App\Modules\Client\Services\InviteUserService;
use Illuminate\Foundation\Http\FormRequest;

class ClientResendInviteRequest extends FormRequest
{
   
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'data.*' => ['required', 'array'],
        ];
    }

    public function resendInvitation()
    {
        return InviteUserService::resendInvitation($this->userid);
    }
}
