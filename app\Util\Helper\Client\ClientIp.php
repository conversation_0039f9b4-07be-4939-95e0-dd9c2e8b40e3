<?php

namespace App\Util\Helper\Client;

class ClientIp
{
    public static function getClientIp(mixed $request): string
    {
        if ($request->headers->has('x-forwarded-for')) {
            $xForwardedFor = $request->headers->get('x-forwarded-for');
            $ips = explode(',', $xForwardedFor);

            return $ips[0];
        } else {
            return $request->ip();
        }
    }
}
