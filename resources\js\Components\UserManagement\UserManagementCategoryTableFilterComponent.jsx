//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import useOutsideClick from "@/Util/useOutsideClick";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";

//* ICONS
//...

//* COMPONENTS
import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementCategoryTableFilterComponent(
    {
        pageRoute = 'user-management.category'
    }
)
{
    //! PACKAGE
    const { orderBy, category } = route().params;
    const refContainer         = useRef();

    //! VARIABLES
    const [stateInput, setStateInput] = useState(category || "");

    const config =
    {
        container:
        {
            active: false,
        },
        field: {
            orderBy:
            {
                active: false,
                value: orderBy ? [orderBy] : [],
                type: "option",
                items:
                [
                    "category:desc",
                    "category:asc",
                    "permissions:desc",
                    "permissions:asc",
                    "createdBy:desc",
                    "createdBy:asc",
                    "lastUpdated:desc",
                    "lastUpdated:asc",
                ],
                name: "Order By",
            },
            category:
            {
                active: false,
                value: category ? [category] : [],
                type: "text",
                name: "Category",
                tempValue: stateInput,
            },
        },
    };
    //! STATES
    const [filter, setFilter]         = useState(config);
    const { field } = filter;

    //! FUNCTIONS
    //...
    useOutsideClick(refContainer, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });



    function handleSubmit(updatedFilter)
    {
        let { orderBy, category } = updatedFilter.field;
        let payload = {};

        if (orderBy.value.length > 0) payload.orderBy = orderBy.value[0];
        if (category.value.length > 0) payload.category = category.value[0];

        router.get(
            route(pageRoute),
            payload, 
            {
                preserveState: false, 
                replace : true, 
            }
        ); 
    };

    function handleDisplayToggle(newObject)
    {
        const closedFilter = offFilter(filter);
        
        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    function handleFieldUpdateValue(key, value)
    {
        if (key == "category")
        {
            setStateInput(value);
            
            if (!value || value === stateInput)
            {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter =
                {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { ...newValue }
                    },
                };

                setFilter(offFilter(updatedFilter));
                handleSubmit(updatedFilter);
                
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    category: {
                        ...prevFilter.field.category,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        handleSubmit(updatedFilter);
    };

    return (
        <div
            className="flex items-center relative"
            ref={refContainer}
        >
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderBy}
                    fieldKey="orderBy"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <TextFilter
                    fieldProp={field.category}
                    fieldKey="category"
                    placeholder='Search Category'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.category.tempValue || field.category.value[0] || "";
                        handleFieldUpdateValue("category", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />
            </div>
        </div>
    );
}
