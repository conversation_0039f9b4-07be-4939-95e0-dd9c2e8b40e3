import { router } from "@inertiajs/react";
import { MdArrowBackIos, MdArrowForwardIos } from "react-icons/md";

export default function PollCursorPaginate({
    onFirstPage = true,
    onLastPage = false,
    nextPageCursor = null,
    previousPageCursor = null,
}) {
    const { domain, type, orderby } = route().params;

    const goToPage = (cursor, isPrev = false) => {
        router.visit(route('epp.poll'), {
            method: 'get',
            preserveScroll: true,
            preserveState: true,
            data: {
                cursor: cursor || null,
                registry: route().params.registry,
                isPrev,
                domain: domain || null,
                type: type || null,
                orderby: orderby || null,
            },
        });
    };

    return (
        <div className="flex justify-end items-center mt-4">
            <div className="flex space-x-2 pr-3">
                <button
                    type="button"
                    onClick={() => goToPage(previousPageCursor, true)}
                    disabled={onFirstPage}
                    className="px-3 py-1 text-sm rounded border border-gray-300 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Previous page"
                >
                    <MdArrowBackIos />
                </button>
                <button
                    type="button"
                    onClick={() => goToPage(nextPageCursor, false)}
                    disabled={onLastPage || !nextPageCursor}
                    className="px-3 py-1 text-sm rounded border border-gray-300 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Next page"
                >
                    <MdArrowForwardIos />
                </button>
            </div>
        </div>

    );
}
