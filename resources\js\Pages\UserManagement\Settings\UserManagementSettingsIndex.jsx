//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import AdminLayout from '@/Layouts/AdminLayout';

//* PARTIALS
import PartialUserManagementSettingsSectionPermissions from './Partials/PartialUserManagementSettingsSectionPermissions';
import PartialUserManagementSettingsSectionCategory from './Partials/PartialUserManagementSettingsSectionCategory';
import PartialUserManagementSettingsSectionRole from './Partials/PartialUserManagementSettingsSectionRole';

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementSettingsIndex(props)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    const itemSections = 
    [
        {
            label    : 'Permissions',
            component: <PartialUserManagementSettingsSectionPermissions/>
        },     
        {
            label    : 'Categories',
            component: <PartialUserManagementSettingsSectionCategory/>
        },     
        {
            label    : 'Roles',
            component: <PartialUserManagementSettingsSectionRole/>
        },     
    ];
    

    //! STATES
    //...
    
    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <AdminLayout
            postRouteName={"user-management.settings"}
        >
            <div
                className="mx-auto container max-w-[1200px] mt-5 flex flex-col gap-8 rounded-lg"
            >
                {
                    itemSections.map(
                        (item, index) => 
                        {
                            return (
                                <section
                                    key={index}
                                    className="grid lg:grid-cols-8 gap-4"
                                >
                                    <div
                                        className='capitalize font-bold text-lg'
                                    >
                                        {item.label}
                                    </div>
                                    <hr className="lg:hidden" />
                                    <div className="w-px bg-gray-200 hidden lg:block">
                                        
                                    </div>
                                    <div
                                        className='lg:col-span-6'
                                    >
                                        {item.component}
                                    </div>
                                </section>
                            ); 
                        }
                    )
                }
            </div>
        </AdminLayout>
    );
}
