{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@headlessui/react": "^1.7.19", "@inertiajs/react": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-react": "^3.0.0", "autoprefixer": "^10.4.12", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.5", "postcss": "^8.4.18", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^4.0.0"}, "dependencies": {"clipboard-copy": "^4.0.1", "evergreen-ui": "^7.1.9", "jotai": "^2.2.0", "laravel-precognition-react-inertia": "^0.7.2", "lodash": "^4.17.21", "react-data-table-component": "^7.6.2", "react-datepicker": "^4.21.0", "react-icons": "^4.12.0", "react-infinite-scroll-component": "^6.1.0", "react-switch": "^7.1.0", "react-toastify": "^9.1.3", "styled-components": "^6.1.13"}}