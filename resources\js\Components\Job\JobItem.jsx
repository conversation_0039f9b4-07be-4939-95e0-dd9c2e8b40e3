//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
//...

//* COMPONENTS
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function JobItem({
    source,
    connection,
    dispatches,
    success,
    failed
})
{
    //! PACKAGE
    //...
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [data, setData] = useState({});
    const [loading, setLoading] = useState(false);
    const [hasSpinner, setSpinner] = useState(false);

    //! USE EFFECTS
    useEffect(() => {
        setLoading(true);

        const fetchJobData = async () => {
            try {
                const response = await axios.get(route("job.data", { connection, source }));
                setData(response.data);
            } catch (error) {
                console.error("Failed to load data for job:", error);
                setData(null);
            } finally {
                setLoading(false);
            }
        };

        fetchJobData();
    }, [connection, source]);

    //! FUNCTIONS    
    router.on("start", () => setSpinner(true));
    router.on("finish", () => setSpinner(false));

    return (
        <tr className="hover:bg-gray-100">
            <td className="px-2 py-2">
                {
                    hasPermission("job.view")
                        ? 
                            <Link
                                href={route("job.view", { connection, source, tab: 'queue' })}
                                method="get"
                                as="button"
                                type="button"
                                title="Click to explore"
                            >
                                <span className="text-link cursor-pointer">{connection}</span>
                            </Link>
                        :
                            <span className="">{connection}</span>
                }
            </td>
            <td className="text-center">
                {loading ? (
                    <div className="flex justify-center items-center">
                        <LoaderSpinner ml="ml-0" h="h-4" w="w-4" />
                    </div>
                ) : (<span>{data.onQueueCount}</span>)}
            </td>
            <td className="text-center">
                {loading ? (
                    <div className="flex justify-center items-center">
                        <LoaderSpinner h="h-4" w="w-4" />
                    </div>
                ) :   hasPermission("job.view") && data.onFailedCount > 0 ? (
                    <Link
                        href={route("job.view", { connection, source, tab: 'failed' })}
                        method="get"
                        as="button"
                        type="button"
                        title="Click to explore"
                    >
                        <span className="text-danger font-bold cursor-pointer">{data.onFailedCount}</span>
                    </Link>
                ) : (
                    <span>{data.onFailedCount}</span>
                )}
            </td>
            <td className="text-center text-white space-x-1">
                <span className={`bg-primary rounded-full px-2.5 py-0 space-x-1 ${dispatches == 0 && "opacity-30"} text-white inline-flex items-center text-xs`}>
                    <span>Dispatch:</span>
                    <span className="font-bold text-lg ">{dispatches}</span>
                </span>
                <span className={`bg-success rounded-full px-2.5 py-0 space-x-1 ${success == 0 && "opacity-30"} text-white inline-flex items-center text-xs`}>
                    <span>Success:</span>
                    <span className="font-bold text-lg">{success}</span>
                </span>
                <span className={`bg-danger rounded-full px-2.5 py-0 space-x-1 ${failed == 0 && "opacity-30"} text-white inline-flex items-center text-xs`}>
                    <span>Failed:</span>
                    <span className="font-bold text-lg">{failed}</span>
                </span>
            </td>
        </tr >
    );
}
