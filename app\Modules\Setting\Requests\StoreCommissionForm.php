<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Services\CommissionSettingService;
use Illuminate\Foundation\Http\FormRequest;

class StoreCommissionForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'numeric', 'exists:client.commission_settings'],
            'minimum_domain' => ['required', 'numeric', 'gt:0'],
            'fix_rate' => ['required', 'numeric', 'gt:0'],
            'percent_rate' => ['required', 'numeric', 'gt:0'],
        ];
    }

    protected function passedValidation(): void
    {
        $this->merge([
            'fee_id' => $this->id,
            'minimum_total_node' => $this->minimum_domain,
        ]);
    }

    public function store()
    {
        CommissionSettingService::isUnique($this->fee_id, $this->minimum_total_node);
        CommissionSettingService::store($this);
    }
}
