<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Services\ExtensionFeeService;
use Illuminate\Foundation\Http\FormRequest;

class ExtensionFeeUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'numeric'],
            'extension' => ['required', 'string'],
            'type' => ['required', 'string'],
            'value' => ['required', 'numeric'],
            'extension_total' => ['required', 'numeric'],
            'is_default' => ['required', 'boolean'],
        ];
    }

    public function update()
    {
        return ExtensionFeeService::instance()->update(
            $this->id,
            $this->extension,
            $this->type,
            $this->value,
            $this->extension_total,
            $this->is_default,
        );
    }
}
