<?php

namespace App\Modules\DomainHistory\Controllers;

use App\Http\Controllers\Controller;
use Inertia\Inertia;
use App\Modules\DomainHistory\Services\DomainLogService;
use App\Modules\DomainHistory\Requests\ShowListRequest;

class DomainLogController extends Controller
{
    protected $domainLogService;

    public function __construct(DomainLogService $domainLogService)
    {
        $this->domainLogService = $domainLogService;
    }

    public function showDomainLogs(ShowListRequest $request, $id)
    {
        $data = $this->domainLogService->prepareInertiaData($id, $request->getFilters());

        return Inertia::render('DomainHistory/ViewIndex', $data);
    }
}

