<?php

namespace App\Modules\Epp\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Epp\Requests\PollRequestForm;
use Inertia\Inertia;

class EppPollController extends Controller
{
    public function index(PollRequestForm $request)
    {
        // dd($request->allPoll());
        return Inertia::render('Epp/Poll/Index', $request->allPoll());
    }

    public function view($id, PollRequestForm $request)
    {
        $request->validate([
            'item' => ['required'],
            'registry' => ['required']
        ]);

        return Inertia::render('Epp/Poll/View', $request->viewById());
    }

    public function pop(PollRequestForm $request)
    {
        $request->pop();

        return response()->json(['success' => true], 200);
    }
}
