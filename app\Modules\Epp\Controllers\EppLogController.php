<?php

namespace App\Modules\Epp\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Epp\Requests\LogRequestForm;
use Inertia\Inertia;

class EppLogController extends Controller
{
    public function index(LogRequestForm $request)
    {
        return Inertia::render('Epp/Log', ['log' => $request->log()]);
    }

    public function more(LogRequestForm $request)
    {
        return response()->json(['log' => $request->log()], 200);
    }

    public function refresh(LogRequestForm $request)
    {
        $request->refresh();

        return response()->json(['success' => true], 200);
    }
}
