<?php

namespace App\Modules\Domain\Services;

use App\Modules\Domain\Jobs\DomainEppClientHoldJob;
use App\Util\Constant\QueueConnection;

class DomainEppClientHoldJobService
{
    private static ?self $instance = null;

    private function __construct()
    {
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function dispatch(array $data): void
    {
        $job = new DomainEppClientHoldJob(
            $data['domainId'],
            $data['domainName'],
            $data['action']
        );

        $job->onQueue($data['queue']);
        $job->onConnection(QueueConnection::DOMAIN_CLIENT_HOLD);
        
        dispatch($job);
    }
} 