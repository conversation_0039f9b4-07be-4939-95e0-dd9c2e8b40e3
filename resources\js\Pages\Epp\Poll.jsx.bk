import Item from "@/Components/Epp/Poll/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import EmptyResult from "@/Components/Util/EmptyResult";
import AdminLayout from "@/Layouts/AdminLayout";
import useOutsideClick from "@/Util/useOutsideClick";
import { router } from "@inertiajs/react";
import { useRef, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
// import { useState } from "react";
import { evaluate } from "@/Util/AxiosResponseHandler";
import axios from "axios";

export default function Poll({ polls }) {
    const [progress, setProgress] = useState(false);
    const { registry } = route().params;
    const data = (polls.data = undefined ? {} : polls.data);
    const config = {
        verisign: {
            active: "verisign" == registry,
        },
        verisign2: {
            active: "verisign2" == registry,
        },
        pir: {
            active: "pir" == registry,
        },
    };
    const [show, setShow] = useState(false);
    const ref = useRef();

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const handleAcknowledgeNewPoll = async (targetRegistry) => {
        if (progress) return;
        const payload = { registry: targetRegistry };
        setProgress(true);
        toast.info(
            "Checking new poll from " + targetRegistry + " , please wait."
        );

        let response = await axios
            .get(route("poll.pop"), payload)
            .then((response) => {
                return response;
            })
            .catch((error) => {
                return error.response;
            });
        response = evaluate(response);
        if (response.success) {
            toast.success("Done, refreshing page.");
            router.get(route("epp.poll"), payload);
        } else {
            toast.error("Pulling failed, something went wrong.");
        }
    };

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[700px] mt-20 flex flex-col space-y-4">
                {registry != undefined && (
                    <div className="flex items-center space-x-4 justify-end">
                        <SecondaryButton
                            processing={progress}
                            onClick={() => handleAcknowledgeNewPoll(registry)}
                        >
                            {registry} : Acknowledge new poll
                        </SecondaryButton>
                    </div>
                )}

                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    <a
                        href={route("epp.poll", { registry: "verisign" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${
                                config.verisign.active &&
                                "bg-gray-100 text-link"
                            }`
                        }
                    >
                        <span className=" text-inherit">VERISIGN</span>
                    </a>
                    <a
                        href={route("epp.poll", { registry: "verisign2" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${
                                config.verisign2.active &&
                                "bg-gray-100 text-link"
                            }`
                        }
                    >
                        <span className=" text-inherit">VERISIGN2</span>
                    </a>
                    <a
                        href={route("epp.poll", { registry: "pir" })}
                        className={
                            "hover:bg-gray-100 px-5 py-1 rounded-sm " +
                            `${config.pir.active && "bg-gray-100 text-link"}`
                        }
                    >
                        <span className=" text-inherit">PIR</span>
                    </a>
                </div>
                {data == null ? (
                    <EmptyResult message="Select registry to view poll" />
                ) : (
                    <>
                        <div>
                            <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                                <thead className=" bg-gray-50 text-sm">
                                    <tr>
                                        <th>
                                            <label className="flex items-center pl-2 space-x-2">
                                                <span className="">Type</span>
                                            </label>
                                        </th>
                                        <th>
                                            <span>message</span>
                                        </th>
                                        <th>
                                            <span>received at</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="text-sm">
                                    {Object.values(data).map((e) => (
                                        <Item key={"pi" + e.id} item={e} />
                                    ))}
                                </tbody>
                            </table>
                        </div>
                        {/* <div className="flex items-center justify-end space-x-2 flex-wrap">
                            <SecondaryButton>
                                <MdArrowBackIos />
                            </SecondaryButton>
                            <SecondaryButton>
                                <MdArrowForwardIos />
                            </SecondaryButton>
                        </div> */}
                    </>
                )}
            </div>
        </AdminLayout>
    );
}
