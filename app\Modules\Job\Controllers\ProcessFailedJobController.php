<?php

namespace App\Modules\Job\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Job\Requests\ProcessFailedJobForm;

class ProcessFailedJobController extends Controller
{
    public function retry(ProcessFailedJobForm $request)
    {
        $request->retry();

        return redirect()->back();
    }

    public function flush(ProcessFailedJobForm $request)
    {
        $request->flush();

        return redirect()->back();
    }

    public function forget(ProcessFailedJobForm $request)
    {
        $request->forget();

        return redirect()->back();
    }
}
