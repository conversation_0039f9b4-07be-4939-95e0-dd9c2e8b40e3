<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('admin_roles', function (Blueprint $table) {
            //! Drop foreign keys first (use their constraint names or generated names)
            $table->dropForeign(['role_id']);
            $table->dropForeign(['admin_id']);

            //! Re-add with cascade on delete
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->foreign('admin_id')->references('id')->on('admins')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('admin_roles', function (Blueprint $table) {
            $table->dropForeign(['role_id']);
            $table->dropForeign(['admin_id']);

            // Re-add original foreign keys without cascade
            $table->foreign('role_id')->references('id')->on('roles');
            $table->foreign('admin_id')->references('id')->on('admins');
        });
    }
};
