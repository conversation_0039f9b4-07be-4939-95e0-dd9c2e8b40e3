//* PACKAGES
import { React, useState, useEffect } from "react";
import { usePage } from "@inertiajs/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

//* ICONS
//...

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function Guest({ children, widthsm = 'sm:max-w-md' }) {

    //! PACKAGE
    const { flash }       = usePage().props;

    //! VARIABLES
    //...

    //! STATES
    //... 

    //! FUNCTIONS
    //...

    //! USE EFFECTS
    useEffect(() => {
        flash.route_message ? toast.error(flash.route_message) : null;
    }, [flash.route_message]);

    useEffect(
        () =>
        {
            flash.successMessage ? toast.success(flash.successMessage) : null;
        },
        [flash.successMessage]
    );
    
    return (
        <div className="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100">
            {/* <div className="w-full sm:max-w-md mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg"> */}
            <div className={`w-full ${widthsm} mt-6 px-6 py-4 bg-white shadow-md overflow-hidden sm:rounded-lg`}>
                <div className="flex flex-col items-center">
                    <div className="p-3 bg-primary rounded-sm text-white font-bold">
                        SD
                    </div>
                    <span className="mt-2 font-bold text-gray-600 text-sm">
                        STRANGEDOMAIN
                    </span>
                </div>

                {children}
            </div>
        </div>
    );
}
