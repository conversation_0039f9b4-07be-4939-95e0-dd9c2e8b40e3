const getRemainingTime = (date, isUTC = false) => {
    const currentDate = new Date();
    const currentDateTime = currentDate.getTime();
    const targetDateTime = typeof date === 'number' ? date : new Date(isUTC ? date + "Z" : date).getTime();

    const durationInMilliseconds = targetDateTime - currentDateTime;
    const seconds = Math.floor(durationInMilliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        return `${days} day${days > 1 ? "s" : ""} left`;
    } else if (hours > 0) {
        return `${hours} hour${hours > 1 ? "s" : ""} left`;
    } else if (minutes > 0) {
        return `${minutes} minute${minutes > 1 ? "s" : ""} left`;
    } else {
        return `${seconds} second${seconds > 1 ? "s" : ""} left`;
    }
};

export default getRemainingTime;
