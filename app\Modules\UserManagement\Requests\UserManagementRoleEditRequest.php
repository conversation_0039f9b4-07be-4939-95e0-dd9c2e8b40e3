<?php

namespace App\Modules\UserManagement\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class UserManagementRoleEditRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required'
        ];
    }

    public function showUpdateForm()
    {
        $data = DB::table('access_category')
        ->select('category.id as c_id', 'category.name', 'access_category.id', 'access.id as a_id', DB::raw("string_agg(access.name, ',') AS access"))
        ->join('access', 'access_category.access_id', "=", 'access.id')
        ->join('category', 'category.id', "=", 'access_category.category_id')
        ->groupBy('category.id', 'access_category.id', 'category.name', 'access.id')
        ->get();

        $access = DB::table('access_roles AS ar')
        ->join('access AS a', 'ar.access_id', 'a.id')
        ->select('a.name', 'a.id')
        ->where('role_id', $this->id)
        ->get();

        $role = DB::table('roles')->where('id', $this->id)->get();

        return [
            'id'     => $this->id,
            'data'   => $data,
            'access' => $access,
            'role'   => $role
        ]; 
    }

    public function getRoleAccess() : \Illuminate\Support\Collection
    {
        $access = DB::table('access_roles AS ar')
        ->join('access AS a', 'ar.access_id', 'a.id')
        ->select('a.name', 'a.id')
        ->where('role_id', $this->id)
        ->get();

        return $access;
    }
}
