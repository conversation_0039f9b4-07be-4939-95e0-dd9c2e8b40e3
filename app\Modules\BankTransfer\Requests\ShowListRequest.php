<?php

namespace App\Modules\BankTransfer\Requests;

use App\Modules\BankTransfer\Services\BankTransferService;
use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'sortby' => ['string'],
        ];
    }

    public function index(): array
    {
        return BankTransferService::instance()->getIndexData($this);
    }
}
