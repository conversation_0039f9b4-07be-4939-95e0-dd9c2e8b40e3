<?php

namespace App\Providers;

use App\Events\AdminActionEvent;
use App\Events\DomainHistoryEvent;
use App\Listeners\AdminActionListener;
use App\Listeners\DomainListener;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Events\EmailSentEvent;
use App\Listeners\EmailSentListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        AdminActionEvent::class => [
            AdminActionListener::class,
        ],
        DomainHistoryEvent::class => [
            DomainListener::class,
        ],
        EmailSentEvent::class => [
            EmailSentListener::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
