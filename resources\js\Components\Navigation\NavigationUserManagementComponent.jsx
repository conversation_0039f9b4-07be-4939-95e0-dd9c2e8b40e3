//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import { MdExpandMore, MdExpandLess } from "react-icons/md";
import { LuUsers } from "react-icons/lu";
import { FaUserCog } from "react-icons/fa";
import { TfiMenuAlt } from "react-icons/tfi";
import { FaGear } from 'react-icons/fa6';

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationUserManagementComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        admin   : route().current("user-management.admin") || currentRoute.includes('user-management.admin') || (window.location.href).includes('user-management.admin'),
        roles   : route().current("user-management.role")  || currentRoute.includes('user-management.role')  || (window.location.href).includes('user-management.role'),
        category: route().current("user-management.category") || currentRoute.includes('user-management.category') || (window.location.href).includes('user-management.category'),
        settings: route().current("user-management.settings") || currentRoute.includes('user-management.settings') || (window.location.href).includes('user-management.settings'),
    };

    const links = 
    [
        {
            routeName: 'user-management.admin',
            hasAccess: hasPermission('user-management.admin'),
            isActive : routes.admin,
            icon     : <LuUsers className="text-2xl"/>,
            label    : 'users'
        }, 
        {
            routeName: 'user-management.role',
            hasAccess: hasPermission('user-management.role'),
            isActive : routes.roles,
            icon     : <FaUserCog className="text-2xl"/>,
            label    : 'roles'
        }, 
        {
            routeName: 'user-management.category',
            hasAccess: hasPermission('user-management.category'),
            isActive : routes.category,
            icon     : <TfiMenuAlt className="text-2xl"/>,
            label    : 'category'
        },
        {
            routeName: 'user-management.settings',
            hasAccess: hasPermission('user-management.settings'),
            isActive : routes.settings,
            icon     : <FaGear className="text-2xl"/>,
            label    : 'settings'
        }
    ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () =>
    {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">User Management</span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
