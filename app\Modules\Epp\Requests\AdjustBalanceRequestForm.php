<?php

namespace App\Modules\Epp\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AdjustBalanceRequestForm extends FormRequest
{
    private $balance;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function messages(): array
    {
        return [
            'amount.lte' => 'Amount should be lower than or equal to current balance',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        try {
            $this->balance = RegistryAccountBalanceService::balance($this->registry_id);
        } catch (Exception $e) {
            throw new FailedRequestException(400, 'Invalid Registry id', 'BAD REQUEST');
        }

        $injectRule = '';

        if ($this->has('type') && strcmp($this->type, 'credit') === 0) {
            $injectRule = '|lte:'.$this->balance->balance;
        }

        return [
            'registry_id' => 'required|integer',
            'amount' => 'required|numeric'.$injectRule,
            'type' => ['required', Rule::in(['credit', 'debit'])],
            'description' => 'required|string',
        ];
    }

    public function store()
    {
        switch ($this->type) {
            case 'credit':
                $response = RegistryAccountBalanceService::credit($this->balance, $this->amount, RegistryTransactionType::SUB_FUND, $this->description);
                break;
            case 'debit':
                $response = RegistryAccountBalanceService::debit($this->balance, $this->amount, RegistryTransactionType::ADD_FUND, $this->description);
                break;
        }

        if (strcmp(gettype($response), 'boolean') == 0) {
            return true;
        }

        if (method_exists($response, 'errors')) {
            return redirect()->route('epp.account.adjust-balance', ['registry_id', $this->balance->registry_id])->withInput()->with('errors', $response);
        }
    }
}
