import { Link } from "@inertiajs/react";
export default function Search({
    message,
    redirect = { route: route("epp.account"), name: "Go to EPP account" },
}) {
    return (
        <div>
            <div className="mx-auto container max-w-[900px] text-center pt-14 flex justify-center items-center flex-wrap">
                <div className="w-auto h-[10rem]">
                    <img
                        className="h-full w-auto"
                        src="/assets/images/searching.svg"
                        alt="background"
                    />
                </div>

                <span className=" text-lg font-semibold text-gray-700">
                    {message}
                </span>
            </div>
            <div className="w-full text-center flex flex-col  items-center pt-12 space-y-7">
                <Link
                    href={redirect.route}
                    className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none "
                >
                    {redirect.name}
                </Link>
            </div>
        </div>
    );
}
