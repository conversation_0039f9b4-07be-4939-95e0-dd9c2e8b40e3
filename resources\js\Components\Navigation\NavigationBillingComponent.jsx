//* PACKAGES
import React, {useState, useEffect} from 'react'

//* ICONS
import {
    MdExpandMore,
    MdTune,
    MdExpandLess,
    MdOutlineCorporateFare,
    MdOutlineImportExport,
    MdOutlineBugReport,
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationRegistryComponent(
    {
        postRouteName
    }
)
{
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;
    
    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        billingClient      : route().current("billing.client") || currentRoute.includes('billing.client'),
        billingRegistrar   : route().current("billing.registrar") || currentRoute.includes('billing.registrar'),
        billingWireTransfer: route().current("billing.wire.transfer") || currentRoute.includes('billing.wire.transfer'),
    };

    const links = 
    [
        {
            routeName: 'billing.wire.transfer',
            hasAccess: hasPermission('billing.wire.transfer'),
            isActive : routes.billingWireTransfer,
            icon     : <MdOutlineCorporateFare className="text-2xl"/>,
            label    : 'wire transfer'
        }, 
        {
            routeName: 'billing.client',
            hasAccess: hasPermission('billing.client'),
            isActive : routes.billingClient,
            icon     : <MdOutlineImportExport className="text-2xl"/>,
            label    : 'client payment summary'
        }, 
    ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () =>
    {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0)
    {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span
                    className=" text-inherit"
                >
                    Billing
                </span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => 
                        {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >   
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
