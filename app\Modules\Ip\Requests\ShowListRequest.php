<?php

namespace App\Modules\Ip\Requests;

use App\Modules\Ip\Services\IpService;
use Illuminate\Foundation\Http\FormRequest;

class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'is_active' => ['boolean'],
        ];
    }

    public function show()
    {
        return IpService::get($this);
    }
}
