<?php

namespace App\Modules\Notification\Requests;

use App\Modules\Notification\Services\NotificationService;
use Illuminate\Foundation\Http\FormRequest;

class UpdateNotificationReadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|string',
        ];
    }

    public function read()
    {
        return NotificationService::setRead($this->id);
    }
}
