import getRecentTime from "./getRecentTime"; 

export const convertDateUtil = (date, format = 1) =>
{
    switch (format)
    {
        case 1:
            return new Date(date + 'Z').toDateString();

        case 2:
            return getRecentTime(date, true);

        default:
            return new Date(date + 'Z').toDateString();
    }
};

export const convertDateToTimeUtil = (date) => 
{
    return new Date(date + 'Z').toLocaleTimeString();
}