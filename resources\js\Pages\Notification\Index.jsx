import AdminLayout from "@/Layouts/AdminLayout";
import NotificationItem from "@/Components/Notification/NotificationItem";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import { useState } from 'react';

import {
    MdNotifications,
    MdKeyboardBackspace
} from "react-icons/md";

import { router } from "@inertiajs/react";
import { stringify } from "postcss";
import { all } from "axios";
import { replace } from "lodash";
import LoaderSpinner from "@/Components/LoaderSpinner";

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
    queryParams = null,
}) {

    queryParams = queryParams || {}
    const searchFieldChange = (name, value) => {
        if (value) {
            queryParams[name] = value   
        } else {
            delete queryParams[name]
        }
        setSpinner(true);
        router.get(route('notification'), queryParams);
    };

    const [hasSpinner, setSpinner] = useState(false);

    router.on("start", () => {
        //setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });
    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[680px] mt-20 flex flex-col space-y-1">
                <div className="flex items-center justify-start gap-2 text-2xl font-bold border-b-2 border-gray-100 px-4 pb-4 mb-4">
                    <a href="#" onClick={() => window.history.back()}>
                        <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20 rounded-full p-1 transition duration-150 cursor-pointer" />
                    </a>
                    <MdNotifications className="text-3xl" />
                    Notifications
                </div>
                <div className="filterEntries ml-[5vh] text-1xl">
                    Show <select
                        className="rounded min-h-0 w-24 p-0 pl-3 text-base h-7"
                        name=""
                        id="table_size"
                        defaultValue={items.length}
                        onChange={e => searchFieldChange('limit', e.target.value)}
                    >
                        <option value=""></option>
                        <option value="15">15</option>
                        <option value="20">20</option>
                        <option value="25">25</option>
                        <option value="30">30</option>
                        <option value="40">40</option>
                        <option value="50">50</option>
                        <option value="100">100 </option>
                    </select> Entries
                    <div className="mx-auto container max-w-[1200px] mt-2 flex flex-col px-5 rounded-lg">{hasSpinner ? <LoaderSpinner ml='ml-64' h='h-14' w='w-14' position='fixed' /> : ""}</div>
                </div>
                {items.length > 0 ? (
                    <>
                        {items.map(item => (
                            <NotificationItem key={item.id} item={item} />
                        ))}
                    </>
                ) : (<span className="flex items-center justify-center py-32 text-3xl font-bold text-gray-200">No Notification</span>)}
                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                />
            </div>
        </AdminLayout>
    );
}
