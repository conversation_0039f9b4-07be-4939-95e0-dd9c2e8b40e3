//* PACKAGES
import React, { useState, useEffect } from 'react'

//* ICONS
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdExpand<PERSON>ess,
    MdExpandMore,
    MdOutlineConstruction,
    MdPieChartOutlined,
    MdPercent,
    MdSettings,
    MdNotificationAdd,
    MdDataThresholding,
} from "react-icons/md";

//* COMPONENTS
import NavLink from "@/Components/NavLink";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function NavigationSettingsComponent(
    {
        postRouteName
    }
) {
    //! PACKAGE
    const currentRoute = route().current() || postRouteName;

    //! HOOKS 
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const routes =
    {
        settingsGeneral: route().current("setting.general"),
        settingsFee: route().current("setting.fee"),
        settingsExtensionFee: route().current("setting.extension.fee"),
        settingsTransactionThreshold: route().current("setting.transaction.threshold"),
        notificationManagement: route().current("notification.management"),
    };

    const links =
        [
            {
                routeName: 'setting.general',
                hasAccess: hasPermission('setting.general'),
                isActive: routes.settingsGeneral,
                icon: <MdOutlineConstruction className="text-2xl" />,
                label: 'general'
            },
            {
                routeName: 'setting.fee',
                hasAccess: hasPermission('setting.fee'),
                isActive: routes.settingsFee,
                icon: <MdAttachMoney className="text-2xl" />,
                label: 'fees'
            },
            {
                routeName: 'setting.extension.fee',
                hasAccess: hasPermission('setting.extension.fee'),
                isActive: routes.settingsExtensionFee,
                icon: <MdAttachMoney className="text-2xl" />,
                label: 'extension fees'
            },
            {
                routeName: 'setting.transaction.threshold',
                hasAccess: hasPermission('setting.transaction.threshold'),
                isActive: routes.settingsTransactionThreshold,
                icon: <MdDataThresholding className="text-2xl" />,
                label: 'transaction threshold'
            },
            {
                routeName: 'notification.management',
                hasAccess: hasPermission('notification.management'),
                isActive: routes.notificationManagement,
                icon: <MdNotificationAdd className="text-2xl" />,
                label: 'general notification'
            },
        ];

    //! STATES
    const [stateShow, setStateShow] = useState(Object.values(routes).includes(true));

    //! FUNCTIONS
    const isVisible = () => {
        return !stateShow ? " hidden" : "";
    };

    if (links.filter(link => link.hasAccess).length == 0) {
        return null;
    }

    return (
        <>
            <button
                onClick={() => setStateShow(!stateShow)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span
                    className=" text-inherit"
                >
                    Settings
                </span>
                {stateShow ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            {
                links.filter(link => link.hasAccess)
                    .map(
                        (item, index) => {
                            return (
                                <NavLink
                                    key={index}
                                    href={route(item.routeName)}
                                    active={item.isActive}
                                    className={isVisible()}
                                >
                                    <div
                                        className='flex gap-4'
                                    >
                                        {item.icon}
                                        <span
                                            className='capitalize'
                                        >
                                            {item.label}
                                        </span>
                                    </div>
                                </NavLink>
                            );
                        }
                    )
            }

        </>
    );
}
