import { useState } from "react";

import NavLink from "@/Components/NavLink";
import { RiContactsBookLine } from "react-icons/ri";

import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdExpandLess,
    MdExpandMore,
    MdSupervisedUserCircle,
} from "react-icons/md";
export default function ClientNav({ postRouteName }) {
    const currentRoute = route().current() || postRouteName;

    const routes = {
        client: route().current("client") || route().current("client.domains"),
        extension_fee: route().current("client.extension") || currentRoute.includes('client.extension'),
        logs: route().current("client.logs") || currentRoute.includes('client.logs') ||
            route().current("client.logs.security.all") || currentRoute.includes('client.logs.security'),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Client</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("client")}
                active={routes.client}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdSupervisedUserCircle className="text-2xl " />
                    <span className=" text-inherit">Client</span>
                </span>

            </NavLink>
            <NavLink
                href={route("client.extension.fee")}
                active={routes.extension_fee}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <MdAttachMoney className="text-2xl " />
                    <span className=" text-inherit">Extension Fees</span>
                </span>

            </NavLink>
            {/* <NavLink
                href={route("client.logs.security.all")}
                active={routes.logs}
                className={visible()}
            >
                <span className="flex space-x-4">
                    <RiContactsBookLine className="text-2xl " />
                    <span className=" text-inherit">Client Activity Logs</span>
                </span>
            </NavLink> */}
        </>
    );
}
