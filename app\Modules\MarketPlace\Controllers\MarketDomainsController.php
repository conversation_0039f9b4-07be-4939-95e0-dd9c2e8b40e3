<?php

namespace App\Modules\MarketPlace\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\MarketPlace\Requests\UpdateDomainStatusRequest;
use App\Modules\MarketPlace\Services\MarketAuditService;
use App\Modules\MarketPlace\Services\MarketPlaceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class MarketDomainsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index() : \Inertia\Response
    {
        return Inertia::render('MarketPlace/MarketPlaceDomains', ['data' => MarketPlaceService::instance()->getDomains()]);
    }

    public function commissions() : \Inertia\Response
    {
        return Inertia::render('MarketPlace/MarketPlaceCommissions', ['data' => MarketAuditService::instance()->getCommissions()]);
    }

    public function update(UpdateDomainStatusRequest $request) : void
    {
        $request->update();
    }
}
