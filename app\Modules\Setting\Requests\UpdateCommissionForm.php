<?php

namespace App\Modules\Setting\Requests;

use App\Modules\Setting\Services\CommissionSettingService;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCommissionForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required'],
            // 'key' => ['required', 'string'],
            'value' => ['required'],
        ];
    }

    // protected function passedValidation(): void
    // {
    //     $this->merge([
    //         'fee_id' => $this->id,
    //         'minimum_total_node' => $this->minimum_domain
    //     ]);
    // }

    public function updateStatus()
    {
        $isActive = intval($this->value);
        $ids = is_array($this->id) ? $this->id : [$this->id];

        CommissionSettingService::updateStatus($ids, $isActive == 1 ? true : false);
    }
}
