<?php

namespace App\Modules\UserManagement\Services;

use App\Modules\CustomLogger\Services\AuthLogger;

use Illuminate\Support\Facades\DB;

class UserManagementPermissionService
{
    /**
     * Fetch Items 
     */
    public function fetchItems()
    {
        return DB::table('access')
            ->where('is_hidden', '=', false)
            ->get();
    }

    /**
     * fetch Itemss active by category 
     */
    public function fetchItemsActiveByCategory()
    {
        return DB::table('access_category')
            ->join('access', 'access_category.access_id', '=', 'access.id')
            ->select(
                'access.id as permissionId',
                'access.name as permissionName'
            )
            ->distinct()
            ->get();
    }

    /**
     * Fetch Items 
     */
    public function fetchItemsGroupByCategory()
    {
        return DB::table('access_category')
            ->select(
                'category.id as categoryId', 
                'category.name as categoryName', 
                'access_category.id as permissionCategoryId', 
                'access.id as permissionId', 
                'access.name as permissionName'
            )
            ->join('access', 'access_category.access_id', "=", 'access.id')
            ->join('category', 'category.id', "=", 'access_category.category_id')
            ->orderBy('access.name')
            ->groupBy('category.id', 'access_category.id', 'category.name', 'access.id')
            ->get();
    }
}
