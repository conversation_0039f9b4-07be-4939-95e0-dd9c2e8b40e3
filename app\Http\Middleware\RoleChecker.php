<?php

namespace App\Http\Middleware;

use Closure;
use Error;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

class RoleChecker
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        $user_id = Auth::id();

        $route = Route::getRoutes()->match($request);
        $rname = $route->getName();

        if($this->checkRole($rname, $user_id)) return $next($request);

        return redirect()->route('page403')->setStatusCode(403)->withErrors(['error' => '403: Not authorized.']);
    }

    private function checkRole($route, $user_id) : bool
    {
        $id = DB::table('access')->where('route', $route)->value('id');

        if(!$id) return false;

        return DB::table('admin_access')
        ->where('access_id', $id)
        ->where('admin_id', $user_id)
        ->exists();
    }
}
