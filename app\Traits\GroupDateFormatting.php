<?php

namespace App\Traits;

use Carbon\Carbon;

trait GroupDateFormatting
{
    private function getRelativeDateFormat(Carbon $date, string $formattedDate): string
    {
        $diffDays = abs(round(Carbon::today()->diffInDays($date, false)));

        return match(true) {
            $diffDays < 7 => "$formattedDate ($diffDays days ago)",
            $diffDays < 30 => $this->formatWeeks($formattedDate, $diffDays),
            $diffDays < 365 => $this->formatMonths($formattedDate, $diffDays),
            default => $this->formatYears($formattedDate, $diffDays)
        };
    }

    private function formatWeeks(string $formattedDate, int $diffDays): string
    {
        $weeks = round($diffDays / 7);
        return "$formattedDate ($weeks week" . ($weeks > 1 ? 's' : '') . " ago)";
    }

    private function formatMonths(string $formattedDate, int $diffDays): string
    {
        $months = round($diffDays / 30);
        return "$formattedDate ($months month" . ($months > 1 ? 's' : '') . " ago)";
    }

    private function formatYears(string $formattedDate, int $diffDays): string
    {
        $years = round($diffDays / 365);
        return "$formattedDate ($years year" . ($years > 1 ? 's' : '') . " ago)";
    }

    public function formatDate(string $dateString): string
    {
        $date = Carbon::parse($dateString);
        $today = Carbon::today();
        $formattedDate = $date->format('M j, Y');

        if ($date->isSameDay($today)) {
            return "Today ($formattedDate)";
        }

        if ($date->isSameDay(Carbon::yesterday())) {
            return "Yesterday ($formattedDate)";
        }

        return $this->getRelativeDateFormat($date, $formattedDate);
    }
}
