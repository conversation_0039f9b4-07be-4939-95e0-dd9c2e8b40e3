<?php

namespace App\Modules\Epp\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use Exception;
use Illuminate\Foundation\Http\FormRequest;

class AccountRequestForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'registry_id' => ['integer'],
        ];
    }

    public function account()
    {
        if (! $this->has('registry_id')) {
            return ['registries' => RegistryAccountBalanceService::allRegistry()];
        }

        return array_merge([
            'registry_id' => $this->registry_id,
            'registries' => RegistryAccountBalanceService::allRegistry(),
            'balance' => RegistryAccountBalanceService::balance($this->registry_id),
        ], RegistryAccountBalanceService::transactions($this->registry_id));
    }

    public function balance()
    {
        if (! $this->has('registry_id')) {
            throw new FailedRequestException(400, 'Registry id is required', 'BAD REQUEST');
        }
        try {
            return RegistryAccountBalanceService::balance($this->registry_id);
        } catch (Exception $e) {
            throw new FailedRequestException(400, 'Invalid Registry id', 'BAD REQUEST');
        }
    }
}
