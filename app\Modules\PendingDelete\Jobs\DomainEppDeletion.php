<?php

namespace App\Modules\PendingDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\PendingDelete\Services\DomainEppDeleteJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Exception;
use Throwable;

class DomainEppDeletion implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $deleteId,
        string $domainId,
        string $domainName,
        string $registeredDomainId,
        string $userId,
        string $email
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'deleteId' => $deleteId,
            'domainId' => $domainId,
            'domainName' => $domainName,
            'registeredDomainId' => $registeredDomainId,
            'userId' => $userId,
            'email' => $email
        ];

        $this->onConnection(QueueConnection::DOMAIN_DELETION);
        $this->onQueue(QueueTypes::DOMAIN_DELETION[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return (int) $this->params['deleteId'];
    }

    public function handle(): void
    {
        try {
            DomainEppDeleteJobService::instance()->eppDelete($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [5, 10];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($exception->getMessage());
        $this->dispatchFailedDomainHistory();
    }

    private function dispatchFailedDomainHistory(): void
    {
        $message = 'Domain "' . $this->params['domainName'] . '" deletion failed during pending deletion job';

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $this->params['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $this->params['userId'],
            'status'    => 'failed',
            'message'   => $message,
            'payload'   => json_encode($this->params),
            'created_at'=> now(),
            'updated_at'=> now(),
        ]);
    }
}
