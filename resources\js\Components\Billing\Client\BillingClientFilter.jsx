import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { _BillingClient } from "../../../Constant/_BillingClient";
import useOutsideClick from "@/Util/useOutsideClick";

export default function BillingClientFilter({ summaryTypes }) {
    const { orderby, type, name, email } = route().params;
    const containerRef = useRef();
    
    const [nameInput, setNameInput] = useState(name || "");
    const [emailInput, setEmailInput] = useState(email || "");

    const config = {
        container: {
            active: false,
            reload: false,
        },

        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: _BillingClient.orderBy.array,
                name: "Order By",
            },
            name: {
                active: false,
                value: name ? [name] : [],
                type: "text",
                name: "Name",
                tempValue: nameInput,
            },
            email: {
                active: false,
                value: email ? [email] : [],
                type: "text",
                name: "Email",
                tempValue: emailInput,
            },
            type: {
                active: false,
                value: type ? [type] : [],
                type: "option",
                items: Object.values(summaryTypes),
                name: "Type",
            },
        }
    };
    const [filter, setFilter] = useState(config);
    const { field } = filter;

    useOutsideClick(containerRef, () => {
        setFilter(prevFilter => {
            const updatedFilter = offFilter(prevFilter);
            return {
                ...updatedFilter,
                field: Object.keys(updatedFilter.field).reduce((acc, key) => ({
                    ...acc,
                    [key]: {
                        ...updatedFilter.field[key],
                        active: false
                    }
                }), {})
            };
        });
    });

    const submit = (updatedFilter) => {
        let { orderby, type, name, email } = updatedFilter.field;
        let payload = {};

        if (orderby.value.length) payload.orderby = orderby.value[0];
        if (type.value.length) payload.type = type.value[0];
        if (name.value.length) payload.name = name.value[0];
        if (email.value.length) payload.email = email.value[0];

        router.get(route(route().current()), payload, {
            preserveState: true,
            replace: true,
        });

        if (updatedFilter.container.reload) {
            toast.info("Reloading Data, Please Wait...");
        }
    };

    const handleDisplayToggle = (newObject) => {
        const closedFilter = offFilter(filter);
        
        setFilter({
            ...closedFilter,
            ...newObject
        });
    };

    const handleFieldUpdateValue = (key, value, forceReload = false) => {
        if (key === "name") {
            setNameInput(value);
            
            if (!value || value === nameInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    name: {
                        ...prevFilter.field.name,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        if (key === "email") {
            setEmailInput(value);
            
            if (!value || value === emailInput) {
                const newValue = updateFieldValue(value, { ...filter.field[key] });
                const updatedFilter = {
                    ...filter,
                    container: { ...filter.container, active: false },
                    field: {
                        ...filter.field,
                        [key]: { 
                            ...newValue,
                            tempValue: value
                        }
                    },
                };
                setFilter(offFilter(updatedFilter));
                submit(updatedFilter);
                return;
            }

            setFilter(prevFilter => ({
                ...prevFilter,
                field: {
                    ...prevFilter.field,
                    email: {
                        ...prevFilter.field.email,
                        tempValue: value
                    }
                }
            }));
            return;
        }

        const newValue = updateFieldValue(value, { ...filter.field[key] });
        const reload = forceReload || !(newValue.value.length === 0 && value !== "");

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue }
            },
        };

        setFilter(offFilter(updatedFilter));
        if (reload) {
            toast.info("Reloading Data, Please Wait...");
            submit(updatedFilter);
        }
    };

    return (
        <div className="flex items-center relative" ref={containerRef}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <div className="relative">
                <DisplayFilter
                    handleDisplayToggle={handleDisplayToggle}
                    container={filter.container}
                    field={filter.field}
                />

                <OptionFilter
                    fieldProp={field.orderby}
                    fieldKey="orderby"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />

                <TextFilter
                    fieldProp={field.name}
                    fieldKey="name"
                    placeholder='Search name'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.name.tempValue || field.name.value[0] || "";
                        handleFieldUpdateValue("name", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />

                <TextFilter
                    fieldProp={field.email}
                    fieldKey="email"
                    placeholder='Search email'
                    handleFieldUpdateValue={handleFieldUpdateValue}
                    offFilter={() => {
                        const currentValue = field.email.tempValue || field.email.value[0] || "";
                        handleFieldUpdateValue("email", currentValue);
                        setFilter(offFilter(filter));
                    }}
                />

                <OptionFilter
                    fieldProp={field.type}
                    fieldKey="type"
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
            </div>
        </div>
    );
}
