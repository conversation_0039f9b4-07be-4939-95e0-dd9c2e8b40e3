<?php

namespace App\Modules\BillingClient\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\BillingClient\Constants\PaymentNodeStatus;
use App\Modules\BillingClient\Constants\PaymentSummaryType;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Setting\Constants\FeeType;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;

class PaymentInvoiceService
{
    use CursorPaginate;

    private $defaultpageLimit = 20;

    public static function instance(): self
    {
        $paymentInvoiceService = new self;

        return $paymentInvoiceService;
    }

    public function getPaymentReimbursement(int $id, int $userId): array
    {
        if (! $id) {
            return [];
        }

        $reimbursement = $this->getReimbursementById($id, $userId);

        $data = [
            'reimbursement' => $reimbursement,
            'status' => PaymentNodeStatus::REFUNDED,
            'summary_type' => PaymentSummaryType::PAYMENT_REIMBURSEMENT,
        ];

        return $data;
    }

    public function getPaymentInvoice(int $id, int $userId): array
    {
        $data = DB::client()->table('payment_invoices')
            ->join('payment_node_invoices', 'payment_node_invoices.payment_invoice_id', '=', 'payment_invoices.id')
            ->leftJoin('payment_reimbursements', 'payment_reimbursements.payment_node_invoice_id', '=', 'payment_node_invoices.id')
            ->join('payment_nodes', 'payment_nodes.id', '=', 'payment_node_invoices.payment_node_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'payment_nodes.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('extension_fees', 'extension_fees.id', '=', 'payment_nodes.extension_fee_id')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('payment_services', 'payment_services.id', '=', 'payment_invoices.payment_service_id')
            ->where('payment_invoices.id', $id)
            ->where('payment_services.user_id', $userId)
            ->select(
                'payment_invoices.id as payment_invoices_id',
                'payment_invoices.total_amount as invoice_total_amount',
                'payment_invoices.paid_amount as invoice_paid_amount',
                'payment_invoices.status as invoice_status',
                'payment_invoices.total_payment_node',
                'payment_invoices.created_at',
                'payment_nodes.id as payment_node_id',
                'payment_nodes.registered_domain_id',
                'payment_nodes.year_length',
                'payment_nodes.rate',
                'payment_nodes.redemption_fee',
                'payment_nodes.total_amount',
                'payment_nodes.total_amount as node_total_amount',
                'payment_nodes.status as node_status',
                'fees.type as node_type',
                'payment_node_invoices.id as node_invoice_id',
                'payment_reimbursements.id as reimbursement_id',
                'payment_reimbursements.total_amount as reimbursement_total_amount',
                'payment_reimbursements.status as reimbursement_status',
                'payment_services.user_id as user_id',
                'payment_services.id as payment_service_id',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.system_credit_id',
                'payment_services.bank_transfer_id',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.extension_id',
                'registered_domains.status as registered_domain_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'domains.name',
                'domains.root',
                'domains.registrant',
                'domains.expiry',
                'domains.contacts',
            )
            ->get()->all();

        $year_sum = 0;
        foreach ($data as $domain) {
            $year_sum += $domain->year_length;
            $domain->node_type = FeeType::getText($domain->node_type);
        }

        $icann_total = round($data[0]->invoice_paid_amount - $data[0]->invoice_total_amount - $data[0]->redemption_fee, 2);
        $obj['data'] = $data;
        $obj['icann_total'] = $icann_total;
        $obj['paymentIntent'] = $this->getStripePaymentIntent($data[0]);
        $obj['summary_type'] = PaymentSummaryType::PAYMENT_INVOICE;

        return $obj;
    }

    public function getReimbursementById(int $id, int $userId): object
    {
        $reimbursement = DB::client()->table('payment_reimbursements as pr')
            ->join('payment_node_invoices as pni', 'pni.id', '=', 'pr.payment_node_invoice_id')
            ->join('payment_nodes as pn', 'pn.id', '=', 'pni.payment_node_id')
            ->join('payment_invoices as pi', 'pi.id', '=', 'pni.payment_invoice_id')
            ->join('registered_domains', 'registered_domains.id', '=', 'pn.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('extension_fees', 'extension_fees.id', '=', 'pn.extension_fee_id')
            ->join('fees', 'fees.id', '=', 'extension_fees.fee_id')
            ->join('payment_services as ps', 'ps.id', '=', 'pr.payment_service_id')
            ->where('pr.id', $id)
            ->where('ps.user_id', $userId)
            ->select(
                'pr.id',
                'pr.payment_node_invoice_id',
                'pr.total_amount',
                'pr.total_amount as current_balance',
                'pr.status as reimbursement_status',
                'pr.payment_service_id as payment_service_id',
                'pr.created_at',
                'ps.user_id as user_id',
                'pi.id as payment_invoice_id',
                'fees.type as node_type',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.extension_id',
                'registered_domains.status as registered_domain_status',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'domains.name',
                'domains.root',
                'domains.registrant',
                'domains.expiry',
                'domains.contacts',
            )->get()->first();

        if (! $reimbursement) {
            // app(AuthLogger::class)->info('get by id: id ' . $id);
            // app(AuthLogger::class)->info('get by id: userid ' . $userId);

            // app(AuthLogger::class)->info('get by id: userid ' . json_encode($reimbursement));
            throw new FailedRequestException(403, 'This action is not authorized.', 'Unauthorized');
        }

        return $reimbursement;
    }

    // PRIVATE FUNCTIONS

    private function getStripePaymentIntent(object $invoice)
    {
        if (! $invoice->stripe_id) {
            return null;
        }

        $stripeTransaction = DB::client()->table('stripe_transactions')
            ->where('id', $invoice->stripe_id)
            ->get()->first();

        return $stripeTransaction->payment_intent ?? null;
    }
}
