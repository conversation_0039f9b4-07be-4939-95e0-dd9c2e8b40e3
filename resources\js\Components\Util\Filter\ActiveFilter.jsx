import { MdCancel } from "react-icons/md";

export default function ActiveFilter({ field, handleFieldUpdateValue }) {
    // Use Object.entries without reversing since order is now handled by parent
    const fieldEntries = Object.entries(field);

    return (
        <div className="space-x-1 pr-2">
            {fieldEntries.map(([f, fieldData]) => {
                // Show either active filter value or temporary value for text fields
                if (fieldData.value.length > 0 || (fieldData.type === 'text' && fieldData.tempValue)) {
                    const filterName = fieldData.name;
                    const filterType = fieldData.type;
                    
                    // For text fields with temporary value
                    if (filterType === 'text' && fieldData.tempValue) {
                        return (
                            <div
                                key={"sf" + f + fieldData.tempValue}
                                className="bg-primary text-white px-2 mx-1 my-1 rounded-lg inline-flex items-center max-w-[20rem]"
                            >   
                                <span className="truncate">{filterName}:&nbsp;{fieldData.tempValue}</span>
                                <button
                                    className="pl-2"
                                    onClick={() => handleFieldUpdateValue(f, "")}
                                >
                                    <MdCancel />
                                </button>
                            </div>
                        );
                    }

                    // For other filter types with values
                    return fieldData.value.map(item => {
                        const handleValue = filterType === 'multiOption' ? item : "";
                        
                        return (
                            <div
                                key={"sf" + f + item}
                                className="bg-primary text-white px-2 mx-1 my-1 rounded-lg inline-flex items-center max-w-[20rem]"
                            >   
                                <span className="truncate">{filterName}:&nbsp;{item}</span>
                                <button
                                    className="pl-2"
                                    onClick={() => handleFieldUpdateValue(f, handleValue)}
                                >
                                    <MdCancel />
                                </button>
                            </div>
                        );
                    });
                }
                return null;
            })}
        </div>
    );
}
