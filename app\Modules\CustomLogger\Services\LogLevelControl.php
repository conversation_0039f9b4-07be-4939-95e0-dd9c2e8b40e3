<?php

namespace App\Modules\CustomLogger\Services;

class LogLevelControl
{
    private $intLevel;

    private $arrLogLevels = [
        'debug' => 0,
        'info' => 1,
        'warning' => 2,
        'error' => 3,
    ];

    public function __construct()
    {
        $this->setIntLevel(env('LOG_LEVEL', 'debug'));
    }

    private function setIntLevel(string $envLogLevel)
    {
        if (! $this->isValidLogLevel($envLogLevel)) {
            $this->intLevel = 0;

            return;
        }

        $this->intLevel = $this->arrLogLevels[$envLogLevel];
    }

    public function isAllowed(string $logLevel)
    {
        if (! $this->isValidLogLevel($logLevel)) {
            return false;
        }

        return $this->arrLogLevels[$logLevel] >= $this->intLevel;
    }

    private function isValidLogLevel(string $logLevel)
    {
        return array_key_exists($logLevel, $this->arrLogLevels);
    }
}
