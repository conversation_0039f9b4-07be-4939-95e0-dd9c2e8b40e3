{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "google/cloud-logging": "1.22", "google/cloud-secret-manager": "^1.12", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^1.0", "jenssegers/agent": "^2.6", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.9", "laravel/tinker": "^2.8", "tightenco/ziggy": "^1.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^2.0", "laravel/pint": "^1.16", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "classmap": [], "files": ["app/Helpers/SecurityHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}