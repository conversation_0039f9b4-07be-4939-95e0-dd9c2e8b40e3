<?php

namespace App\Modules\Domain\Requests;

use App\Modules\Domain\Services\DomainClientHoldServices;
use Illuminate\Foundation\Http\FormRequest;

class DomainClientHoldRequest extends FormRequest
{
    protected DomainClientHoldServices $domainClientHoldServices;

    public function __construct(DomainClientHoldServices $domainClientHoldServices)
    {
        parent::__construct();
        $this->domainClientHoldServices = $domainClientHoldServices;
    }
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'domain_ids' => ['required', 'array', 'min:1'],
            'domain_ids.*' => ['required', 'integer']
        ];
    }

    public function messages(): array
    {
        return [
            'domain_ids.required' => 'At least one domain is required',
            'domain_ids.array' => 'Domain IDs must be provided as an array',
            'domain_ids.min' => 'At least one domain is required',
            'domain_ids.*.required' => 'Each domain ID is required',
            'domain_ids.*.integer' => 'Each domain ID must be an integer',
            'domain_ids.*.exists' => 'One or more domain IDs do not exist in our records'
        ];
    }
    
    public function handle(): array
    {
        return $this->domainClientHoldServices->updateClientHoldStatus(
            $this->input('domain_ids')
        );
    }
}