import React from 'react';

function ExpirationNotice({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">{data.greeting}</p>
                <p className="mt-4">We hope you're well. Your domain is scheduled to expire in 30 days or more. Please see attached file for the full list.</p>
                <p className="mt-2">
                    Visit <a href="http://StrangeDomains.com" className="text-blue-600 underline italic">StrangeDomains.com</a> to renew now.
                </p>
                <p className="mt-2">If you already renewed your domain, please disregard this message.</p>
                <p className="mt-4 font-bold mb-2">Sincerely,</p>
                <p className="font-bold">{data.sender_name}</p>
            </div>
        </div>
    );
}

export default ExpirationNotice;