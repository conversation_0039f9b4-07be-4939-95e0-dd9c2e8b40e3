<?php

namespace App\Console\Commands\RequestDelete;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Exception;
use Illuminate\Console\Command;

class ApprovalDeleteRequest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:approval-delete-request';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process approved domain deletion requests after 24 hours - handles deletion and local updates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'ApprovalDeleteRequest: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo $e->getMessage();
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('ApprovalDeleteRequest: Running...');

        $processedCount = DomainDeleteService::instance()->processExpiredRequests();

        app(AuthLogger::class)->info("ApprovalDeleteRequest: Processed {$processedCount} expired requests");
        app(AuthLogger::class)->info('ApprovalDeleteRequest: Done');
    }
}
