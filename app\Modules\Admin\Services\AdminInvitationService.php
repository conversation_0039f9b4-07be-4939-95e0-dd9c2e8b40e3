<?php

namespace App\Modules\Admin\Services;

use App\Mail\AdminInvitationRegistrationMail;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail; 

class AdminInvitationService
{
    /**
     * @var int 
     */
    public int $validUntil = 60; 

    /**
     * Create Entry
     * 
     * @param array $data
     */
    public function createEntry(array $data) 
    {
        $token = Str::random(32);

        DB::table('admin_invitations')
            ->insert(
                [
                    'user_id'     => $data['userId'],
                    'token'       => $token,
                    'valid_until' => now()->addMinutes($this->validUntil)
                ]
            );

        $this->sendEmail(
            [
                'email'            => $data['email'],
                'name'             => $data['name'],
                'registrationLink' => route('admin-registration.setup', ['token' => $token])
            ]
        );
    }

    /**
     * Resend Entry 
     * 
     * @param int $adminId
     */
    public function resendEntry(int $adminId)
    {
        $admin = DB::table('admins')
            ->where('id', '=', $adminId)
            ->firstOrFail();

        DB::table('admin_invitations')
            ->where('user_id', '=', $adminId)
            ->delete();

        $this->createEntry(
            [
                'email'  => $admin->email,
                'userId' => $admin->id,
                'name'   => $admin->name,
            ]
        );
    }

    /**
     * Send Email
     * 
     * @param array $data
     */
    private function sendEmail(array $data)
    {
        Mail::to($data['email'])->queue(new AdminInvitationRegistrationMail($data));
    }

}
