<?php

namespace App\Modules\SecretManager;

use Google\Cloud\SecretManager\V1\SecretManagerServiceClient;
// use Illuminate\Support\Facades\Http;
use GuzzleHttp;

class RemoteSecretManager implements SecretManagerInterface
{
    private $client;

    private $http;

    private string $GOOGLE = 'Google';

    private string $METADATA_FLAVOR = 'Metadata-Flavor';

    private string $PROJECT_ID = 'PROJECT_ID';

    private string $METADATA_ROOT_URI = 'http://metadata.google.internal/computeMetadata/v1/instance/attributes/';

    public function __construct()
    {

        $this->client = new SecretManagerServiceClient();
        $this->http = new GuzzleHttp\Client(['base_uri' => $this->METADATA_ROOT_URI, 'headers' => [$this->METADATA_FLAVOR => $this->GOOGLE]]);
    }

    public function getPayload(string $key): string
    {
        $name = $this->client->secretVersionName($this->getMeta($this->PROJECT_ID), $this->getMeta($key), 'latest');
        $response = $this->client->accessSecretVersion($name);

        return $response->getPayload()->getData();
    }

    public function getMeta(string $metadata): string
    {
        return $this->http->request('GET', $metadata)->getBody();
    }
}
