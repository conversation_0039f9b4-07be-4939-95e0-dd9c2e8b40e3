import React, { forwardRef, useEffect, useRef } from "react";

export default forwardRef(function TextArea(
    {
        type = "text",
        name,
        value,
        className,
        autoComplete,
        required,
        isFocused,
        handleChange,
        placeholder,
        rows = 2,
        cols = 50,
        maxLength = 500,
        readOnly = false, 
        isDisabled = false, 
    },
    ref
) {
    const input = ref ? ref : useRef();

    if (isFocused)
    {
        useEffect(() => {
            if (isFocused) {
                input.current.focus();
            }
        }, []);
    }

    return (
        <div className="flex flex-col items-start">
            <textarea
                maxLength={maxLength}
                placeholder={placeholder}
                rows={rows}
                cols={cols}
                type={type}
                name={name}
                value={value}
                className={
                    `border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-md shadow-sm placeholder-gray-200 ` +
                    className + ` ${isDisabled == true ? 'bg-slate-100' : ''}`
                }
                ref={input}
                autoComplete={autoComplete}
                required={required}
                readOnly={readOnly}
                disabled={isDisabled}
                onChange={(e) => handleChange(e)}
            />
        </div>
    );
});
