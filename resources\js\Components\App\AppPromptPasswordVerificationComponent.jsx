//* PACKAGES 
import React, { Fragment, useRef, useState, useEffect } from "react";
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";
import { Dialog, Transition } from '@headlessui/react';

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";
import { AiFillEyeInvisible, AiOutlineEye } from "react-icons/ai";

//* COMPONENTS
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* UTILS 
import { evaluate } from "@/Util/AxiosResponseHandler";

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...


export default function AppPromptPasswordVerificationComponent(
    {
        //! VARIABLES
        children,
        className,
        show = false,
        maxWidth = 'md',
        closeable = true,
        overflow = 'overflow-hidden',
        
        //! STATES
        //...
        
        //! EVENTS
        onSubmitSuccess = () => {},
        onSubmitError   = () => {},
        onClose         = () => {}
    }
)
{
    //! VARIABLES 
    //...

    //! STATES 
    const maxWidthClass =
    {
        sm: 'sm:max-w-sm',
        md: 'sm:max-w-md',
        lg: 'sm:max-w-lg',
        xl: 'sm:max-w-xl',
        '2xl': 'sm:max-w-2xl',
        '3xl': 'sm:max-w-3xl', 
        '4xl': 'sm:max-w-4xl',
        '5xl': 'sm:max-w-5xl',
        '6xl': 'sm:max-w-6xl'
    }[maxWidth];
    
    const [stateInputPassword, setStateInputPassword]           = useState('');
    const [stateInputPasswordError, setStateInputPasswordError] = useState('');
    const [stateShowPassword, setStateShowPassword]             = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleSelfClose()
    {
        setStateInputPassword('');
        setStateInputPasswordError('')
        setStateShowPassword(false);
        
        //* CLOSE MODAL
        onClose();
    }
    
    async function handleOnSubmit()
    {
        setStateInputPasswordError('')

        try
        {
            const data =
            {
                password: stateInputPassword
            }; 

            let response = await axios
                .post(
                    route("admin-verification.password"),
                    data
                ); 
                        
            if (response.status == 200)
            {   
                handleSelfClose();
                onSubmitSuccess();
            }
        }
        catch (error)
        {
            if (error.status == 422)
            {
                setStateInputPasswordError(error.response.data.errors.password);
            }

            onSubmitError();
        }
    }

    return (
        <Transition
            show={show}
            as={Fragment}
            leave="duration-200"
        >
            <Dialog
                as="div"
                id="modal"
                className="fixed inset-0 flex overflow-y-auto px-4 py-6 sm:px-0 items-center z-50 transform transition-all w-full"
                onClose={close}
            >
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="absolute inset-0 bg-gray-500/75" />
                </Transition.Child>

                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    enterTo="opacity-100 translate-y-0 sm:scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                    leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                    <Dialog.Panel
                        className={`mb-6 bg-white rounded-lg shadow-xl transform transition-all sm:w-full sm:mx-auto ${maxWidthClass} ${overflow} ${className}`}
                    >
                        <div
                            className="flex flex-col justify-around gap-y-10 pt-5 pb-10 px-10"
                        >
                            <div
                                className="flex flex-col"
                            >
                                <div
                                    className="flex justify-end"
                                >
                                    <button
                                        type="button"
                                        className="text-primary ease-in-out duration-100 hover:text-blue-900"
                                        onClick={handleSelfClose}
                                    >
                                        <IoMdCloseCircle
                                            className="h-8 w-8 "
                                        />
                                    </button>
                                </div>
                                <div
                                    className="flex flex-col gap-y-5 select-none"
                                >
                                    <div
                                        className="text-center"
                                    >
                                        <span
                                            className="p-3 bg-primary rounded-sm text-white font-bold text-3xl"
                                        >
                                            SD
                                        </span>
                                    </div>
                                    <div
                                        className="block text-center text-gray-700 text-xl"
                                    >
                                        Security Verification
                                    </div>
                                    <div
                                        className="grow text-center"
                                    >
                                        Enter your Password to Continue
                                    </div>
                                </div>
                            </div>
                            <div
                                className="flex flex-col gap-y-8 grow"
                            >
                                <div
                                    className="flex flex-col gap-y-5"
                                >
                                    <div className="relative">
                                        <TextInput
                                            type={stateShowPassword ? "text" : "password"}
                                            value={stateInputPassword}
                                            name="custom-password-field"
                                            placeholder="Password"
                                            className="w-full"
                                            autoComplete="current-password"
                                            handleChange={(e) => setStateInputPassword(e.currentTarget.value)}
                                        />
                                        <div className="absolute top-2 right-5">
                                            <i
                                                onClick={() => setStateShowPassword(!stateShowPassword)}
                                                className="text-xl cursor-pointer text-primary hover:text-gray-500"
                                            >
                                                {
                                                    stateShowPassword
                                                        ?
                                                        <AiFillEyeInvisible
                                                            className='h-6 w-6'
                                                        />
                                                        :
                                                        <AiOutlineEye
                                                            className='h-6 w-6'
                                                        />
                                                }
                                            </i>
                                        </div>
                                    </div>
                                    <InputError
                                        className="text-center"
                                        message={stateInputPasswordError}
                                    />
                                </div>
                                <PrimaryButton
                                    type="button"
                                    className="w-full select-none"
                                    processing={stateInputPassword.length == 0}
                                    onClick={handleOnSubmit}               
                                >
                                    Verify
                                </PrimaryButton>
                            </div>
                        </div>
                    </Dialog.Panel>
                </Transition.Child>
            </Dialog>
        </Transition>
    );
}
