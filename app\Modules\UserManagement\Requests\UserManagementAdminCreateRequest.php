<?php

namespace App\Modules\UserManagement\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserManagementAdminCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'          => ['required', 'min:2', 'max:100'],
            'email'         => ['required', 'email:rfc,dns', 'max:100', 'unique:admins,email'],
            'roleId'        => ['nullable', 'exists:roles,id'],
            'permissions'   => ['required', 'array'],
            'permissions.*' => ['integer']
        ];
    }

    public function messages()
    {
        return [
            'permissions.required' => 'Please select at least one permission.',
        ];
    }
}
