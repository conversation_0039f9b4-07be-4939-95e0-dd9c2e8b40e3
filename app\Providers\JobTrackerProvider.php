<?php

namespace App\Providers;

use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\ServiceProvider;

class JobTrackerProvider extends ServiceProvider
{
    private const DISPATCH = 'dispatch';

    private const FAILED = 'failed';

    private const SUCCESS = 'success';

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {

        Queue::before(function (JobProcessing $event) {

            $payload = [
                'connection' => $event->job->getConnectionName(),
                'queue' => $event->job->getQueue(),
                'type' => self::DISPATCH,
            ];
            $this->trackJob($payload);
        });

        Queue::after(function (JobProcessed $event) {
            $payload = [
                'connection' => $event->job->getConnectionName(),
                'queue' => $event->job->getQueue(),
                'type' => $event->job->hasFailed() ? self::FAILED : self::SUCCESS,
            ];
            $this->trackJob($payload);
        });
    }

    private function trackJob($payload)
    {
        $DB_SOURCE = DB::client();

        $param = ['connection' => $payload['connection'], 'queue' => $payload['queue'], 'date' => now()];

        $affected = $DB_SOURCE->table('job_trackers')->where($param)->increment($payload['type'], 1);

        if ($affected) {
            return;
        }

        $param[self::DISPATCH] = 0;
        $param[self::SUCCESS] = 0;
        $param[self::FAILED] = 0;
        $param[$payload['type']] = 1;

        $DB_SOURCE->table('job_trackers')->insert($param);

    }
}
