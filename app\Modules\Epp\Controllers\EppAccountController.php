<?php

namespace App\Modules\Epp\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Epp\Requests\AccountRequestForm;
use App\Modules\Epp\Requests\AdjustBalanceRequestForm;
use Inertia\Inertia;

class EppAccountController extends Controller
{
    public function index(AccountRequestForm $request)
    {
        return Inertia::render('Epp/Account/Index', $request->account());
    }

    public function adjustBalance(AccountRequestForm $request)
    {
        $request->validate([
            'registry' => ['required'],
        ]);

        return Inertia::render('Epp/Account/AdjustBalance', ['registry' => $request->registry, 'balance' => $request->balance()]);
    }

    public function store(AdjustBalanceRequestForm $request)
    {
        $request->store();

        return redirect()->route('epp.account', ['registry_id' => $request->registry_id]);
    }
}
