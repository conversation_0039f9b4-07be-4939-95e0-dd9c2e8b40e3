<?php

namespace App\Modules\Notification\Constants;

class NotificationStatus
{
    public const ACTIVE = 'active';
    public const DISABLED = 'disabled';
    public const EXPIRED = 'expired';
    public const PENDING = 'pending';

    public static function getStatusStyle(string $status): string
    {
        return match ($status) {
            self::ACTIVE => 'bg-green-500',
            self::DISABLED => 'bg-gray-500',
            self::EXPIRED => 'bg-red-500',
            self::PENDING => 'bg-blue-500',
            default => 'bg-gray-500'
        };
    }

    public static function toArray(): array
    {
        return [
            self::ACTIVE,
            self::DISABLED,
            self::EXPIRED,
            self::PENDING
        ];
    }
}
