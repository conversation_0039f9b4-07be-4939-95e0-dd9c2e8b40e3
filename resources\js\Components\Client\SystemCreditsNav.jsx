import React from 'react';
import NavLink from "@/Components/NavLink";

const SystemCreditNav = ({ postRouteName }) => {

    const currentRoute = route().current() || postRouteName;

    const routes = {
        credits: route().current("system-credits") || currentRoute.includes('system-credits') || (window.location.href).includes('system-credits'),
    };

    return (
        <NavLink
            href={route("system.credits")}
            active={routes.credits}
        >
            <span className="flex space-x-4">
                <span className="text-inherit">System Credits</span>
            </span>
        </NavLink>
    );
};

export default SystemCreditNav;
