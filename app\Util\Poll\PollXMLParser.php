<?php

namespace App\Util\Poll;

use App\Modules\Epp\Constants\EppPollNotificationType;
use SimpleXMLElement;

class PollXMLParser
{
    /***
     * Parse XML String
     *
     * @param String $input String to parse
     * @return Array of json data and domain name
     */
    public static function xmlToJson(string $input, string $type): array
    {
        $input = trim($input);
        if (empty($input)) {
            return '';
        }

        $xmlStr = preg_replace("/(<\/?)(\w+):([^>]*>)/", '$1$2$3', $input);
        $newXML = new SimpleXMLElement($xmlStr);

        switch ($type) {
            case EppPollNotificationType::DOMAIN_TRANSFER:
                return self::getDomaintype($newXML);
                break;
            default:
                return self::getDefaultType($newXML);
        }
    }

    private static function getDomaintype($newXML)
    {
        $json = json_encode($newXML);
        $decoded = json_decode($json, true);
        $d_array = collect($decoded);
        $keys = $d_array->keys();
        $list = [];

        foreach ($keys as $key) {
            $item = $d_array[$key];

            if (array_key_exists('@attributes', $item)) {
                $item = $item + collect($item['@attributes'])->toArray();
                unset($item['@attributes']);
            }

            if (array_key_exists('msg', $item)) {
                $message = ['message' => $item['msg']];
                $item = $item + $message;
                unset($item['msg']);
            }

            if (array_key_exists('domaintrnData', $item)) {
                $domain = collect($item['domaintrnData'])->toArray();
                $domKeys = collect($item['domaintrnData'])->keys();
                foreach ($domKeys as $d) {
                    $dKey = str_replace('domain', '', $d);
                    $domain[$dKey] = $domain[$d];
                    unset($domain[$d]);
                }
                $domArr['domain'] = $domain;
                $item = $item + $domArr;
                unset($item['domaintrnData']);
            }

            $list[$key] = $item;
        }

        // dd($list);

        $domainName = '';
        if ($list['resData']['domain']['name']) {
            $domainName = $list['resData']['domain']['name'];
        }

        return [
            'json_data' => json_encode($list),
            'name' => $domainName,
        ];
    }

    private static function getDefaultType($newXML)
    {
        $json = json_encode($newXML);
        $decoded = json_decode($json, true);
        $d_array = collect($decoded);
        $keys = $d_array->keys();
        $list = [];

        // dd($newXML);

        foreach ($keys as $key) {
            $item = $d_array[$key];

            if (array_key_exists('@attributes', $item)) {
                $item = $item + collect($item['@attributes'])->toArray();
                unset($item['@attributes']);
            }

            if (array_key_exists('msg', $item)) {
                $message = ['message' => $item['msg']];
                $item = $item + $message;
                unset($item['msg']);
            }

            $list[$key] = $item;
        }

        $domainName = '';

        return [
            'json_data' => json_encode($list),
            'name' => $domainName,
        ];
    }
}

// sample result
// $result = {
//     "result": {
//         "code": 1301,
//         "message": "Command completed successfully; ack to dequeue"
//     },
//     "msgQ":{
//         "count": "17",
//         "id": "4229314",
//         "qDate": "2023-11-23T05:38:25.000Z",
//         "message": "Transfer Cancelled."
//     },
//     "resData": {
//         "domain": {
//             "name":"FER1.COM",
//             "trStatus":"clientCancelled",
//             "reID": "29000487",
//             "reDate":"2023-11-23T05:38:14.000Z",
//             "acID":"strange1",
//             "acDate": "2023-11-23T05:38:25.000Z"
//         },
//     "trID": {
//         "svTRID": "1543199354-1701661958638"
//     }
//     }
// }
