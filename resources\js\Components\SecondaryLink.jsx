import React from "react";
import { Link } from "@inertiajs/react";
export default function SecondaryLink({
    href = "#",
    className = "",
    method = "get",
    children,
    processing,
    shouldPreserveState = false
}) {
    return (
        <Link
            preserveState={shouldPreserveState}
            as="button"
            href={href}
            method={method}
            className={
                `inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150 ${processing && "opacity-25"
                } ` + className
            }
            disabled={processing}
        >
            {children}
        </Link>
    );
}
