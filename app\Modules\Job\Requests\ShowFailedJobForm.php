<?php

namespace App\Modules\Job\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Job\Constants\ConnectionSource;
use App\Modules\Job\Constants\JobConnection;
use App\Modules\Job\Services\FailedJobService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowFailedJobForm extends FormRequest
{
    private $connections = [];

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'integer', Rule::exists("{$this->source}.failed_jobs", 'id')],
            'source'   => ['required', 'string', Rule::in(ConnectionSource::all())],
            'connection' => ['required', 'string', Rule::in($this->connections)],
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->has('source')) {
            $this->connections = array_column(JobConnection::getJobs($this->source), 'table');
        }
    }

    // public function index()
    // {
    //     $jobs = $this->has('source') ? FailedJobService::getFailedByJob($this->source, $this->connection) : [];

    //     return $jobs;
    // }

    public function getById()
    {
        if (! $this->has('source')) {
            throw new FailedRequestException(400, 'No source has been selected', 'Bad Request');
        }

        return FailedJobService::get($this->source, $this->id);
    }
}
