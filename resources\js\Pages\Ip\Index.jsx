import Checkbox from "@/Components/Checkbox";
import Item from "@/Components/Ip/Item";
import SecondaryButton from "@/Components/SecondaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import { getEventValue } from "@/Util/TargetInputEvent";
import { useState } from "react";
import { router } from "@inertiajs/react";
import {
    MdArrowBackIos,
    MdArrowForwardIos,
    MdOutlineSettings,
    MdOutlineSortByAlpha,
    MdOutlineSwapVert,
} from "react-icons/md";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoaderSpinner from "@/Components/LoaderSpinner";

export default function Index({
    items,
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "item",
}) {
    const STATUS_TYPE = {
        ALL: "ALL",
        ALLOWED: "ACTIVE",
        BLOCKED: "BLOCKED",
    };

    const defaultStatus = () => {
        const { is_active } = route().params;

        if (is_active == undefined) return STATUS_TYPE.ALL;
        else if (is_active == 1) return STATUS_TYPE.ALLOWED;
        else return STATUS_TYPE.BLOCKED;
    };

    const callSortName = () => {
        const { is_active, orderby } = route().params;
        // console.log(orderby);

        let str = "";
        if (orderby == "name:asc") {
            str = "name:desc";
        } else {
            str = "name:asc";
        }

        let payload = {};
        payload.is_active = is_active;
        payload.orderby = str;

        router.get(route("ip"), payload);
    };

    const [status, setStatus] = useState(defaultStatus());
    const [selectAll, setSelectAll] = useState(false);
    const [selectedItems, setSelectedItems] = useState([]);

    const handleStatusChange = (statusType) => {
        const { orderby } = route().params;
        let payload = {};

        setSpinner(true);

        setStatus(statusType);
        if (statusType === "ALL") {
            payload = {};
        } else {
            payload = {
                is_active: statusType == STATUS_TYPE.ALLOWED ? 1 : 0, //boolean validation does not accept true or false ?
            };
        }

        payload.orderby = orderby;
        router.get(route("ip"), payload);
    };

    const handleSelectAllChange = (e) => {
        const checked = getEventValue(e);
        setSelectAll(checked);

        setSelectedItems(checked ? items.map((item) => item.id) : []);
    };

    const handleItemCheckboxChange = (itemId, itemStatus, checked) => {
        setSelectedItems((prevSelectedItems) => {
            return checked
                ? [...prevSelectedItems, itemId]
                : prevSelectedItems.filter((id) => id !== itemId);
        });

        let temp = [...selectedItems];

        if (temp.includes(itemId)) {
            temp = temp.filter((e) => e != itemId);
        } else {
            temp.push(itemId);
        }

        temp.length == items.length ? setSelectAll(true) : setSelectAll(false);
    };

    const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

    const handleResponse = async (status) => {
        toast.info("Updating IP Status.");
        router.patch(route("ip.update-status"), {
            ids: selectedItems,
            is_active: status,
        });

        await sleep(500);
        setSelectedItems([]);
        setSelectAll(false);
    };

    const [hasSpinner, setSpinner] = useState(false);

    router.on("start", () => {
        setSpinner(true);
    });

    router.on("finish", () => {
        setSpinner(false);
    });

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">
                <div className="flex items-center space-x-4 justify-end">
                    <SecondaryButton
                        onClick={() => handleResponse(true)}
                        processing={
                            selectedItems.length == 0 ||
                            status == STATUS_TYPE.ALLOWED
                        }
                    >
                        Allow
                    </SecondaryButton>
                    <SecondaryButton
                        onClick={() => handleResponse(false)}
                        processing={
                            selectedItems.length == 0 ||
                            status == STATUS_TYPE.BLOCKED
                        }
                    >
                        Block
                    </SecondaryButton>
                </div>
                <div className="flex items-center flex-wrap cursor-pointer border-b text-default">
                    <div
                        onClick={() => handleStatusChange(STATUS_TYPE.ALL)}
                        className={`px-5 py-1 rounded-sm ${status == STATUS_TYPE.ALL
                            ? "bg-gray-100 text-gray-700"
                            : "hover:bg-gray-100 hover:text-link"
                            }`}
                    >
                        <span className=" text-inherit">All</span>
                    </div>
                    <div
                        onClick={() => handleStatusChange(STATUS_TYPE.ALLOWED)}
                        className={`px-5 py-1 rounded-sm ${status == STATUS_TYPE.ALLOWED
                            ? "bg-gray-100 text-gray-700"
                            : "hover:bg-gray-100 hover:text-link"
                            }`}
                    >
                        <span className=" text-inherit">Allowed</span>
                    </div>
                    <div
                        onClick={() => handleStatusChange(STATUS_TYPE.BLOCKED)}
                        className={`px-5 py-1 rounded-sm ${status == STATUS_TYPE.BLOCKED
                            ? "bg-gray-100 text-gray-700"
                            : "hover:bg-gray-100 hover:text-link"
                            }`}
                    >
                        <span className=" text-inherit">Blocked</span>
                    </div>
                </div>
                <div>
                    <table className="min-w-full text-left border-spacing-y-2.5 border-separate ">
                        <thead className=" bg-gray-50 text-sm">
                            <tr>
                                <th>
                                    <label className="flex items-center pl-2 space-x-2">
                                        {items.length === 0 ?
                                            <Checkbox
                                                disabled={true}
                                                className="pointer-events-none opacity-0"
                                            /> :
                                            <Checkbox
                                                name="select_all"
                                                value="select_all"
                                                checked={selectAll}
                                                handleChange={handleSelectAllChange}
                                            />
                                        }
                                        <span className="">IP Address</span>
                                        <button
                                            onClick={() => callSortName()}
                                            disabled={items.length === 0}
                                        >
                                            <MdOutlineSwapVert />
                                        </button>
                                    </label>
                                </th>
                                <th>
                                    <span>Status</span>
                                </th>
                                <th>
                                    <span>Updated</span>
                                </th>
                                <th>
                                    <span>Created</span>
                                </th>

                                <th>
                                    <span className="text-xl">
                                        <MdOutlineSettings />
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody className="text-sm">
                            {hasSpinner ? (
                                <tr>
                                    <td colSpan={7}>
                                        <div className="mx-auto container mt-8 flex flex-col px-28 rounded-lg"><LoaderSpinner ml='ml-96' h='h-12' w='w-12' position='absolute' /><br /><span className="relative top-9 left-72 ml-20">Loading Data...</span></div>
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {Object.values(items).map((item) => {
                                        return (
                                            <Item
                                                item={item}
                                                key={"ip-" + item.id}
                                                isSelected={selectedItems.includes(
                                                    item.id
                                                )}
                                                onCheckboxChange={
                                                    handleItemCheckboxChange
                                                }
                                            />
                                        );
                                    })}
                                </>
                            )}
                        </tbody>
                    </table>
                </div>
                {hasSpinner ? " " :
                    (
                        <>
                            <CursorPaginate
                                onFirstPage={onFirstPage}
                                onLastPage={onLastPage}
                                nextPageUrl={nextPageUrl}
                                previousPageUrl={previousPageUrl}
                                itemCount={itemCount}
                                total={total}
                                itemName={itemName}
                            />
                        </>
                    )
                }
            </div>
        </AdminLayout>
    );
}
