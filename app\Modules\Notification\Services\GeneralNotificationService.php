<?php

namespace App\Modules\Notification\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use App\Models\ScheduleNotification;
use App\Modules\Notification\Constants\NotificationStatus;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class GeneralNotificationService
{
    public function getAllUsers(): Collection
    {
        return DB::client()->table('users')
            ->select(['id', 'email'])
            ->get();
    }

    public function createScheduledNotifications(array $data): bool
    {
        $userIds = $this->getUserIds($data['preview_emails'] ?? []);
        if (empty($userIds)) {
            throw new Exception('No valid users found for notification.');
        }

        $notifications = $this->prepareNotificationsData($data, $userIds);

        if (empty($notifications)) {
            throw new Exception('Failed to prepare notification data.');
        }

        foreach (array_chunk($notifications, 100) as $chunk) {
            DB::client()->table('schedule_notifications')->insert($chunk);
        }
        
        return true;
    
    }

    private function getUserIds(array $emails): array
    {
        return DB::client()->table('users')
            ->whereIn('email', $emails)
            ->pluck('id')
            ->toArray();
    }

    private function prepareNotificationsData(array $data, array $userIds): array
    {
        $notifications = [];
        $startDates = $this->getStartDatesForScheduleType($data);

        foreach ($userIds as $userId) {
            foreach ($startDates as $startDate) {
                $notifications[] = $this->buildNotificationData($data, $userId, $startDate);
            }
        }

        event(new AdminActionEvent( auth()->user()->id, HistoryType::NOTIFICATION_CREATED, 'Announcement Notification created ' . 'Title: ' . $data['title'] . ' by '. auth()->user()->email ));
        return $notifications;
    }

    private function getStartDatesForScheduleType(array $data): array
    {
        return match ($data['schedule_type']) {
            'weekly' => $this->getWeeklyDates($data['weekday']),
            'monthly' => [$this->getMonthlyDate($data['day_of_month'])],
            'yearly' => [$this->getYearlyDate($data['month'], $data['day_of_month'])],
            'one-time' => [$this->getOneTimeDate($data['start_date'])],
            default => throw new Exception('Invalid schedule type.')
        };
    }

    private function buildNotificationData(array $data, int $userId, string $startDate): array
    {
        $now = Carbon::now();
        
        return [
            'user_id' => $userId,
            'title' => $data['title'],
            'message' => $data['message'],
            'link_name' => $data['link_name'],
            'redirect_url' => $data['redirect_url'],
            'type' => $data['type'],
            'status' => NotificationStatus::PENDING,
            'schedule_type' => $data['schedule_type'],
            'time' => $data['time'],
            'start_date' => $startDate,
            'min_registration_period' => $data['min_registration_period'] ?? null,
            'max_registration_period' => $data['max_registration_period'] ?? null,
            'expiration' => $data['expiration'] ?? null,
            'created_at' => $now,
            'updated_at' => $now,
        ];
    }

    private function getWeeklyDates(array $weekdays): array
    {
        $dates = [];
        $startOfWeek = Carbon::now()->startOfWeek();

        foreach ($weekdays as $day) {
            $date = $startOfWeek->copy()->addDays(intval($day));
            if ($date->isPast()) {
                $date->addWeek();
            }
            $dates[] = $date->format('Y-m-d');
        }

        return $dates;
    }

    private function getOneTimeDate(string $startDate): string
    {
        return Carbon::parse($startDate)->format('Y-m-d');
    }

    private function getMonthlyDate(int|string $dayOfMonth): string
    {
        $day = intval($dayOfMonth);
        if ($day < 1 || $day > 31) {
            throw new Exception('Invalid day of month for monthly schedule.');
        }

        $date = Carbon::now()->setDay($day);
        if ($date->isPast()) {
            $date->addMonth();
        }

        return $date->format('Y-m-d');
    }

    private function getYearlyDate(int|string $month, int|string $day): string
    {
        $date = Carbon::now()
            ->setMonth(intval($month))
            ->setDay(intval($day));

        if ($date->isPast()) {
            $date->addYear();
        }

        return $date->format('Y-m-d');
    }
}