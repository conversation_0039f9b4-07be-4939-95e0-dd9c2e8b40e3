<?php

namespace App\Modules\Dashboard\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Activity\Services\AdminActivityService;
use App\Modules\Dashboard\Requests\ShowLogRequest;
use App\Modules\DomainHistory\Services\DomainHistoryService;
use App\Modules\Client\Services\SecurityLogService;
use Inertia\Inertia;

class DashboardController extends Controller
{
    
    protected $domainHistoryService;
    protected $securityLogService;

    public function __construct(DomainHistoryService $domainHistoryService, SecurityLogService $securityLogService)
    {
        $this->domainHistoryService = $domainHistoryService;
        $this->securityLogService = $securityLogService;
    }
    public function index(ShowLogRequest $request, ?int $userId = null)
    {
        $data = $this->domainHistoryService->prepareDomainHistoryData($request);
        $activityLogs[] = $this->securityLogService->getSecurityLogsData($userId, $request);
        return Inertia::render('Dashboard/DashboardIndex',  ['domains' => $data, 'logs' => $activityLogs, 'transactionTypes'=> $data['transactionTypes']]); 
    }
}
