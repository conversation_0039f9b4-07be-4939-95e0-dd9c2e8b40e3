<?php

namespace App\Modules\AdminCredit\Services;

use App\Models\AdminCredits;
use App\Modules\AdminCredit\Constants\AdminCreditType;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Error;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;

class AdminCreditService
{
    use CursorPaginate;

    private $pageLimit = 50;

    public static function instance(): self
    {
        $adminCreditService = new self;

        return $adminCreditService;
    }

    public function getHistory()
    {
        $builder = DB::client()->table('system_credits')
            ->orderBy('id', 'desc')
            ->paginate($this->pageLimit);

        return CursorPaginate::cursor($builder);
    }

    public function store(array $request, $userId = null)
    {
        $runningBalance = $this->getCurrentRunningBalance($request);

        $data = [
            'running_balance' => $runningBalance,
            'type' => $request['type'] ?? '',
            'amount' => $request['amount'] ?? 0,
            'note' => $request['description'],
            'created_at' => Carbon::now()
        ];

        $latestBlock = AdminCreditBlockService::instance()->addBlock($data, Auth::id() ?? 0);

        // AdminCreditBlockService::instance()->addBlock($data, Auth::id() ?? 0);
        $data['block_id'] = $latestBlock->id;
        $data['user_id'] = $userId;

        return DB::client()->table('system_credits')->insertGetId($data);
    }

    public function balance()
    {
        return DB::client()->table('system_credits')->orderBy('id', 'desc')->first();
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getPreviousBalance()
    {
        $previousCredit = DB::client()->table('system_credits')
            ->orderBy('id', 'desc')
            ->first();

        return $previousCredit->running_balance ?? 0;
    }

    private function getCurrentRunningBalance(array $request)
    {
        $previousBalance = $this->getPreviousBalance();
        $amount = $request['amount'] ?? 0;
        $runningBalance = $request['type'] == AdminCreditType::DEBIT ?
            $this->debit($previousBalance, $amount) :
            $this->credit($previousBalance, $amount);

        return $runningBalance;
    }

    private function credit($previousBalance, $amount): float
    {
        $this->validateBalance($previousBalance, $amount);

        $total = floatval($previousBalance) - floatval($amount);

        $validator = Validator::make(['total' => $total], ['total' => 'gte:0.00',]);
        if ($validator->fails()) throw  ValidationException::withMessages(['message' => ['Error: Negative credit']]);

        event(new AdminActionEvent(
            auth()->user()->id,
            HistoryType::PAYMENT_CREATED,
            "System Credit Debit: $" . number_format($amount, 2) . "$ by " . auth()->user()->email
        ));

        return round($total, 2);
    }

    private function debit($previousBalance, $amount): float
    {
        $this->validateBalance($previousBalance, $amount);

        $total = floatval($previousBalance) + floatval($amount);

        return round($total, 2);
    }

    private function validateBalance($previousBalance, $amount)
    {
        $validator = Validator::make(['balance' => $previousBalance, 'amount' => $amount], [
            'balance' => 'required|numeric',
            'amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $validator;
        }
    }
}
