import React from "react";

export default function PrimaryButton({
    type = "submit",
    className = "",
    processing,
    children,
    onClick,
}) {
    return (
        <button
            type={type}
            onClick={onClick}
            className={
                `block px-4 py-2 bg-primary border border-transparent rounded-md font-semibold text-sm 
                text-white tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 
                focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition 
                ease-in-out duration-150 ${processing && "opacity-25"} ${className}`
            }
            disabled={processing}
        >
            {children}
        </button>
    );
}
