import React, { forwardRef, useEffect, useRef } from "react";

export default forwardRef(function TextInput(
    {
        maxLength = 50,
        type = "text",
        name,
        value,
        className,
        autoComplete,
        required,
        isFocused,
        handleChange,
        placeholder,
        disabled,
        min,
        max,
        step="any",
    },
    ref
) {
    const input = ref ? ref : useRef();

    useEffect(() => {
        if (isFocused) {
            input.current.focus();
        }
    }, []);

    return (
        <div className="flex flex-col items-start">
            <input
                maxLength={maxLength}
                placeholder={placeholder}
                type={type}
                name={name}
                value={value}
                className={
                    `disabled:bg-gray-100 disabled:cursor-not-allowed border-gray-300 focus:border-gray-500 focus:ring-gray-500 rounded-md shadow-sm placeholder-gray-200 ` +
                    className
                }
                ref={input}
                autoComplete={autoComplete}
                required={required}
                onChange={(e) => handleChange(e)}
                disabled={disabled}
                min={min}
                max={max}
                step={step}
            />
        </div>
    );
});
