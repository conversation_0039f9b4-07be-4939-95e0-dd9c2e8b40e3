<?php

namespace App\Modules\UserManagement\Constants;

final class UserManagementPermissionConstants
{
        /**
         * @var array
         */
        public const PERMISSIONS_EXCLUDED =
        [
            "sanctum.csrf-cookie",
            "telescope",
            "ignition.healthCheck",
            "ignition.executeSolution",
            "ignition.updateConfig",
            "bad-request",
            "invalid-password-reset-token",
            "home",
            "dashboard",
            "page403",
            "admin-registration.setup",
            "admin-registration.register",
            "admin-verification.password",
            "register",
            "login",
            "password.request",
            "password.email",
            "password.reset",
            "password.store",
            "verification.notice",
            "verification.verify",
            "verification.send",
            "password.confirm",
            "password.update",
            "logout",
            "notification",
            "notification.update.read",
            "notification.unread.count",
            "notification.dropdown.data",
            "profile.edit",
            "profile.update",
            "profile.destroy",
            "fake.route",
        ];

        /**
         * @var array
         */
        public const PERMISSIONS_HIDDEN =
        [
            "user-management.admin",
            "user-management.admin.create",
            "user-management.admin.edit",
            "user-management.admin.store",
            "user-management.admin.view-permissions",
            "user-management.admin.invitation-resend",
            "user-management.admin.update",
            "user-management.admin.enable",
            "user-management.admin.disable",
            "user-management.admin.delete",
            "user-management.role",
            "user-management.role.create",
            "user-management.role.edit",
            "user-management.role.store",
            "user-management.role.view-permissions",
            "user-management.role.view-role-permissions",
            "user-management.role.update",
            "user-management.role.delete",
            "user-management.role.bulk-delete",
            "user-management.category",
            "user-management.category.create",
            "user-management.category.edit",
            "user-management.category.store",
            "user-management.category.view-permissions",
            "user-management.category.update",
            "user-management.category.delete",
            "user-management.category.bulk-delete",
            "user-management.settings",
            "user-management.settings.sync-permissions",
            "user-management.settings.category.load-defaults",
            "user-management.settings.role.load-defaults",
        ];
}
