<?php

namespace App\Modules\PendingDelete\Services;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Modules\PendingDelete\Services\PendingDeleteService;

class RedemptionPeriodQueryService
{
    public static function instance(): self
    {
        return new self();
    }

    public function getDomainsInRedemptionPeriod(int $daysExpired): Collection
    {
        $cutoffTimestamp = Carbon::now()->subDays($daysExpired)->getTimestampMs();

        $query = DB::client()->table('pending_domain_deletions')
            ->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->whereNull('pending_domain_deletions.deleted_at')
            ->where('domains.expiry', '<=', $cutoffTimestamp)
            ->whereNotNull('domains.expiry')
            ->select(
                'pending_domain_deletions.id',
                'domains.name',
                'domains.expiry'
            );

        return $query->get();
    }

    public function processExpiredDomains(int $daysExpired): array
    {
        $expiredDomains = $this->getDomainsInRedemptionPeriod($daysExpired);

        if ($expiredDomains->isEmpty()) {
            return ['count' => 0, 'domains' => []];
        }

        $pendingDeletionIds = $expiredDomains->pluck('id')->toArray();
        $domainNames = $expiredDomains->pluck('name')->toArray();

        PendingDeleteService::instance()->delete($pendingDeletionIds);

        return [
            'count' => count($pendingDeletionIds),
            'domains' => $domainNames
        ];
    }
}
