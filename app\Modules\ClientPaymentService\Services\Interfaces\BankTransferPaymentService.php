<?php

namespace App\Modules\ClientPaymentService\Services\Interfaces;

use App\Modules\ClientPaymentService\Constants\PaymentServiceType;
use App\Modules\ClientPaymentService\Contracts\PaymentServiceInterface;
use Illuminate\Support\Facades\DB;

class BankTransferPaymentService implements PaymentServiceInterface
{
    public function getPaymentService(object $paymentService)
    {
        $data = DB::client()->table('payment_services')
            ->join('bank_transfers', 'bank_transfers.id', '=', 'payment_services.bank_transfer_id')
            ->join('payment_summaries', 'payment_summaries.payment_service_id', '=', 'payment_services.id')
            ->where('payment_services.id', $paymentService->id)
            ->where('payment_services.user_id', $paymentService->user_id)
            ->where('bank_transfers.id', $paymentService->bank_transfer_id)
            ->select(
                'payment_services.*',
                'bank_transfers.account_name',
                'bank_transfers.verified_at',
                'bank_transfers.gross_amount',
                'bank_transfers.net_amount',
                'bank_transfers.service_fee',
                'bank_transfers.company',
                'bank_transfers.note',
                'payment_summaries.paid_amount as paid_amount',
                'payment_summaries.total_amount as total_amount',
                'payment_summaries.name as summary_name',
                'payment_summaries.type as summary_type',
            )->get()->first();

        $data->payment_service_type = PaymentServiceType::BANK_TRANSFER;

        return $data;
    }
}
