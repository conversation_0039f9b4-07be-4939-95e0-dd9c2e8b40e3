<?php

namespace App\Modules\Domain\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\DomainClientHoldRequest;
use App\Modules\Domain\Requests\DomainClientUnholdRequest;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;

class DomainClientHoldController extends Controller
{
    public function update(DomainClientHoldRequest $request): RedirectResponse
    {
        $result = $request->handle();

        if ($result['status'] === 'error') {
            return redirect()->back()->with('error', $result['message'] ?? 'Failed to apply client hold status');
        }

        return redirect()->back()->with('success', 'Client hold status update has been queued');
    }

    public function remove(DomainClientUnholdRequest $request): RedirectResponse
    {
        $result = $request->handle();

        if ($result['status'] === 'error') {
            return redirect()->back()->with('error', $result['message'] ?? 'Failed to remove client hold status');
        }

        return redirect()->back()->with('success', 'Client hold status removal has been queued');
    }
}