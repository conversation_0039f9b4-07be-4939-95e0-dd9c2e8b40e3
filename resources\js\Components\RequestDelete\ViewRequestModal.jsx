import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { MdClose } from "react-icons/md";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";

export default function ViewRequestModal({ isOpen, onClose, deletionRequest }) {
    const [activeTab, setActiveTab] = useState("request");

    if (!deletionRequest) return null;

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-30" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
                                {/* Header */}
                                <div className="flex items-center justify-between p-6 border-b">
                                    <Dialog.Title
                                        as="h3"
                                        className="text-xl font-bold leading-6 text-gray-900"
                                    >
                                        {deletionRequest.domainName}
                                    </Dialog.Title>
                                    <button
                                        onClick={onClose}
                                        className="text-gray-400 hover:text-gray-600"
                                    >
                                        <MdClose className="w-6 h-6" />
                                    </button>
                                </div>

                                {/* Tabs */}
                                <div className="border-b border-gray-200">
                                    <nav className="-mb-px flex">
                                        <button
                                            onClick={() =>
                                                setActiveTab("request")
                                            }
                                            className={`py-2 px-4 border-b-2 font-medium text-sm ${
                                                activeTab === "request"
                                                    ? "border-blue-500 text-blue-600 bg-blue-50"
                                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                            }`}
                                        >
                                            Request Details
                                        </button>
                                        <button
                                            onClick={() =>
                                                setActiveTab("feedback")
                                            }
                                            className={`py-2 px-4 border-b-2 font-medium text-sm ${
                                                activeTab === "feedback"
                                                    ? "border-blue-500 text-blue-600 bg-blue-50"
                                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                            }`}
                                        >
                                            Admin Feedback
                                        </button>
                                    </nav>
                                </div>

                                {/* Tab Content */}
                                <div className="p-6">
                                    {activeTab === "request" && (
                                        <div className="space-y-6">
                                            <div className="grid grid-cols-2 gap-6">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Client Name:
                                                    </label>
                                                    <p className="text-gray-900">
                                                        {
                                                            deletionRequest.first_name
                                                        }{" "}
                                                        {
                                                            deletionRequest.last_name
                                                        }
                                                    </p>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Client Email:
                                                    </label>
                                                    <p className="text-gray-900">
                                                        {deletionRequest.email}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-2 gap-6">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Client Domain:
                                                    </label>
                                                    <p className="text-gray-900">
                                                        {
                                                            deletionRequest.domainName
                                                        }
                                                    </p>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Request Date:
                                                    </label>
                                                    <p className="text-gray-900">
                                                        {setDefaultDateFormat(
                                                            deletionRequest.requested_at
                                                        )}
                                                    </p>
                                                </div>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Reason for Deletion:
                                                </label>
                                                <div className="bg-gray-50 p-4 rounded-lg">
                                                    <p className="text-gray-900 whitespace-pre-wrap">
                                                        {deletionRequest.reason ||
                                                            "No reason provided"}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {activeTab === "feedback" && (
                                        <div className="space-y-6">
                                            <div className="grid grid-cols-2 gap-6">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Support Agent:
                                                    </label>
                                                    <p className="text-gray-900">
                                                        {deletionRequest.support_agent_name ||
                                                            "Not assigned"}
                                                    </p>
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Feedback Date:
                                                    </label>
                                                    <p className="text-gray-900">
                                                        {deletionRequest.feedback_date
                                                            ? setDefaultDateFormat(
                                                                  deletionRequest.feedback_date
                                                              )
                                                            : deletionRequest.deleted_at
                                                            ? setDefaultDateFormat(
                                                                  deletionRequest.deleted_at
                                                              )
                                                            : "No Provided"}
                                                    </p>
                                                </div>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Note:
                                                </label>
                                                <div className="bg-gray-50 p-4 rounded-lg">
                                                    <p className="text-gray-900 whitespace-pre-wrap">
                                                        {deletionRequest.support_note ||
                                                            "No admin feedback provided"}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>

                                {/* Footer */}
                                <div className="flex justify-end p-6 border-t bg-gray-50">
                                    <button
                                        onClick={onClose}
                                        className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                                    >
                                        Close
                                    </button>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
