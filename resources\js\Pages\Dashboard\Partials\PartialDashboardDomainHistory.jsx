//* PACKAGES
import React, { useState, useEffect } from 'react'
import { Link, router, usePage } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import {
    FiGlobe,
    FiUsers,
    <PERSON>List,
    FiUser,
    FiFileText,
    FiClock,
} from "react-icons/fi";

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialDashboardDomainHistory(
    {
        //! PROPS
        //... 

        //! STATES 
        //...

        //! EVENTS
        //...
    }
) {
    //! PACKAGE
    //...

    //! HOOKS
    const { hasPermission } = usePermissions();

    //! VARIABLES
    const {
        domains = { data: [] },
        transactionTypes,
        logs,
    } = usePage().props;

    const dateFormat = (date) => {
        const year = new Intl.DateTimeFormat('en-GB', { year: 'numeric' }).format(date);
        const month = new Intl.DateTimeFormat('en-GB', { month: '2-digit' }).format(date);
        const day = new Intl.DateTimeFormat('en-GB', { day: '2-digit' }).format(date);
        return `${month}/${day}/${year}`;
    }

    //! STATES
    //...

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    return (
        <div className="bg-gray-100 p-4 rounded-xl w-full">
            <div className="flex items-center gap-2 mb-2">
                <FiFileText className="text-lg" />
                <h2 className="text-lg font-semibold">
                    Domain History
                </h2>
            </div>
            <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                <div className="min-w-full text-sm">
                    <table className='table-fixed w-full'>
                        <thead className="bg-white border-b font-medium text-left">
                            <tr>
                                <th className="py-2 px-2 rounded-tl-lg w-1/3">Domain</th>
                                <th className="py-2 px-2">Last Activity</th>
                                <th className="py-2 px-2 w-1/6">Last Update</th>
                                <th className="py-2 px-2 rounded-tr-lg w-1/6"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {Array.isArray(domains.domains.data) &&
                                domains.domains.data.map((log, index) => (
                                    <tr
                                        key={index}
                                    >
                                        <td className="py-2 px-2 break-words">
                                            {log.name}
                                        </td>
                                        <td className="py-2 px-2 break-words">
                                            {transactionTypes[log.type]}
                                        </td>
                                        <td className="py-2 px-2 break-words">
                                            {log.created_at
                                                ? dateFormat(new Date(log.created_at))
                                                : "Invalid Date"}
                                        </td>
                                        <td>
                                            {
                                                hasPermission('domain.history.show')
                                                    ?
                                                    <div className="rounded-md border border-[#0077a3] text-center">
                                                        <Link
                                                            href={route(
                                                                "domain.history.show",
                                                                {
                                                                    id: log.id || null,
                                                                }
                                                            )}
                                                            className="text-[#0077a3] text-xs-custom"
                                                        >
                                                            View All Logs
                                                        </Link>
                                                    </div>
                                                    :
                                                    null
                                            }
                                        </td>
                                    </tr>
                                ))}
                        </tbody>
                    </table>
                </div>
            </div>

            <div className="mt-6 flex justify-center">
                {
                    hasPermission('domain.history')
                        ?
                        <Link
                            href={route("domain.history")}
                            className="bg-[#0077a3] text-white px-5 py-2 rounded-md font-medium hover:bg-[#005f85] transition text-xs-custom"
                        >
                            View Domain History
                        </Link>
                        :
                        null
                }
            </div>
        </div>
    );
}
