import React from 'react'
import ReactSwitch from 'react-switch';

export default function ToggleSwitch({
    isChecked = false,
    isDisabled = false,
    onChangeEvent = () => { alert('test')}
}) {
    return (
        <ReactSwitch
            checked={isChecked}
            onChange={onChangeEvent}
            onColor="#147ea7"
            offColor="#ccc"
            offHandleColor="#fff"
            onHandleColor="#fff"
            disabled={isDisabled}
        />
    )
}