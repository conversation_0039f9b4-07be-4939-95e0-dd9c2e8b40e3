<?php

use App\Modules\Job\Controllers\ProcessFailedJobController;
use App\Modules\Job\Controllers\ShowFailedJobController;
use App\Modules\Job\Controllers\ShowJobController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('job')->group(function () {
    Route::get('/', [ShowJobController::class, 'index'])->name('job');
    Route::get('/view', [ShowJobController::class, 'view'])->name('job.view');
    Route::get('/data', [ShowJobController::class, 'getData'])->name('job.data');

    Route::prefix('failed')->group(function () {
        Route::get('/', [ShowFailedJobController::class, 'index'])->name('job.failed');
        Route::get('/view', [ShowFailedJobController::class, 'get'])->name('job.failed-queue');
        Route::post('/retry', [ProcessFailedJobController::class, 'retry'])->name('job.failed-retry');
        Route::post('/forget', [ProcessFailedJobController::class, 'forget'])->name('job.failed-forget');
        Route::post('/flush', [ProcessFailedJobController::class, 'flush'])->name('job.failed-flush');
    });
});
