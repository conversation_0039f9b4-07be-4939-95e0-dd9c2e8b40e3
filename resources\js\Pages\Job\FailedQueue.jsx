import AdminLayout from "@/Layouts/AdminLayout";
import { router } from "@inertiajs/react";
import { useState, useEffect } from "react";
import { MdKeyboardBackspace, MdOutlineContentCopy } from "react-icons/md";
import LoaderSpinner from "@/Components/LoaderSpinner";

export default function FailedQueue({ job }) {
    const payload = JSON.parse(job.payload);
    const exception = job.exception.split(/\#\d{1,}/);
    const [hasSpinner, setSpinner] = useState(false);
    const [copied, setCopied] = useState(false);

    useEffect(() => {
        router.on("start", () => setSpinner(true));
        router.on("finish", () => setSpinner(false));

        return () => {
            router.on("start", null);
            router.on("finish", null);
        };
    }, []);

    const handleCopy = () => {
        navigator.clipboard.writeText(job.uuid);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleBack = (e) => {
        e.preventDefault();
        window.history.back();
    };

    const renderKeyValue = (key, value) => (
        <div key={key} className="space-x-4">
            <label>{key}:</label>
            <span>{value ?? "-"}</span>
        </div>
    );

    const renderObject = (obj, render) =>
        Object.entries(obj).flatMap(([key, val]) =>
            val && typeof val === "object"
                ? renderObject(val, render)
                : render(key, val)
        );

    return (
        <AdminLayout>
            {hasSpinner && (
                <div className="fixed ml-96">
                    <LoaderSpinner h="h-12" w="w-12" />
                </div>
            )}

            <div className="mx-auto container max-w-[1000px] mt-20 px-5 flex flex-col space-y-8">
                <div className="flex flex-col space-y-4">
                    <div className="flex items-center space-x-4 text-gray-700 text-lg mb-6 relative">
                        <button
                            onClick={handleBack}
                            className="hover:bg-gray-200 rounded-full p-1 transition duration-150"
                        >
                            <MdKeyboardBackspace className="text-3xl cursor-pointer" />
                        </button>
                        <span className="font-semibold sm:text-base">{job.uuid}</span>
                        <div className="relative">
                            <button
                                onClick={handleCopy}
                                className="text-blue-600 hover:underline hover:text-blue-800"
                                title="Copy UUID"
                            >
                                <MdOutlineContentCopy className="text-lg cursor-pointer" />
                            </button>
                            <span className={`absolute -top-6 left-1/2 -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-0.5 rounded transition-all duration-300 ease-out ${copied ? "opacity-100 scale-100" : "opacity-0 scale-90 pointer-events-none"}`}>
                                Copied!
                            </span>
                        </div>
                    </div>

                    <div className="bg-gray-50 border border-gray-200 rounded-md px-6 py-4 mb-10 shadow-sm">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-12 text-sm text-gray-800">
                            <div className="space-y-1">
                                <p className="text-gray-500 text-xs uppercase tracking-wider">ID</p>
                                <p className="font-medium">{job.id}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-gray-500 text-xs uppercase tracking-wider">Connection</p>
                                <p className="font-medium">{job.connection}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-gray-500 text-xs uppercase tracking-wider">Queue</p>
                                <p className="font-medium">{job.queue}</p>
                            </div>
                            <div className="space-y-1">
                                <p className="text-gray-500 text-xs uppercase tracking-wider">Failed At</p>
                                <p className="font-medium">{job.failed_at}</p>
                            </div>
                            {payload.client_id && (
                                <div className="space-y-1 sm:col-span-2">
                                    <p className="text-gray-500 text-xs uppercase tracking-wider">Client ID</p>
                                    <p className="font-medium">{payload.client_id}</p>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                        <label className="font-semibold">Payload</label>
                        <div className="bg-gray-200 p-4 rounded-sm space-y-2 overflow-x-auto max-h-96 text-sm">
                            <span>{`{`}</span>
                            <div className="pl-6">
                                {renderObject(payload, renderKeyValue)}
                            </div>
                            <span>{`}`}</span>
                        </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                        <label className="font-semibold">Exception</label>
                        <div className="bg-gray-200 p-4 rounded-sm space-y-2 overflow-x-auto max-h-96 text-sm">
                            {exception.map((e, i) => (
                                <p key={`ex-${i}`}>{e}</p>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
