<?php

namespace App\Modules\Epp\Services;

use App\Util\Helper\HttpResponse;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class LogService
{
    private static $linePerPage = 150;

    private static $registry = [
        'verisign' => '/verisign/v1/log',
        'pir' => '/pir/v1/log',
    ];

    public static function get($registryName, $page = 1)
    {
        $path = $registryName.'/'.$page.'.log';

        return Storage::fileExists($path) ? Storage::get($path) : '';
    }

    public static function refresh($registryName)
    {
        $pageCounter = 1;

        $request = Http::registry($registryName)->get(Config::get('epp.log').'/');
        $request = HttpResponse::filled($request)['data'];

        Storage::deleteDirectory($registryName);
        Storage::makeDirectory($registryName);

        $request = explode("\n", $request);
        $pageContent = '';

        for ($i = count($request) - 1, $pageLine = self::$linePerPage; $i >= 0; $i--, $pageLine--) {
            $isCommand = trim($request[$i]);
            if (strlen($isCommand) > 0 && $isCommand[0] == '<') {
                $pageContent = $request[$i].PHP_EOL.$pageContent;
            } else {
                $hasCommand = explode('<', $request[$i], 2);
                if (count($hasCommand) == 2) {
                    $pageContent = $hasCommand[0].PHP_EOL.'<'.$hasCommand[1].PHP_EOL.$pageContent;
                } else {
                    $pageContent = $request[$i].PHP_EOL.$pageContent;
                }
            }

            if ($pageLine < 0) {
                Storage::put($registryName.'/'.$pageCounter.'.log', $pageContent);
                $pageContent = '';
                $pageCounter++;
                $pageLine = self::$linePerPage;
            }
        }

        if ($pageContent != '') {
            Storage::put($registryName.'/'.$pageCounter.'.log', $pageContent);
        }

    }
}
