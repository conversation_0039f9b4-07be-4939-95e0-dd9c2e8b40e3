import React from "react";
import { <PERSON>, router } from "@inertiajs/react";

export default function NavLink({
    href,
    active,
    children,
    Icon = "",
    className = "",
    padding="pl-8",
}) {
    return (
        <button
            className={
                `flex items-center space-x-4 hover:text-primary hover:shadow-sm ${padding}  py-1 ${
                    active && "bg-gray-100 text-primary"
                }` + className
            }
            onClick={() => router.get(href)}
        >
            {Icon}
            {children}
        </button>
        // <div
        //     className={
        //         `flex items-center space-x-4 hover:text-gray-900 hover:shadow-sm pl-8 py-1 ${
        //             active && "bg-gray-100 text-primary"
        //         }` + className
        //     }
        // >
        //     {Icon}
        //     <Link as="button" href={href}>
        //         {children}
        //     </Link>
        // </div>
    );
}
