import React from 'react';
import { Head, router } from '@inertiajs/react';
import { Md<PERSON><PERSON><PERSON><PERSON>, Md<PERSON>erson, MdEmail, MdLocationOn, MdVerified, MdAccountBalance, MdDomain } from 'react-icons/md';
import AdminLayout from '@/Layouts/AdminLayout';

export default function Detail({ client, balance, domain_count }) {
    const handleBack = () => {
        router.get(route('client'));
    };

    const formatBalance = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
        }).format(amount || 0);
    };

    const getStatusDisplay = (client) => {
        if (client?.is_active && client?.contact_setup) {
            return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Enabled</span>;
        } else if (client?.contact_setup && !client?.is_active) {
            return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Disabled</span>;
        } else if (!client?.is_active && client?.is_invited) {
            return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending Invite</span>;
        } else if (!client?.contact_setup) {
            return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Verification Required</span>;
        }
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Unknown</span>;
    };

    const formatLastActive = (lastActiveAt) => {
        if (!lastActiveAt) {
            return <span className="text-red-500 font-medium">Not Yet Active</span>;
        }
        return <span className="text-green-600 font-medium">{new Date(lastActiveAt).toLocaleDateString()}</span>;
    };

    return (
        <AdminLayout>
            <Head title={`Client Details - ${client?.name || 'Unknown Client'}`} />

            <div className="min-h-screen ">
                <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <div className="mb-8">
                        <button
                            onClick={handleBack}
                            className="inline-flex items-center  text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors duration-200"
                        >
                            <MdArrowBack className="mr-2 h-6 w-6" />
                            Back to Clients
                        </button>
                        <h1 className="mt-4 text-3xl font-bold text-gray-900">Client Details</h1>
                        <p className="mt-2 text-sm text-gray-600">Comprehensive client information and account overview</p>
                    </div>

                    {client ? (
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-white border border-gray-300 rounded-lg shadow-sm p-6">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <MdAccountBalance className="h-8 w-8 text-green-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Current Balance</p>
                                            <p className="text-2xl font-bold text-green-600">{formatBalance(balance)}</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white border border-gray-300 rounded-lg shadow-sm p-6">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <MdDomain className="h-8 w-8 text-blue-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Total Domains</p>
                                            <p className="text-2xl font-bold text-gray-900">{domain_count || 0}</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white border border-gray-300 rounded-lg shadow-sm p-6">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <MdVerified className="h-8 w-8 text-gray-600" />
                                        </div>
                                        <div className="ml-4">
                                            <p className="text-sm font-medium text-gray-600">Account Status</p>
                                            <div className="mt-1">{getStatusDisplay(client)}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div className="bg-white border border-gray-300 rounded-lg shadow-sm">
                                    <div className="px-6 py-4 border-b border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                            <MdPerson className="mr-2 h-5 w-5 text-gray-600" />
                                            Personal Information
                                        </h3>
                                    </div>
                                    <div className="px-6 py-4 space-y-4">
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">First Name</label>
                                                <p className="mt-1 text-sm text-gray-900">{client.first_name || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">Last Name</label>
                                                <p className="mt-1 text-sm text-gray-900">{client.last_name || 'N/A'}</p>
                                            </div>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Email Address</label>
                                            <p className="mt-1 text-sm text-gray-900 flex items-center">
                                                <MdEmail className="mr-2 h-4 w-4 text-gray-400" />
                                                {client.email}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Last Active</label>
                                            <p className="mt-1 text-sm">{formatLastActive(client.last_active_at)}</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-white border border-gray-300 rounded-lg shadow-sm">
                                    <div className="px-6 py-4 border-b border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                                            <MdLocationOn className="mr-2 h-5 w-5 text-gray-600" />
                                            Contact Information
                                        </h3>
                                    </div>
                                    <div className="px-6 py-4 space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Street Address</label>
                                            <p className="mt-1 text-sm text-gray-900">{client.street || 'N/A'}</p>
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">City</label>
                                                <p className="mt-1 text-sm text-gray-900">{client.city || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">State/Province</label>
                                                <p className="mt-1 text-sm text-gray-900">{client.state_province || 'N/A'}</p>
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">Postal Code</label>
                                                <p className="mt-1 text-sm text-gray-900">{client.postal_code || 'N/A'}</p>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-500">Country</label>
                                                <p className="mt-1 text-sm text-gray-900">{client.country_code || 'N/A'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white border border-gray-300 rounded-lg shadow-sm">
                                <div className="px-6 py-4 border-b border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-900">Account Information</h3>
                                </div>
                                <div className="px-6 py-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Member Since</label>
                                            <p className="mt-1 text-sm text-gray-900">
                                                {client.created_at ? new Date(client.created_at).toLocaleDateString('en-US', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric'
                                                }) : 'N/A'}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Identity Verified</label>
                                            <p className="mt-1 text-sm">
                                                {client.is_identity_verified ? (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        <MdVerified className="mr-1 h-3 w-3" />
                                                        Verified
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Not Verified
                                                    </span>
                                                )}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Contact Setup</label>
                                            <p className="mt-1 text-sm">
                                                {client.contact_setup ? (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Complete
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        Pending
                                                    </span>
                                                )}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-500">Credit Setup</label>
                                            <p className="mt-1 text-sm">
                                                {client.account_credit_setup ? (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Complete
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        Pending
                                                    </span>
                                                )}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="bg-white border border-gray-300 rounded-lg shadow-sm p-8 text-center">
                            <p className="text-gray-500">Client not found.</p>
                        </div>
                    )}
                </div>
            </div>
        </AdminLayout>
    );
}
