import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";

export default function DomainDeletionModal({ isOpen, onClose, deletionRequest }) {
    const [note, setNote] = useState("");
    const [submitting, setSubmitting] = useState(false);

    

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={onClose}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-30" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-xl font-bold leading-6 text-gray-900"
                                >
                                    Client’s Request for Domain Deletion
                                </Dialog.Title>

                                {/* Support Feedback (Form if not yet submitted) */}
                                {!deletionRequest.support_note?.trim() && (
                                    <form
                                        onSubmit={handleSubmit}
                                        className="mt-4 mb-6 border border-blue-200 bg-blue-50 rounded p-4"
                                    >
                                        <p className="text-blue-700 font-semibold mb-2">Support Feedback</p>
                                        <label
                                            htmlFor="support_note"
                                            className="block font-medium mb-1"
                                        >
                                            Support Note
                                        </label>
                                        <textarea
                                            id="support_note"
                                            value={note}
                                            onChange={(e) => setNote(e.target.value)}
                                            className="w-full border border-gray-300 rounded p-2"
                                            rows={4}
                                            placeholder="Enter your support feedback here..."
                                            required
                                        />
                                        <button
                                            type="submit"
                                            className="mt-3 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
                                            disabled={submitting || !note.trim()}
                                        >
                                            {submitting ? "Submitting..." : "Submit Feedback"}
                                        </button>
                                    </form>
                                )}

                                {/* Request Details */}
                                <div className="border border-red-300 bg-red-50 rounded-md p-4 mb-4 mt-4">
                                    <p className="text-red-700 font-semibold mb-2">Request Details:</p>
                                    <p><strong>Client Name:</strong> {deletionRequest.first_name}{" "}{deletionRequest.last_name}</p>
                                    <p><strong>Client Email:</strong> {deletionRequest.email}</p>
                                    <p><strong>Client Domain:</strong> {deletionRequest.domainName}</p>
                                    <p><strong>Request Date:</strong> {setDefaultDateFormat(deletionRequest.requested_at) + ' ' + new Date(deletionRequest.requested_at + 'Z').toLocaleTimeString()}</p>
                                    <p><strong>Reason for Deletion:</strong> {deletionRequest.reason}</p>
                                </div>

                                {/* Read-Only Feedback if already submitted */}
                                {deletionRequest.support_note && (
                                    <div className="border border-blue-300 bg-blue-50 rounded-md p-4">
                                        <p className="text-blue-700 font-semibold mb-2">Support Feedback:</p>
                                        <p><strong>Support Agent:</strong> {deletionRequest.support_agent_name}</p>
                                        <p><strong>Feedback Date:</strong> {deletionRequest.feedback_date}</p>
                                        <p><strong>Note:</strong> {deletionRequest.support_note}</p>
                                    </div>
                                )}

                                <div className="mt-6 flex justify-end">
                                    <button
                                        type="button"
                                        className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded hover:bg-gray-100"
                                        onClick={onClose}
                                    >
                                        Got It
                                    </button>
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
