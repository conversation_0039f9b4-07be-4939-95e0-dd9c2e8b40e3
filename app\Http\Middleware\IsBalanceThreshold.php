<?php

namespace App\Http\Middleware;

use App\Modules\Epp\Services\RegistryAccountBalanceService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class IsBalanceThreshold
{
    private static $warning = 'warning';

    private static $flag = 'flag';

    private static $registries = 'registryArray';

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        self::setRegistryNamesArray();
        self::setBalanceWarning();
        self::setBalanceFlag();

        return $next($request);
    }

    private function setRegistryNamesArray()
    {
        $registryArray = DB::client()->table('registries')->get()->pluck('name')->toArray();
        if (! session()->has(self::$registries)) {
            session([self::$registries => json_encode($registryArray)]);
        }
    }

    private function setBalanceWarning()
    {
        $registries = RegistryAccountBalanceService::checkMinimumRegistryBalanceBulk();
        foreach ($registries as $registry) {
            $titleText = 'Balance Reached Minimum Threshold';
            $msgText = 'The balance on your '.strtoupper($registry['name']).' account is within the minimum threshold. Click to settle the account.';

            if ($registry['is_less_minimum']) {
                self::setMessage($registry, self::$warning, $titleText, $msgText);
            } else {
                self::unsetMessage($registry['name'], self::$warning);
            }
        }
    }

    private function setBalanceFlag()
    {
        $registries = RegistryAccountBalanceService::checkBalanceFlagBulk();
        foreach ($registries as $registry) {
            $titleText = 'Insufficient Balance';
            $msgText = 'You have insufficient balance on your '.strtoupper($registry['name']).' account. Click to settle the account.';

            if ($registry['is_flag']) {
                self::setMessage($registry, self::$flag, $titleText, $msgText);
            } else {
                self::unsetMessage($registry['name'], self::$flag);
            }
        }
    }

    private function setMessage(array $registry, string $type, $titleText, $msgText)
    {
        $registryName = $registry['name'];
        $registryId = $registry['id'];

        $registryFlashKey = $registryName.'_'.$type;

        $data = [
            'id' => $registryId,
            'name' => $registryName,
            'title' => $titleText,
            'message' => $msgText,
        ];

        if (! session()->has($registryFlashKey)) {
            session([$registryFlashKey => json_encode($data)]);
        }
    }

    private function unsetMessage(string $registry, string $type)
    {
        $registryFlashKey = $registry.'_'.$type;

        if (session()->has($registryFlashKey)) {
            session()->forget($registryFlashKey);
        }
    }
}
