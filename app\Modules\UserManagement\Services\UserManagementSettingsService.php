<?php

namespace App\Modules\UserManagement\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\UserManagement\Constants\UserManagementCategoryConstants;
use App\Modules\UserManagement\Constants\UserManagementPermissionConstants;
use App\Modules\UserManagement\Constants\UserManagementRoleConstants;

use Exception;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

class UserManagementSettingsService
{
    /**
     * Sync Permissions
     */
    public function syncPermissions()
    {                
        try 
        {
            DB::transaction(
                function ()
                {
                    $permissionsExcluded = UserManagementPermissionConstants::PERMISSIONS_EXCLUDED; 
                    $permissionsHidden   = UserManagementPermissionConstants::PERMISSIONS_HIDDEN;

                    $data     = [];
                    $routeslist = Route::getRoutes();

                    //! Get all existing names from the 'access' table
                    $existingNames = DB::table('access')->pluck('name')->toArray();
                    $routeNames    = [];


                    foreach ($routeslist as $route) 
                    {
                        $name = $route->getName();

                        if (
                            $name && !in_array($name, $existingNames) && !in_array($name, $permissionsExcluded)
                        ) 
                        {
                            $data[] = 
                            [
                                'name'  => $name,
                                'route' => $name, 
                            ];
                        }

                        $routeNames[] = $name; 
                    }

                    $this->deleteObsoletePermissions(array_filter($routeNames));

                    if (!empty($data)) 
                    {
                        DB::table('access')->insert($data);
                    }

                    DB::table('access')->whereIn('name', $permissionsHidden)->update(['is_hidden' => true]);

                    app(AuthLogger::class)->info("Permissions Sync");
                }
            );

            $this->syncPermissionsToMainUser(); 
        }
        catch (Exception $error)
        {
            app(AuthLogger::class)->error("Permissions Could not Sync {$error->getMessage()}");
        }
    }

    /**
     * Delete Obsolete Permissions
     * 
     * @param array $routeNames
     */
    public function deleteObsoletePermissions(array $routeNames)
    {        
        DB::table('access')
            ->whereNotIn('name', $routeNames)
            ->delete(); 
    }

    /**
     * Sync Permissions to main user 
     */
    public function syncPermissionsToMainUser()
    {
        $user = DB::table('admins')
            ->where('id', '=', 1)
            ->firstOrFail(); 

        //! INCLUDE HIDDEN PERMISSIONS 
        $permissions = DB::table('access')
            ->pluck('id')
            ->toArray(); 

        $permissionsToBeInserted = []; 

        foreach ($permissions as $permission)
        {
            $permissionsToBeInserted[] = 
            [
                'admin_id'  => $user->id,
                'access_id' => $permission
            ];
        }

        DB::table('admin_access')->insertOrIgnore($permissionsToBeInserted);
    }

    /**
     * Load Default Categories 
     */
    public function loadDefaultCategories()
    {
        try 
        {   
            DB::transaction(
            function () 
                {
                    $categories = UserManagementCategoryConstants::PREDEFINED_DEFAULTS;

                    foreach ($categories as $category)
                    {
                        //! CHECK IF THE CATEGORY ALREADY EXISTS
                        $existingCategory = DB::table('category')->where('name', $category['name'])->first();

                        if ($existingCategory) 
                        {
                            $categoryId = $existingCategory->id; 
                        }
                        else 
                        {
                            $categoryId = DB::table('category')
                                ->insertGetId(
                                    [
                                        'name'       => $category['name'],
                                        'admin_id'   => Auth::user()->id,
                                        'created_at' => now(),
                                        'updated_at' => now(), 
                                    ]
                                );
                        }

                        $permissionsToBeInserted = [];

                        $permissionsFromName = DB::table('access')
                            ->where('name', 'like', "{$category['searchRouteName']}%")
                            ->get();

                        foreach($permissionsFromName as $permission)
                        {
                            //! CHECK IF THIS ACCESS_ID + CATEGORY_ID COMBO ALREADY EXISTS
                            $entryExists = DB::table('access_category')
                                ->where('access_id', $permission->id)
                                ->where('category_id', $categoryId)
                                ->exists();

                            if ($entryExists == false) 
                            {
                                $permissionsToBeInserted[] =
                                    [
                                        'access_id'   => $permission->id,
                                        'category_id' => $categoryId,
                                        'created_at'  => now(),
                                        'updated_at'  => now(),
                                    ];
                            }
                        }

                        if (!empty($permissionsToBeInserted)) 
                        {
                            DB::table('access_category')->insert($permissionsToBeInserted);
                        }
                    }

                    app(AuthLogger::class)->info("Default Categories Loaded");
                }
            );
        }
        catch (Exception $error) 
        {
            app(AuthLogger::class)->error("Could not load Default Categories {$error->getMessage()}");
        }
    }

    /**
     * Load Default Categories 
     */
    public function loadDefaultRoles()
    {
        try 
        {
            DB::transaction(
            function () 
                {
                    $roles = UserManagementRoleConstants::PREDEFINED_DEFAULTS;

                    foreach($roles as $role)
                    {
                        $existingRole = DB::table('roles')->where('name', $role['name'])->first();

                        if ($existingRole)
                        {
                            $roleId = $existingRole->id; 
                        }
                        else 
                        {
                            $roleId = DB::table('roles')->insertGetId(
                                [
                                    'name'    => $role['name'],
                                    'user_id' => Auth::user()->id
                                ]
                            );
                        }

                        $this->loadRolePermissionsFromCategory(
                            $roleId,
                            $role['permissionByCategoryGroup'],
                            $role['excludedPermissions']
                        );

                        $this->loadRolePermissionsAdditional(
                            $roleId,
                            $role['additionalPermissions'],
                            $role['excludedPermissions']
                        );
                    }
                }
            );

            app(AuthLogger::class)->info("Default Roles Loaded");
        }
        catch (Exception $error) 
        {
            app(AuthLogger::class)->error("Could not load Default Roles {$error->getMessage()}");
        }
    }

    /**
     * Load Role Permissions from Category
     *
     * @param int   $roleId
     * @param array $categories
     * @param array $excludedPermissions 
     */
    private function loadRolePermissionsFromCategory(int $roleId, array $categories, array $excludedPermissions)
    {
        $permissionsFromCategory = []; 

        foreach ($categories as $category)
        {
            $existingCategory = DB::table('category')->where('name', $category)->first();

            if ($existingCategory)
            {
                $permissions = DB::table('access_category')
                    ->select(
                        'access.id as id',
                            'access.name as name'
                    )
                    ->join('access', 'access.id', '=', 'access_category.access_id')
                    ->where('category_id', '=', $existingCategory->id)
                    ->get();

                foreach ($permissions as $permission)
                {
                    if (!in_array(
                            $permission->name,
                            $excludedPermissions
                        )
                    )
                    {
                        $permissionsFromCategory[] = $permission->id;  
                    }
                }
            }
        }

        $insertPermissions = []; 

        foreach ($permissionsFromCategory as $permissionId)
        {
            $exists = DB::table('access_roles')
                ->where('role_id', $roleId)
                ->where('access_id', $permissionId)
                ->exists();

            if (!$exists) 
            {
                $insertPermissions[] = 
                [
                    'role_id'   => $roleId,
                    'access_id' => $permissionId
                ];
            }
        }

        DB::table('access_roles')->insert($insertPermissions);
    }

    /**
     * Load Role Permissions Additional
     *
     * @param int   $roleId
     * @param array $additionalPermissions
     * @param array $excludedPermissions 
     */
    private function loadRolePermissionsAdditional(int $roleId, array $additionalPermissions, array $excludedPermissions)
    {
        $insertPermissions = [];
        $permissionsFound  = [];

        foreach ($additionalPermissions as $permission) 
        {            
            $existingPermission = DB::table('access')
                ->where('name', '=',  $permission)
                ->first();

            if ($existingPermission) 
            {
                if (!in_array(
                        $existingPermission->name,
                        $excludedPermissions
                    )
                )
                {
                    $permissionsFound[] = $existingPermission->id;  
                }
            }
        }

        foreach ($permissionsFound as $permissionId)
        {
            $exists = DB::table('access_roles')
                ->where('role_id', $roleId)
                ->where('access_id', $permissionId)
                ->exists();

            if (!$exists) 
            {
                $insertPermissions[] = 
                [
                    'role_id'   => $roleId,
                    'access_id' => $permissionId
                ];
            }
        }

        DB::table('access_roles')->insert($insertPermissions);
    }
}
