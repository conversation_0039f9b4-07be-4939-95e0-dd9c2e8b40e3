<?php

namespace App\Modules\Notification\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use App\Modules\Notification\Services\NotificationSchedulerService;
use App\Modules\Notification\Constants\NotificationStatus;
use App\Models\ScheduleNotification;
use App\Modules\Notification\Requests\UpdateNotificationStatus;
use App\Modules\Notification\Requests\DeleteNotificationRequest;

class NotificationManagementController extends Controller
{
    protected $notificationSchedulerService;

    public function __construct(NotificationSchedulerService $notificationSchedulerService)
    {
        $this->notificationSchedulerService = $notificationSchedulerService;
    }

    public function index(Request $request): Response
    {
        return Inertia::render('Notification/NotificationManagement/Index', 
            $this->notificationSchedulerService->processNotificationList($request)
        );
    }

    public function updateStatus(UpdateNotificationStatus $request)
    {
        return $this->notificationSchedulerService->processStatusUpdate($request);
    }

    public function delete(DeleteNotificationRequest $request)
    {
        return $this->notificationSchedulerService->processDelete($request);
    }

    public function users($id, Request $request): Response
    {
        return Inertia::render('Notification/NotificationManagement/UsersList', 
            $this->notificationSchedulerService->processUsersList($id, $request)
        );
    }
}