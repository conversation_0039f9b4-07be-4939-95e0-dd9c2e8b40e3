<?php

namespace App\Modules\UserManagement\Controllers;

use App\Http\Controllers\Controller;

use App\Modules\UserManagement\Services\UserManagementRoleService;
use App\Modules\UserManagement\Requests\UserManagementRoleIndexRequest; 
use App\Modules\UserManagement\Requests\UserManagementRoleBulkDeleteRequest;
use App\Modules\UserManagement\Requests\UserManagementRoleCreateRequest;
use App\Modules\UserManagement\Requests\UserManagementRoleEditRequest;
use App\Modules\UserManagement\Requests\UserManagementRoleUpdateRequest;

use Inertia\Inertia;

class UserManagementRoleController extends Controller
{
    public function index(UserManagementRoleIndexRequest $request)
    {
        return Inertia::render(
            'UserManagement/Role/UserManagementRoleIndex',
            [
                'data' => (new UserManagementRoleService())->fetchItems($request->only('showItems', 'role', 'orderBy'))
            ]
        );
    }

    public function create()
    {
        $data = (new UserManagementRoleService())->lordCreateFormData();

        return Inertia::render(
            'UserManagement/Role/UserManagementRoleCreate', 
            $data
        );
    }

    public function store(UserManagementRoleCreateRequest $request)
    {
        (new UserManagementRoleService())->createItem($request->only('name', 'permissions'));

        return redirect()->route('user-management.role')
            ->with('successMessage', 'Role Created');
    }

    public function fetchPermissions(int $id)
    {
        return (new UserManagementRoleService())->fetchItemPermissions($id);
    }

    public function edit($id)
    {
        $data = (new UserManagementRoleService())->lordEditFormData($id);
        
        return Inertia::render(
            'UserManagement/Role/UserManagementRoleUpdate',
            $data
        );
    }

    public function update(UserManagementRoleUpdateRequest $request, int $id)
    {
        (new UserManagementRoleService())->updateItem($request->only('name', 'syncPermissions', 'permissions'), $id);

        return redirect()->route('user-management.role')
            ->with('successMessage', 'Category Updated');    
    }

    public function getRolePermissions(UserManagementRoleEditRequest $request)
    {
        return $request->getRoleAccess();
    }

    public function delete(int $id)
    {
        (new UserManagementRoleService())->deleteItem($id);
    }

    public function bulkDelete(UserManagementRoleBulkDeleteRequest $request)
    {
        (new UserManagementRoleService())->bulkDeleteItems($request->only('roles'));
    }
}
