<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\DB;
use App\Exceptions\FailedRequestException;

class ClientDetailService
{
    public static function instance(): self
    {
        return new self;
    }

    public function getClientDetails(int $clientId)
    {
        $client = DB::client()->table('users')
            ->where('id', $clientId)
            ->whereNull('deleted_at')
            ->select(
                'id', 'first_name', 'last_name', 'email', 'created_at', 'updated_at',
                'is_active', 'is_invited', 'contact_setup', 'last_active_at',
                'street', 'city', 'state_province', 'postal_code', 'country_code',
                'is_identity_verified', 'account_credit_setup', 'stripe_customer_id'
            )
            ->first();

        if (!$client) {
            throw new FailedRequestException(404, 'Client not found.', '');
        }

        $client->name = trim($client->first_name . ' ' . $client->last_name);

        $balance = $this->getClientBalance($clientId);
        $domainCount = $this->getClientDomainCount($clientId);

        return [
            'client' => $client,
            'balance' => $balance,
            'domain_count' => $domainCount,
        ];
    }

    private function getClientBalance(int $clientId)
    {
        $balance = DB::client()->table('account_credits')
            ->where('user_id', $clientId)
            ->orderBy('id', 'desc')
            ->first();

        return $balance ? $balance->running_balance : 0;
    }

    private function getClientDomainCount(int $clientId)
    {
        return DB::client()->table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->where('user_contacts.user_id', $clientId)
            ->where('registered_domains.status', '!=', 'deleted')
            ->count();
    }
}