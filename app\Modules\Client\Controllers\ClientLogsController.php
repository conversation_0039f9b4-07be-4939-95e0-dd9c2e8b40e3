<?php

namespace App\Modules\Client\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Client\Services\ClientLogService;
use App\Modules\Client\Requests\ShowListRequest;
use Inertia\Response;
use Inertia\Inertia;
use Illuminate\Http\RedirectResponse;

class ClientLogsController extends Controller
{
    protected $clientLogService;

    public function __construct(ClientLogService $clientLogService)
    {
        $this->clientLogService = $clientLogService;
    }

    /**
     * Redirect to security logs page
     */
    public function index(ShowListRequest $request): RedirectResponse
    {
        return redirect()->route('client.logs.security.all', $request->query());
    }
}
