<?php

namespace App\Listeners;

use App\Events\EmailSentEvent;
use Exception;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;

class EmailSentListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    public function handle(EmailSentEvent $event)
    {
        try {
            $this->createTransactionHistory($event);
        } catch (Exception $e) {
            // $this->createTransactionHistory($event, 'failed');
            app(AuthLogger::class)->error('Email History: '.$e->getMessage());
        }
    }

    private function createTransactionHistory(EmailSentEvent $event, ?string $overrideStatus = null): void
    {
        DB::client()->table('email_histories')->insert([
            'user_id' => $event->userId,
            'name' => $event->name,
            'recipient_email' => $event->recipient_email,
            'subject' => $event->subject,
            'email_body' => $event->email_body,
            'email_type' => $event->email_type,
            'attachment' => $event->attachment,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}