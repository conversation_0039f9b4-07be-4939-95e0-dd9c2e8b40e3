<?php

namespace App\Modules\Transfer\Constants;

use App\Modules\Epp\Constants\EppDomainStatus;

final class TransferRequest
{
    public const INBOUND = 'inbound';

    public const OUTBOUND = 'outbound';

    public const PENDING = 'pending';

    public const PENDING_REQUEST = 'pendingRequest';

    public const PENDING_APPROVAL = 'pendingApproval';

    public const SYSTEM_CANCELLED = 'systemCancelled';

    public const USER_CANCELLED = 'userCancelled';

    public const CONFLICT = 'conflict';

    public const REQUIRED_ACTION = 'required';

    public const PREVIOUS = 'previous';

    public const CATEGORIES = [
        self::PENDING,
        self::CONFLICT,
        self::REQUIRED_ACTION,
        self::PREVIOUS,
    ];

    public const STATUS_GROUPS = [
        self::PENDING => [
            self::PENDING,
            self::PENDING_REQUEST,
            self::PENDING_APPROVAL,
        ],
        self::CONFLICT => [
            EppDomainStatus::TRANSFER_CLIENT_CONFLICT,
        ],
        self::REQUIRED_ACTION => [
            EppDomainStatus::TRANSFER_PENDING,
        ],
        self::PREVIOUS => [
            EppDomainStatus::TRANSFER_CLIENT_APPROVED,
            EppDomainStatus::TRANSFER_CLIENT_REJECTED,
            EppDomainStatus::TRANSFER_CLIENT_CANCELLED,
            EppDomainStatus::TRANSFER_SERVER_APPROVED,
            EppDomainStatus::TRANSFER_SERVER_REJECTED,
            self::SYSTEM_CANCELLED,
        ],
    ];

    public const TRANSFER_HISTORY_STATUS = [
        self::INBOUND => [
            self::INBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_APPROVED,
            self::INBOUND.'.'.EppDomainStatus::TRANSFER_SERVER_APPROVED,
            self::INBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_REJECTED,
            self::INBOUND.'.'.EppDomainStatus::TRANSFER_SERVER_REJECTED,
            self::INBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_CANCELLED,
            TransferRequest::SYSTEM_CANCELLED,
        ],
        self::OUTBOUND => [
            self::OUTBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_APPROVED,
            self::OUTBOUND.'.'.EppDomainStatus::TRANSFER_SERVER_APPROVED,
            self::OUTBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_REJECTED,
            self::OUTBOUND.'.'.EppDomainStatus::TRANSFER_SERVER_REJECTED,
            self::OUTBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_CANCELLED,
        ],
    ];
}
