<?php

namespace App\Modules\PendingDelete\Services;

use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Modules\Client\Constants\DomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainEppDeleteJobService
{
    public static function instance()
    {
        $domainEppDeleteJobService = new self;

        return $domainEppDeleteJobService;
    }

    public function eppDelete(array $domain): void
    {
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($domain['domainName'])['data'];
        $datastoreInfo = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName'])['data'];
        $isExpirationValid = $this->validateDomainExpiry($eppInfo['expiry'], $datastoreInfo['expiry']);
        $isStatusValid = $this->validateDomainStatus($eppInfo['status']);

        if (!$isExpirationValid || !$isStatusValid) {
            NotificationService::sendFailedDeleteRequestNotif($domain['domainName']);
            $this->revertDomain($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            return;
        }

        $eppDeleteResult = EppDomainService::instance()->callEppDomainDelete($domain['domainName']);
        $datastoreDeleteResult = EppDomainService::instance()->callDatastoreDomainDelete($domain['domainName']);

        $eppSuccess = isset($eppDeleteResult['status']) && $eppDeleteResult['status'] === 'OK';
        $datastoreSuccess = isset($datastoreDeleteResult['status']) && $datastoreDeleteResult['status'] === 'OK';

        if ($eppSuccess && $datastoreSuccess) {
            $this->dispatchDomainHistory($domain, 'success');
        } else {
            NotificationService::sendFailedDeleteRequestNotif($domain['domainName']);
            $this->revertDomain($domain);
            $this->dispatchDomainHistory($domain, 'failed');
            throw new \Exception("EPP deletion failed for domain {$domain['domainName']}");
        }
    }

    private function validateDomainExpiry(string $eppExpiry, string $datastoreExpiry): bool
    {
        $eppDate = Carbon::parse($eppExpiry)->setMilliseconds(0);
        $datastoreDate = Carbon::parse($datastoreExpiry)->setMilliseconds(0);

        return $eppDate->diffInDays($datastoreDate, true) > 5;
    }

    private function validateDomainStatus(array $currentStatus): bool
    {
        $requiredStatus = EppDomainStatus::POST_AUTO_RENEWAL_GRACE_PERIOD_STATUS;
        $missingRequiredStatuses = array_diff($requiredStatus, $currentStatus);

        return empty($missingRequiredStatuses);
    }

    private function revertDomain(array $domain): void
    {
        DB::client()->table('pending_domain_deletions')->where('id', $domain['deleteId'])
            ->update([
                'deleted_by' => null,
                'deleted_at' => null,
                'updated_at' => Carbon::now()
            ]);

        DB::client()->table('registered_domains')->where('id', $domain['registeredDomainId'])
            ->update([
                'status' => UserDomainStatus::OWNED,
                'deleted_at' => null,
                'updated_at' => Carbon::now()
            ]);

        DB::client()->table('domains')->where('id', $domain['domainId'])
            ->update([
                'status' => DomainStatus::EXPIRED,
                'updated_at' => Carbon::now()
            ]);
    }

    private function dispatchDomainHistory(array $domain, string $status): void
    {
        if ($status === 'success') {
            $message = 'Domain "' . $domain['domainName'] . '" deleted from pending deletion by system';
        } else {
            $message = 'Domain "' . $domain['domainName'] . '" deletion failed during pending deletion process';
        }

        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $domain['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $domain['userId'],
            'status'    => $status,
            'message'   => $message,
            'payload'   => json_encode($domain),
            'created_at'=> now(),
            'updated_at'=> now(),
        ]);
    }
}
