<?php

namespace App\Modules\Notification\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Exception;

class GeneralNotificationRequest extends FormRequest
{
    private const SCHEDULE_TYPES = ['one-time', 'weekly', 'monthly', 'yearly'];
    private const NOTIFICATION_TYPES = ['Important', 'Normal'];
    private const SELECTION_MODES = ['all', 'select'];

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $selectionMode = $this->input('selection_mode');
        
        return array_merge(
            $this->baseRules(),
            $this->scheduleTypeRules(),
            $this->optionalFieldsRules(),
            $this->input('selection_mode') === 'select' ? $this->selectedUsersRules() : []

        );
    }

    private function baseRules(): array
    {
        return [
            'selection_mode' => 'required|in:' . implode(',', self::SELECTION_MODES),
            'preview_emails' => 'required|array',
            'preview_emails.*' => 'email',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'link_name' => 'required|string|max:255',
            'redirect_url' => 'required|string|starts_with:/',
            'type' => 'required|string|in:' . implode(',', self::NOTIFICATION_TYPES),
            'schedule_type' => 'required|string|in:' . implode(',', self::SCHEDULE_TYPES),
            'time' => 'required|date_format:H:i',
        ];
    }

    private function scheduleTypeRules(): array
    {
        return match ($this->input('schedule_type')) {
            'one-time' => [
                'start_date' => 'required|date',
            ],
            'weekly' => [
                'weekday' => 'required|array|min:1',
                'weekday.*' => 'integer|between:0,6'
            ],
            'monthly' => ['day_of_month' => 'required|integer|between:1,31'],
            'yearly' => [
                'month' => 'required|integer|between:1,12',
                'day_of_month' => 'required|integer|between:1,31'
            ],
            default => []
        };
    }

    private function selectedUsersRules(): array
    {
        return [
            'selected_users' => 'required|array|min:1|max:20',
            'selected_users.*' => 'required|integer|',
        ];
    }

    private function optionalFieldsRules(): array
    {
        return [
            'min_registration_period' => 'nullable|integer|between:1,365',
            'max_registration_period' => 'nullable|integer|between:1,365|gte:min_registration_period',
            'expiration' => 'nullable|date',
        ];
    }

    public function handleError(Exception $e): array
    {
        return [
            'error' => 'Failed to schedule notification: ' . $e->getMessage(),
        ];
    }

    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Validation\ValidationException($validator);
    }

    public function getNotificationData(): array
    {
        $data = $this->validated();
        
        return array_merge(
            $data,
            [
                'preview_emails' => $data['preview_emails'],
                'title' => $data['title'],
                'message' => $data['message'],
                'link_name' => $data['link_name'],
                'redirect_url' => $data['redirect_url'],
                'type' => $data['type'],
                'schedule_type' => $data['schedule_type'],
                'time' => $data['time'],
                
                'start_date' => $data['start_date'] ?? null,
                'weekday' => $data['weekday'] ?? null,
                'day_of_month' => $data['day_of_month'] ?? null,
                'month' => $data['month'] ?? null,
                
                // Optional fields
                'min_registration_period' => $data['min_registration_period'] ?? null,
                'max_registration_period' => $data['max_registration_period'] ?? null,
                'expiration' => $data['expiration'] ?? null,
            ]
        );
    }

    protected function prepareForValidation()
    {
        if (!$this->has('preview_emails') && $this->has('selected_users')) {
            $this->merge([
                'preview_emails' => []
            ]);
        }
    }
}