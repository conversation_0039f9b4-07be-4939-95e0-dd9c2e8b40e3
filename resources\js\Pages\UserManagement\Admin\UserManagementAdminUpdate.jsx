//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* ICONS
import { MdKeyboardBackspace } from 'react-icons/md';

//* COMPONENTS
import AdminLayout from '@/Layouts/AdminLayout'

//* PARTIALS
import PartialUserManagementAdminFormSetPermissions from './Partials/PartialUserManagementAdminFormSetPermissions';

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserManagementAdminUpdate(props)
{
    //! PACKAGE
    //...
    
    //! VARIABLES
    //...

    //! STATES
    const activePermissions                                                 = props.data.activePermissions;
    const categories                                                        = props.data.categories;
    const categoryPermissions                                               = props.data.categoryPermissions;
    const roles                                                             = props.data.roles;
    const [stateInputName, setStateInputName]                               = useState(props.data.admin.adminName);
    const [stateInputEmail, setStateInputEmail]                             = useState(props.data.admin.adminEmail);
    const [stateSelectedRole, setStateSelectedRole]                         = useState(props.data.admin.roleId ?? '');
    const [stateInputSelectedPermissions, setStateInputSelectedPermissions] = useState(props.data.initialPermissions);
    const [stateErrorMessageName, setStateErrorMessageName]                 = useState(null);
    const [stateErrorMessageEmail, setStateErrorMessageEmail]               = useState(null);
    const [stateErrorMessageRole, setStateErrorMessageRole]                 = useState(null);
    const [stateErrorMessagePermissions, setStateErrorMessagePermissions]   = useState(null);

    //! FUNCTIONS


    function handleSubmit()
    {
        setStateErrorMessageEmail(null);
        setStateErrorMessagePermissions(null);
        setStateErrorMessageRole(null);
        setStateErrorMessageName(null);

        router.patch(
            route("user-management.admin.update", {id : props.data.admin.adminId}),
                {
                    name       : stateInputName,
                    email      : stateInputEmail,
                    roleId     : stateSelectedRole == 0 ? null : stateSelectedRole,
                    permissions: stateInputSelectedPermissions,
                },
                {
                    onSuccess: () => 
                    {
                        toast.success('success'); 
                    }, 
                    onError: (error) => 
                    {
                        toast.error('Please fix input errors')

                        setStateErrorMessageEmail(error.email ?? null);
                        setStateErrorMessagePermissions(error.permissions ?? null);
                        setStateErrorMessageRole(error.role ?? null);
                        setStateErrorMessageName(error.name ?? null);
                    }
                }
            )
    }

     //! USE EFFECTS



    return (
        <AdminLayout>
            <div
                className="mx-auto container max-w-[1200px] flex flex-col gap-4 rounded-lg"
            >
                <div
                    className="flex items-center text-lg font-semibold"
                >
                    <Link
                        href={route("user-management.admin")}
                        className=" hover:!shadow-none flex gap-2 items-center"
                        replace={true}
                    >
                        <MdKeyboardBackspace
                            className="text-3xl rounded-full cursor-pointer hover:text-gray-700 text-gray-700"
                        />
                        <span
                            className="hover:text-gray-700 text-gray-700"
                        >
                            Back to Users
                        </span>
                    </Link>
                </div>

                <div
                    className="header"
                >
                    <div
                        className='text-2xl font-semibold'
                    >
                        Update User
                    </div>
                </div>

                <PartialUserManagementAdminFormSetPermissions
                    mode={'update'}
                    activePermissions={activePermissions}
                    activeTabs={categories.map(category => category.name)}
                    categories={categories}
                    categoryPermissions={categoryPermissions}
                    roles={roles}
                    stateInputName={stateInputName}
                    setStateInputName={setStateInputName}
                    stateInputEmail={stateInputEmail}
                    setStateInputEmail={setStateInputEmail}
                    stateInputSelectedPermissions={stateInputSelectedPermissions}
                    setStateInputSelectedPermissions={setStateInputSelectedPermissions}
                    stateSelectedRole={stateSelectedRole}
                    setStateSelectedRole={setStateSelectedRole}
                    stateErrorMessageName={stateErrorMessageName}
                    stateErrorMessageEmail={stateErrorMessageEmail}
                    stateErrorMessageRole={stateErrorMessageRole}
                    stateErrorMessagePermissions={stateErrorMessagePermissions}
                    handleEventSubmit={handleSubmit}
                />
            </div>
        </AdminLayout>
    )
}
