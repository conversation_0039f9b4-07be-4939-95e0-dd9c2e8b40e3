<?php

namespace App\Modules\Client\Services;

use Illuminate\Support\Facades\DB;
use App\Modules\Client\Constants\ClientStatus;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class ClientLogService
{
    private const PAGE_LIMIT = 20;
    
    public function getMainLogs(Request $request): array
    {
        $query = DB::table('public.user_transaction_histories as uth')
            ->join('public.users', 'uth.user_id', '=', 'users.id')
            ->select(
                'uth.user_id',
                'users.email',
                DB::raw('MAX(uth.id) as id'),
                DB::raw('MAX(uth.created_at) as created_at'),
                DB::raw('MAX(uth.message) as message'),
                DB::raw('(
                    SELECT type 
                    FROM public.user_transaction_histories 
                    WHERE user_id = uth.user_id 
                    ORDER BY id DESC 
                    LIMIT 1
                ) as type')
            )
            ->groupBy('uth.user_id', 'users.email')
            ->orderBy(DB::raw('MAX(uth.id)'), 'desc');

        $this->applyFilters($query, $request);
        
        $logs = $query->paginate(self::PAGE_LIMIT)->appends($request->query());
        $formattedLogs = $this->formatLogs($logs);

        return [
            'logs' => $formattedLogs->items(),
            'pagination' => $this->formatPagination($formattedLogs),
            'statuses' => ClientStatus::getAllStatuses()
        ];
    }

    private function applyFilters(Builder $query, Request $request): void
    {
        if ($request->has('email')) {
            $query->where('users.email', 'LIKE', "{$request->input('email')}%");
        }

        if ($request->has('type')) {
            $query->having(DB::raw('(
                SELECT type 
                FROM public.user_transaction_histories 
                WHERE user_id = uth.user_id 
                ORDER BY id DESC 
                LIMIT 1
            )'), $request->input('type'));
        }

        if ($request->has('date')) {
            $this->applyDateFilter($query, $request->input('date'));
        }
    }

    private function applyDateFilter(Builder $query, string $dateFilter): void
    {
        $dateConditions = [
            'today' => [Carbon::today(), null],
            'yesterday' => [Carbon::yesterday(), Carbon::today()],
            'last 7 days' => [Carbon::now()->subDays(7), null],
            'last 30 days' => [Carbon::now()->subDays(30), null]
        ];

        if (!isset($dateConditions[$dateFilter])) {
            return;
        }

        [$startDate, $endDate] = $dateConditions[$dateFilter];

        $query->having(DB::raw('MAX(uth.created_at)'), '<=', $startDate);
        if ($endDate) {
            $query->having(DB::raw('MAX(uth.created_at)'), '<=', $endDate);
        }
    }
    private function formatLogs(LengthAwarePaginator $logs): LengthAwarePaginator
    {
        $statusMap = ClientStatus::getAllStatuses();
        
        $logs->through(function ($log) use ($statusMap) {
            $log->type = $statusMap[strtoupper($log->type)] ?? $log->type;
            return $log;
        });

        return $logs;
    }

    private function formatPagination(LengthAwarePaginator $paginator): array
    {
        return [
            'onFirstPage' => $paginator->onFirstPage(),
            'onLastPage' => $paginator->onLastPage(),
            'nextPageUrl' => $paginator->nextPageUrl(),
            'previousPageUrl' => $paginator->previousPageUrl(),
            'itemCount' => $paginator->count(),
            'total' => $paginator->total(),
            'currentPage' => $paginator->currentPage(),
            'lastPage' => $paginator->lastPage()
        ];
    }

   
}
