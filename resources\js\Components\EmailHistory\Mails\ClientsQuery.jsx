import React from 'react';

function ClientsQuery({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">Sender: {data.name}</p>
                <p className="font-bold mb-2">Email: {data.sender}</p>
                <p className="font-bold mb-2">{data.body}</p>

            </div>
        </div>
    );
}

export default ClientsQuery;