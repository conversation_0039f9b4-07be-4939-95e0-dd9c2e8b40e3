<?php

use App\Modules\Activity\Controllers\AdminActivityController;
use App\Modules\Activity\Controllers\ClientActivityController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth', 'registry.balance', 'auth.active', 'auth.permission.check'])->prefix('activity')->group(function () {
    Route::get('/admin', [AdminActivityController::class, 'index'])->name('activity.admin');
    Route::get('/admin-more', [AdminActivityController::class, 'more'])->name('activity.admin-more');

    Route::get('/client', [ClientActivityController::class, 'index'])->name('activity.client');
    Route::get('/client-more', [ClientActivityController::class, 'more'])->name('activity.client-more');
});
