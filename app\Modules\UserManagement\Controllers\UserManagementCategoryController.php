<?php

namespace App\Modules\UserManagement\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\UserManagement\Services\UserManagementCategoryService;
use App\Modules\UserManagement\Services\UserManagementPermissionService; 
use App\Modules\UserManagement\Requests\UserManagementCategoryBulkDeleteRequest;
use App\Modules\UserManagement\Requests\UserManagementCategoryCreateRequest;
use App\Modules\UserManagement\Requests\UserManagementCategoryIndexRequest;
use App\Modules\UserManagement\Requests\UserManagementCategoryUpdateRequest;

use Inertia\Inertia;

class UserManagementCategoryController extends Controller
{
    public function index(UserManagementCategoryIndexRequest $request)
    {
        return Inertia::render(
            'UserManagement/Category/UserManagementCategoryIndex', 
            [
                'data' => (new UserManagementCategoryService())->fetchItems($request->only('showItems', 'category', 'orderBy'))
            ]
        );
    }

    public function create() : \Inertia\Response
    {
        return Inertia::render(
            'UserManagement/Category/UserManagementCategoryCreate', 
            [
                'permissions' => (new UserManagementPermissionService())->fetchItems()
                ]
        );
    }

    public function store(UserManagementCategoryCreateRequest $request)
    {
        (new UserManagementCategoryService())->createItem($request->only('name', 'permissions'));

        return redirect()->route('user-management.category')
            ->with('successMessage', 'Category Created');
    }

    public function fetchPermissions(int $id)
    {                
        return (new UserManagementCategoryService())->fetchItemPermissions($id);
    }

    public function edit($id) : \Inertia\Response
    {
        $data = (new UserManagementCategoryService())->fetchItem($id);

        return Inertia::render(
            'UserManagement/Category/UserManagementCategoryUpdate',
        $data
        );
    }

    public function update(UserManagementCategoryUpdateRequest $request, int $id)
    {
        (new UserManagementCategoryService())->updateItem($request->only('name', 'permissions'), $id);

        return redirect()->route('user-management.category')
            ->with('successMessage', 'Category Updated');
    }

    public function delete(int $id)
    {
        (new UserManagementCategoryService())->deleteItem($id);

        // return redirect()->back('user-management.category')
        //     ->with('successMessage', 'Category Deleted');
    }

    public function bulkDelete(UserManagementCategoryBulkDeleteRequest $request)
    {
        (new UserManagementCategoryService())->bulkDeleteItems($request->only('categories'));

        // return redirect()->back('user-management.category')
        //     ->with('successMessage', 'Categories Deleted');
    }
}
