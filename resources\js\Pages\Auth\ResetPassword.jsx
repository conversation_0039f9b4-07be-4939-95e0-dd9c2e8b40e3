//* PACKAGES
import React, {useState, useEffect} from 'react'
import { toast } from 'react-toastify';
import axios from 'axios';
import { Head, useForm, Link } from "@inertiajs/react";

//* ICONS
import { AiFillEyeInvisible, AiO<PERSON>lineEye } from "react-icons/ai";

//* COMPONENTS
import AppPasswordStrengthIndicatorComponent from '@/Components/App/AppPasswordStrengthIndicatorComponent';
import GuestLayout from "@/Layouts/GuestLayout";
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import PrimaryButton from "@/Components/PrimaryButton";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...



export default function ResetPassword(
    {
        token,
        email
    }
)
{
    //! PACKAGE
    const { data, setData, post, processing, errors, reset } = useForm(
        {
            token: token,
            email: email,
            password: "",
            password_confirmation: "",
        }
    );
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [showInputPassword, setShowInputPassword]               = useState(false);
    const [showInputConfirmPassword, setShowInputConfirmPassword] = useState(false);

    //! USE EFFECTS
    useEffect(() => {
        return () => {
            reset("password", "password_confirmation");
        };
    }, []);

    //! FUNCTIONS
    const onHandleChange = (event) => {
        setData(event.target.name, getEventValue(event));
    };

    const submit = (e) => {
        e.preventDefault();

        post(route("password.store"));
    };

    return (
        <GuestLayout>
            <Head title="Reset Password" />

            <form
                className='flex flex-col gap-4'
                onSubmit={submit}
            >
                <div
                    className="flex flex-col gap-2"
                >
                    <InputLabel htmlFor="email" value="Email" />

                    <TextInput
                        id="email"
                        type="text"
                        name="email"
                        value={data.email}
                        className="w-full bg-gray-100"
                        autoComplete="username"
                        handleChange={onHandleChange}
                        disabled={true}
                    />

                    <InputError message={errors.email} />
                </div>

                <div
                    className="flex flex-col gap-2"
                >
                    <InputLabel
                        htmlFor="password"
                        value="Password"
                    />

                    <div
                        className='relative'
                    >
                        <TextInput
                            type={showInputPassword ? "text" : "password"}
                            name="password"
                            value={data.password}
                            autoComplete="new-password"
                            placeholder="P@ssword25"
                            className="w-full"
                            isFocused={true}
                            handleChange={onHandleChange}
                        />
                        <div
                            className="absolute right-0 top-1/2 -translate-y-1/2 pr-5"
                        >
                            <i
                                onClick={() => setShowInputPassword(!showInputPassword)}
                                className="text-xl cursor-pointer hover:text-primary duration-100 ease-in"
                            >
                                {showInputPassword ? (
                                    <AiFillEyeInvisible />
                                ) : (
                                    <AiOutlineEye />
                                )}
                            </i>
                        </div>
                    </div>

                    {
                        errors.password != null
                            ?
                                <InputError message={errors.password} />
                            :
                            (
                                <span className='text-xs text-gray-400 [&::first-letter]:uppercase'>
                                    Password must contain at least 8 characters, including 1 digit, 1 uppercase letter, 1 lowercase letter & 1 special character.
                                </span>                                               
                            )
                    }
                    {
                        data.password.length > 0 
                            ?
                                <AppPasswordStrengthIndicatorComponent
                                    password={data.password}
                                />
                            :
                                null 
                    }
                </div>


                <div
                    className="flex flex-col gap-2"
                >
                    <InputLabel
                        htmlFor="password_confirmation"
                        value="Confirm Password"
                    />

                    <div
                        className='relative'
                    >
                        <TextInput
                            type={showInputConfirmPassword ? "text" : "password"}
                            name="password_confirmation"
                            value={data.password_confirmation}
                            className="w-full"
                            placeholder="P@ssword25"
                            autoComplete="new-password"
                            handleChange={onHandleChange}
                        />

                        <div
                            className="absolute right-0 top-1/2 -translate-y-1/2 pr-5"
                        >
                            <i
                                onClick={() => setShowInputConfirmPassword(!showInputConfirmPassword)}
                                className="text-xl cursor-pointer hover:text-primary duration-100 ease-in"
                            >
                                {showInputConfirmPassword ? (
                                    <AiFillEyeInvisible />
                                ) : (
                                    <AiOutlineEye />
                                )}
                            </i>
                        </div>
                    </div>
                </div>

                <div>
                    <PrimaryButton
                        className="w-full"
                        disabled={processing}
                    >
                        Reset Password
                    </PrimaryButton>
                </div>
            </form>
        </GuestLayout>
    );
}
