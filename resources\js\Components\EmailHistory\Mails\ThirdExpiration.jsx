import React from 'react';

function ThirdExpiration({ emailData, links }) {
    const data = JSON.parse(emailData);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
            <div className="p-9 bg-white max-w-xl w-full">
                <p className="font-bold mb-2">{data.greeting}</p>
                <p className="mt-4">We hope you're well. Your domain has reached its expiration date. Please see attached file for the full list.</p>
                <p className="font-bold mt-4">IMPORTANT NOTICE</p>
                <p className="mt-2">
                    You can still recover expired domain within 30 days from its deletion. Please note that additional restoration fees will apply. 
                    Contact <a href="http://StrangeDomains.com" className="text-blue-600 underline italic">StrangeDomains.com</a> immediately to proceed with the restoration.
                </p>
                <p className="mt-2">If you already renewed your domain, please disregard this message.</p>
                <p className="mt-4 font-bold mb-2">Sincerely,</p>
                <p className="font-bold">{data.sender_name}</p>
            </div>
        </div>
    );
}

export default ThirdExpiration;