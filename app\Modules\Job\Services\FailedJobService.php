<?php

namespace App\Modules\Job\Services;

use App\Exceptions\FailedRequestException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Job\Constants\ConnectionSource;
use App\Modules\Job\Constants\FailedJobCommand;
use App\Traits\CursorPaginate;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class FailedJobService
{
    private static $CLIENT_RETRY_QUEUE_URL = '/api/failed-queue/retry';

    private static $CLIENT_FLUSH_QUEUE_URL = '/api/failed-queue/flush';

    private static $CLIENT_FORGET_QUEUE_URL = '/api/failed-queue/forget';

    use CursorPaginate;

    private static $pageLimit = 10;

    // public static function index(string $dbConnection)
    // {
    //     $failedJobs = DB::connection($dbConnection)->table('failed_jobs')
    //         ->orderBy('id', 'desc')
    //         ->paginate(self::$pageLimit);

    //     return CursorPaginate::cursor($failedJobs, ['source=' . $dbConnection]);
    // }

    public static function getFailedByJobs($request)
    {
        $failedJobs = DB::connection($request->source)->table('failed_jobs')
            ->where('connection', $request->connection)
            ->orderBy('id', 'desc')
            ->paginate(self::$pageLimit);

        return CursorPaginate::cursor($failedJobs, self::paramToURI($request));
    }

    public static function get(string $dbConnection, string $id)
    {
        $job = DB::connection($dbConnection)->table('failed_jobs')->find($id);

        if ($job != null) {
            return $job;
        }

        throw new FailedRequestException(401, 'failed queue with id ' . $id . ' does not exist', 'INVALID ID');
    }

    public static function retry(string $dbConnection, $option)
    {
        $option = self::validateOption($option);

        if (strcmp($dbConnection, ConnectionSource::ADMIN) == 0) {
            self::adminCommand(FailedJobCommand::QUEUE_RETRY, $option);
        } elseif (strcmp($dbConnection, ConnectionSource::CLIENT) == 0) {
            Http::client()->post(self::$CLIENT_RETRY_QUEUE_URL, ['option' => $option]);
        } else {
            throw new FailedRequestException(401, 'retryAll: Invalid command parameter', 'INVALID COMMAND');
        }

        app(AuthLogger::class)->info('FailedJobService: retry called.');
    }

    public static function flush(string $dbConnection)
    {
        $option = self::validateOption('');

        if (strcmp($dbConnection, ConnectionSource::ADMIN) == 0) {
            self::adminCommand(FailedJobCommand::QUEUE_FLUSH);
        } elseif (strcmp($dbConnection, ConnectionSource::CLIENT) == 0) {
            Http::client()->post(self::$CLIENT_FLUSH_QUEUE_URL);
        } else {
            throw new FailedRequestException(401, 'flush: Invalid command parameter', 'INVALID COMMAND');
        }

        app(AuthLogger::class)->info('FailedJobService: flush called.');
    }

    public static function forget(string $dbConnection, $option)
    {
        $option = self::validateOption($option);

        if (strcmp($dbConnection, ConnectionSource::ADMIN) == 0) {
            self::adminCommand(FailedJobCommand::QUEUE_FORGET, $option);
        } elseif (strcmp($dbConnection, ConnectionSource::CLIENT) == 0) {
            Http::client()->post(self::$CLIENT_FORGET_QUEUE_URL, ['option' => $option]);
        } else {
            throw new FailedRequestException(401, 'flush: Invalid command parameter', 'INVALID COMMAND');
        }

        app(AuthLogger::class)->info('FailedJobService: forget called.');
    }

    private static function validateOption($option)
    {
        if (strcmp(gettype($option), 'array') == 0) {
            return implode(' ', $option);
        } elseif (strcmp(gettype($option), 'string') == 0) {
            return $option;
        }

        throw new FailedRequestException(401, 'retry: Invalid option type', 'INVALID COMMAND');
    }

    private static function adminCommand(string $command, string $option = '')
    {
        if (! FailedJobCommand::validCommand($command)) {
            throw new FailedRequestException(401, 'Invalid command parameter', 'INVALID COMMAND');
        }

        try {
            Artisan::call(trim($command . ' ' . $option));

            $output = Artisan::output();
            app(AuthLogger::class)->info('Artisan: ' . $output);
        } catch (Exception $e) {
            $errorMsg = 'Artisan: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private static function paramToURI($request)
    {
        $param = [];

        if ($request->has('connection')) {
            $param[] = 'connection=' . $request->connection;
        }

        if ($request->has('source')) {
            $param[] = 'source=' . $request->source;
        }

        if ($request->has('date')) {
            $param[] = 'date=' . $request->date;
        }

        if ($request->has('tab')) {
            $param[] = 'tab=' . $request->tab;
        }

        return $param;
    }
}
