<?php

namespace App\Exceptions;

use Exception;
use Inertia\Inertia;

class UnderMaintenanceException extends Exception
{
    public function __construct($code, $help, $error)
    {
        $this->code = $code;
        $this->message = $help;
        $this->file = $error;
    }

    public function render()
    {
        return Inertia::render('Errors/CustomMessage', [
            'code' => $this->code,
            'help' => $this->message,
            'error' => $this->file,
        ]);
    }
}
