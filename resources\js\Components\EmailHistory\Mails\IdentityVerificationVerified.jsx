import React from "react";

export default function IdentityVerificationVerified({ emailBody, links }) {
    const parsedData = JSON.parse(emailBody);

    return (
        <div className="bg-slate-100 min-h-fit flex items-center justify-center" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'", color: "#1a202c" }}>
            <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full text-slate-500">
                <p className="mb-4 font-bold">{parsedData.greeting}</p>

                <p className="mb-4">
                    We are pleased to inform you that your identity has been successfully{" "}
                    <span className="font-bold">verified</span>.
                </p>

                <p className="mb-4">
                    You can now proceed with the next steps of the registration by visiting{" "}
                    <a href={parsedData.redirectUrl} className="text-blue-600 underline">
                        this link
                    </a>
                    .
                </p>

                <p className="mb-6">
                    If you have any questions or need assistance, feel free to contact us at{" "}
                    <a href={parsedData.supportUrl} className="text-blue-600 underline">
                        StrangeDomains.com
                    </a>
                    .
                </p>

                <div className="mb-4">
                    <p className="font-bold">Sincerely,</p>
                    <br />
                    <p className="font-bold">{parsedData.sender_name}</p>
                </div>
            </div>
        </div>
    );
}
