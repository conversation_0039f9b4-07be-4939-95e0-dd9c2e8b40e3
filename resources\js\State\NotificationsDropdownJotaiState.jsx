import { atom } from 'jotai';

// Atom for storing notifications
export const notifications = atom([]);

// Atom for unread notifications count
export const tunreadCounter = atom(-1);

// Atom for total number of notifications
export const totalNotifications = atom(0);

// Atom for tracking current page number for pagination
export const pageNumber = atom(1);

// Atom for checking if more notifications are available
export const hasMore = atom(true);

//hide notification badge onclick

export const hideBadge = atom(false);

export const readNotification = atom([]);

//Atom for tracking loading state