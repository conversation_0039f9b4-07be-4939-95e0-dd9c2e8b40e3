import React from "react";
import NavLink from "@/Components/NavLink";
import { MdOutlineSystemUpdateAlt } from "react-icons/md";

export default function SystemLogNav({ postRouteName = "" }) {
    const currentRoute = route().current() || postRouteName;

    return (
        <NavLink
            href={route("system.logs")}
            active={currentRoute === "system.logs"}
        >
            <span className="flex space-x-4">
                {/* <MdOutlineSystemUpdateAlt className="text-2xl" /> */}
                <span className="text-inherit">System Logs</span>
            </span>
        </NavLink>
    );
}