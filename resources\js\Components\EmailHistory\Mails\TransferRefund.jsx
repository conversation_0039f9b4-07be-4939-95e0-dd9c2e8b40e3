import React from 'react';

function TransferRefund({ emailData, links }) {
    const data = JSON.parse(emailData);
    const { refund_type, domain, refunded_amount, other_data } = data;

    return (
      
        <div className="bg-slate-100 min-h-fit flex items-center justify-center text-slate-500" style={{ fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'" }}>
            <div className="p-9 bg-white shadow-md rounded-md max-w-xl w-full" >
                <p className="font-bold text-lg">Greetings!</p>
                <p className="mt-4">
                    We are pleased to inform you that the refund process for the domain transfer of "<strong>{domain}</strong>" has been initiated. 
                    The refunded amount totals <strong>${refunded_amount}</strong>.
                </p>
                <p className="mt-4">
                    It can take approximately 10 days to appear on your statement. If it takes longer, please contact your bank for assistance. 
                    For other questions or concerns, please don't hesitate to reach out.
                </p>
                <a href={links.PAYMENT_REFUND_VIEW + '/' + other_data.summaryId} className="text-blue-600 underline mt-4 block">View Refund Details</a>
                <p className="font-bold mt-6 mb-2">SUMMARY</p>
                <div className="border-b mt-2 mb-2"></div>
                <p className="mb-2">Invoice #: {other_data.invoice_num}</p>
                <p className="mb-4">Date: {other_data.date}</p>
                <table className="mt-4 w-full ">
                    <thead>
                        <tr className="border-b">
                            <th className="text-left py-2">Domain</th>
                            <th className="text-left py-2">Type</th>
                            <th className="text-left py-2">Refunded Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td className="py-2">{domain}</td>
                            <td className="py-2">{refund_type}</td>
                            <td className="py-2">${refunded_amount}</td>
                        </tr>
                    </tbody>
                </table>
                <p className="mt-4">
                    For more information, please see StrangeDomains's <a href={links.REFUND_POLICY} className="text-blue-600 underline">Refund Policy</a>.
                </p>
                <p className="mt-4 font-bold mb-2">Sincerely,</p>
                <p className="font-bold">Strange Domains</p>
            </div>
        </div>
    );
}

export default TransferRefund;
