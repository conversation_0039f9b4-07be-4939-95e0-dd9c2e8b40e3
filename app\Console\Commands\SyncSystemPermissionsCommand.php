<?php

namespace App\Console\Commands;

use App\Modules\UserManagement\Services\UserManagementSettingsService;

use Illuminate\Console\Command;

class SyncSystemPermissionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync System Permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try 
        {
            (new UserManagementSettingsService())->syncPermissions();

            $this->info('✅ System permissions synced successfully.');

            return Command::SUCCESS;
        } 
        catch (\Throwable $e) 
        {
            $this->error("❌ Failed to sync permissions: {$e->getMessage()}");

            return Command::FAILURE; // exit code 1
        }        
    }
}
