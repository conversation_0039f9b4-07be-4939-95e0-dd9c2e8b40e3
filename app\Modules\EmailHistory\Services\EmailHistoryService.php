<?php

namespace App\Modules\EmailHistory\Services;

use App\Modules\EmailHistory\Constants\Links;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use App\Modules\EmailHistory\Services\EmailSearchService;
use Illuminate\Support\Facades\Response;
use Illuminate\Http\Request;

class EmailHistoryService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    private const TABLE_NAME = 'email_histories';

    public function getPaginatedEmails($request)
    {
        $orderby = $request->input('orderby', 'created_at');

        self::$pageLimit = 20;

        $query = $this->buildEmailQuery($orderby);

        $this->applyFilters($query, $request);

        $filterParams = $request->only(['name', 'email', 'subject', 'emailType', 'search', 'orderby']);

        return $query->paginate(self::$pageLimit)->appends($filterParams)->withQueryString();
    }

    private function buildEmailQuery($orderby)
    {
        $query = DB::client()->table(self::TABLE_NAME)
            ->select('id', 'user_id', 'name', 'recipient_email', 'subject', 'created_at', 'email_type', 'email_body', 'attachment');

        $query->addSelect(DB::raw("
            CASE 
                WHEN email_type LIKE '%Payment Invoice%' THEN 'PaymentInvoice'
                WHEN email_type LIKE 'Third Expiration Notice' THEN 'ThirdExpiration'
                WHEN email_type LIKE '%Authentication Request%' THEN 'AuthenticationRequest'
                WHEN email_type LIKE '%Transfer Refund%' THEN 'TransferRefund'
                WHEN email_type LIKE 'First Expiration Notice%' THEN 'ExpirationNotice'
                WHEN email_type LIKE 'Second Expiration Notice%' THEN 'ExpirationNotice'
                WHEN email_type LIKE 'In Process Identity Verification Notice' THEN 'IdentityVerificationInProcess'
                WHEN email_type LIKE 'Verified Identity Verification Notice' THEN 'IdentityVerificationVerified'
                WHEN email_type LIKE 'OTP Verification' THEN 'OTPVerification'
                WHEN email_type LIKE 'Account Credit - Added to Account' THEN 'AddAccountCredit'
                WHEN email_type LIKE 'Account Credit - Bank Transfer Notification' THEN 'BankTransferNotification'
                WHEN email_type LIKE 'Domain Redemption Notice' THEN 'DomainRedemptionNotice'
                WHEN email_type LIKE 'Report Abuse' THEN 'ReportAbuse'
                WHEN email_type LIKE 'Clients Query' THEN 'ClientsQuery'
                WHEN email_type LIKE 'Domain Transfer - Outbound Request' THEN 'DomainTransferRequestInitiated'
                WHEN email_type LIKE 'User Invite' THEN 'UserInvite'
                WHEN email_type LIKE 'Domain Redemption Period' THEN 'DomainRedemptionPeriod'
                WHEN email_type LIKE '%Refund%' THEN 'DomainRefund'
                ELSE 'Default'
            END AS component_type
        "));

        if ($orderby === 'NAME_ASC') {
            $query->orderBy('name', 'asc');
        } elseif ($orderby === 'NAME_DESC') {
            $query->orderBy('name', 'desc');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        return $query;
    }

    private function applyFilters(Builder $query, $request)
    {
        $this->applyCaseInsensitiveFilter($query, 'name', $request->input('name'));
        $this->applyCaseInsensitiveFilter($query, 'recipient_email', $request->input('email'));
        if ($request->input('subject')) {
            $query->where('subject', $request->input('subject'));
        }
        if ($request->input('emailType')) {
            $query->where('email_type', $request->input('emailType'));
        }

        $date = $request->input('date');
        if ($date) {
            switch ($date) {
                case 'today':
                    $query->whereDate('created_at', '=', now()->toDateString());
                    break;
                case 'yesterday':
                    $query->whereDate('created_at', '=', now()->subDay()->toDateString());
                    break;
                case 'last 7 days':
                    $query->whereDate('created_at', '<=', now()->subDays(7)->toDateString());
                    break;
                case 'last 30 days':
                    $query->whereDate('created_at', '<=', now()->subDays(30)->toDateString());
                    break;
            }
        }

        $search = EmailSearchService::validateSearch($request->input('search', null));
        EmailSearchService::applySearch($query, $search);
    }

    private function applyCaseInsensitiveFilter(Builder $query, $column, $value)
    {
        if (!empty($value)) {
            if ($column === 'name' && strtolower($value) === 'system') {
                $query->whereNull('user_id');
            } else {
                $query->where(DB::raw('LOWER(' . $column . ')'), 'like', '%' . strtolower($value) . '%');
            }
        }
    }

    public function formatPaginationData($emails)
    {
        return [
            'emails' => $emails->items(),
            'onFirstPage' => $emails->onFirstPage(),
            'onLastPage' => $emails->onLastPage(),
            'nextPageUrl' => $emails->nextPageUrl(),
            'previousPageUrl' => $emails->previousPageUrl(),
            'itemCount' => $emails->count(),
            'total' => $emails->total(),
            'links' => Links::getAll(config('app.client_url')),
        ];
    }

    public function getAttachmentResponse($id)
    {
        $email = DB::client()->table(self::TABLE_NAME)
            ->select('attachment', 'name')
            ->where('id', $id)
            ->first();

        if (!$email || !$email->attachment) {
            return response()->json(['error' => 'Attachment not found'], 404);
        }

        $fileName = "domains_list_{$email->name}.csv";
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename={$fileName}",
        ];
        
        return Response::make($email->attachment, 200, $headers);
    }

    public function getEmailsForIndex(Request $request)
    {
        $emails = $this->getPaginatedEmails($request);

        return array_merge(
            $this->formatPaginationData($emails),
            [
                'search' => $request->input('search', ''),
                'orderby' => $request->input('orderby', 'created_at')
            ]
        );
    }
}
