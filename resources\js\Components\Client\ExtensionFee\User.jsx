//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';
import "react-toastify/dist/ReactToastify.css";

//* ICONS
import {
    MdMoreVert,
    MdOutlinePersonOutline,
    MdRadioButtonChecked,
    MdOutlineLockPerson,
    MdAttribution,
    MdVerifiedUser,
    MdOutlineCheckCircleOutline
} from "react-icons/md";

//* COMPONENTS
import Checkbox from "@/Components/Checkbox";
import DropDownContainer from "@/Components/DropDownContainer";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import { getEventValue } from "@/Util/TargetInputEvent";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function User(
    {
        item,
        fees,
        isSelected,
        onCheckboxChange
    }
)
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const sleep = ms => new Promise(r => setTimeout(r, ms));

    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, item.is_active, getEventValue(e));
    };

    const handleView = (view=true) => {
        setShow(false);
        if(view) toast.info("Checking Custom Fees...");

        const fullName = item.first_name + " " + item.last_name;

        router.get(route("client.extension.fee-view"), {
            id: item.id,
            name: fullName,
            extension: "com"
        });
    };

    const handleCreate = async () => {
        setShow(false);
        toast.info("Creating Custom Fees...");

        router.post(route("client.extension.fee-create"), {
            id: item.id,
            name: item.name
        });

        await sleep(2000);
        handleView(false);
    };

    const handleDelete = () => {
        setShow(false);
        toast.info("Deleting Custom Fees...");

        router.delete(route("client.extension.fee-delete"), {
            data: [item.id],
        });
    };

    const handleReset = () => {
        setShow(false);
        toast.info("Resetting Custom Fees...");

        router.patch(route("client.setting.extension.fee-reset"), {
            id: item.id,
        });
    };

    useOutsideClick(ref, () => {
        setShow(false);
    });

    const actionsDropdown = () =>
    {
        const actions =
        [
            {
                hasAccess       : hasPermission('client.extension.fee-view'),
                shouldDisplay   : fees != null,
                label           : 'view',
                title           : null, 
                handleEventClick: handleView,
            }, 
            {
                hasAccess       : hasPermission('client.extension.fee-create'),
                shouldDisplay   : fees == null,
                label           : 'create',
                title           : null,
                handleEventClick: handleCreate,
            }, 
            {
                hasAccess       : hasPermission('client.setting.extension.fee-reset'),
                shouldDisplay   : fees != null,
                label           : 'reset all adjustments',
                title           : "Values are reset to 0. Client can still use custom fees.",
                handleEventClick: handleReset,
            }, 
            {
                hasAccess       : hasPermission('client.extension.fee-delete'),
                shouldDisplay   : fees != null,
                label           : 'delete',
                title           : "Delete client custom fees and use default in settings.",
                handleEventClick: handleDelete,
            }, 
        ];
        
        const permittedActions = actions.filter(action => action.hasAccess && action.shouldDisplay);
    
        return (
            <DropDownContainer
                show={show}
            >
                {
                    permittedActions.length == 0 
                        ?
                            <div
                                className="px-5 py-1 text-danger flex justify-start font-medium"
                            >
                                No Actions Permitted
                            </div>
                        :
                                permittedActions.map(
                                    (action, actionIndex) => 
                                    {
                                        return (
                                            <button
                                                key={actionIndex}
                                                className="px-5 py-1 hover:bg-gray-100 flex justify-start capitalize"
                                                title={action.title}
                                                onClick={action.handleEventClick}
                                            >
                                                {action.label}
                                            </button>
                                        )
                                    }
                            )
                }
            </DropDownContainer>
        );
    };
    return (
        <tr className="hover:bg-gray-100 ">
            <td>
                <label className="flex items-center pl-2 space-x-2">
                    {/* <Checkbox
                        name="user"
                        value="user"
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    /> */}
                    <div className=" text-2xl border p-1 rounded-full">
                        <MdOutlinePersonOutline />
                    </div>
                    <span className=" font-semibold cursor-pointer">
                        {item.name}
                    </span>
                </label>
            </td>
            <td>
                <span>{item.email}</span>
            </td>
            <td>
                {item.is_active ? (
                    <div className="text-green-500 inline-flex space-x-1 items-center">
                        <MdVerifiedUser />
                        <span>Enabled</span>
                    </div>
                ) : (
                    <div className="text-gray-400 inline-flex space-x-1 items-center">
                        <MdOutlineLockPerson />
                        <span>Disabled</span>
                    </div>
                )}
            </td>
            <td>
                {fees ? (
                    <div className="text-green-500 inline-flex space-x-1 items-center">
                        <MdOutlineCheckCircleOutline />
                        <span>Set</span>
                    </div>

                ) : (
                    <div className="text-gray-400 inline-flex space-x-1 items-center">
                        <MdOutlineLockPerson />
                        <span>Not Set</span>

                    </div>
                )}
            </td>
            <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer
                        show={show}
                        className="items-start right-0"
                    >
                        {actionsDropdown()}
                    </DropDownContainer>
                </span>
            </td>
        </tr>
    );
}
