import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import CheckFilter from "@/Components/Util/Filter/CheckFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import TextFilter from "@/Components/Util/Filter/TextFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";

export default function SampleImplementation() {
    const config = {
        container: {
            active: false,
        },
        field: {
            sortby: {
                active: false,
                value: [],
                type: "option",
                items: ["id:desc", "id:asc"],
                name: "Sort By",
            },
            status: {
                active: false,
                value: [],
                type: "check",
                items: ["active", "disabled"],
                name: "Status",
            },
            search: {
                active: false,
                value: [],
                type: "text",
                name: "Search",
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, () => {
        setFilter(offFilter(filter));
    });

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });
        setFilter({
            ...filter,
            container: { ...filter.container, active: false },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        });
    };

    return (
        <div className="flex items-center relative  " ref={ref}>
            <ActiveFilter
                field={field}
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <DisplayFilter
                handleDisplayToggle={handleDisplayToggle}
                container={filter.container}
                field={filter.field}
            />

            <OptionFilter
                fieldProp={field.sortby}
                fieldKey="sortby"
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <CheckFilter
                fieldProp={field.status}
                fieldKey="status"
                handleFieldUpdateValue={handleFieldUpdateValue}
            />
            <TextFilter
                fieldProp={field.search}
                fieldKey="search"
                handleFieldUpdateValue={handleFieldUpdateValue}
                offFilter={() => setFilter(offFilter(filter))}
            />
        </div>
    );
}
