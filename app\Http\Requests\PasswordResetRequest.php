<?php

namespace App\Http\Requests;

use App\Util\Constant\RateLimiterKey;
use App\Util\Helper\RateLimit;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class PasswordResetRequest extends FormRequest
{
    protected $redirect = 'bad-request';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email',
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'Invalid request.',
        ];
    }

    public function checkToken()
    {
        $attempt = 5;
        $decay = 300; // in seconds

        if (RateLimit::attempt(RateLimiterKey::resetPasswordAttempt($this->ip()), $attempt, $decay)) {
            $reset_token = DB::table('password_reset_tokens')->where('email', $this->query('email'))->first();
            if ($reset_token && Hash::check($this->route('token'), $reset_token->token)) {
                $date_creation = Carbon::parse($reset_token->created_at);
                if (! Carbon::now()->greaterThan($date_creation->addMinutes(config('auth.passwords.users.expire')))) {
                    return 'valid';
                }

                return 'invalid';
            }
        }

        return 'limited';
    }
}
