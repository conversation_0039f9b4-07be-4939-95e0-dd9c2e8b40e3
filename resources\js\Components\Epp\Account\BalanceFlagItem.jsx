import { Link, usePage} from "@inertiajs/react";
import React, { useState } from 'react';
import { Alert } from 'evergreen-ui';

const BalanceFlagItem = ({ keyId, registryProps, intentType="danger" }) => {
    const registryArray = JSON.parse(usePage().props.registryArray);

    const user = usePage().props.auth.user;
    const flash = usePage().props.flash;
   
    const getFlashData = (registry, key) => {

        // console.log(registryProps);
        // console.log(registryProps[registry]);
        var registryData = (registryProps[registry] != null) ? JSON.parse(registryProps[registry]) : null;

        // console.log(registryData.id);

        return ((registryData != null) && key == 'id') ? registryData.id :
            ((registryData != null) && key == 'name') ? registryData.name :
                ((registryData != null) && key == 'title') ? registryData.title :
                    ((registryData != null) && key == 'message') ? registryData.message : null;
    }

    return (
        <div className="mx-auto container max-w-[1200px] mt-2 flex flex-col px-5 rounded-lg ">
            {registryArray.map(registry => (
                getFlashData(registry, 'title') &&
                <Link

                    key={keyId + getFlashData(registry, 'id')}
                    href={route("epp.account", { registry_id: getFlashData(registry, 'id') })}
                    method="get"
                >
                    <Alert

                        intent={intentType}
                        title={<span className="text-inherit font-bold">{getFlashData(registry, 'title')}</span>}
                        marginBottom={10}
                    >
                        <span className="text-inherit text-sm">{getFlashData(registry, 'message')}</span>
                    </Alert>
                </Link>
            ))}

        </div>

    )
}

export default BalanceFlagItem;