export const evaluate = (response) => {
    let code = 200;
    let success = true;
    let errorBag = {};

    if (response.status > 299) {
        let { message, errors } = response.data;

        success = false;
        code = response.status;
        if (message != undefined) errorBag.message = message;

        if (errors != undefined && Object.keys(errors).length > 0) {
            Object.keys(errors).forEach((key) => {
                errorBag[key] = errors[key].join(",");
            });
        }

        return {
            code,
            success,
            errors: errorBag,
        };
    }

    return {
        code: response.status,
        success,
        data: response.data,
    };
};
