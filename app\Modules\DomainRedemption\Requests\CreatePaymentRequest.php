<?php

namespace App\Modules\DomainRedemption\Requests;

use App\Modules\DomainRedemption\Services\DomainRedemptionService;
use Illuminate\Foundation\Http\FormRequest;

class CreatePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'ids' => ['required', 'array', 'min:1'],
            'ids.*' => ['required', 'integer'],
            'total_amount' => ['required', 'numeric', 'min:0.01'],
            'valid_until' => ['required', 'date', 'after:today'],
            'note' => ['nullable', 'string', 'max:500']
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'ids.required' => 'At least one domain must be selected.',
            'ids.array' => 'Invalid domain selection format.',
            'ids.min' => 'At least one domain must be selected.',
            'ids.*.required' => 'Each domain ID is required.',
            'ids.*.integer' => 'Each domain ID must be a valid number.',
            'total_amount.required' => 'Total amount is required.',
            'total_amount.numeric' => 'Total amount must be a valid number.',
            'total_amount.min' => 'Total amount must be greater than $0.01.',
            'valid_until.required' => 'Valid until date is required.',
            'valid_until.date' => 'Valid until must be a valid date.',
            'valid_until.after' => 'Valid until date must be in the future.',
            'note.max' => 'Note cannot exceed 500 characters.'
        ];
    }

    public function createPayment(): void
    {
        DomainRedemptionService::instance()->createPaymentWithDetails(
            $this->ids,
            $this->total_amount,
            $this->valid_until,
            $this->note
        );
    }
}
