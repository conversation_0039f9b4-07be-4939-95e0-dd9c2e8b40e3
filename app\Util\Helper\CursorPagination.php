<?php

namespace App\Util\Helper;

class CursorPagination
{
    public static function cursorPaginate(object $cursor)
    {
        return (object) [
            'onFirstPage' => $cursor->onFirstPage(),
            'onLastPage' => $cursor->onLastPage(),
            'nextPageUrl' => $cursor->nextPageUrl(),
            'previousPageUrl' => $cursor->previousPageUrl(),
            'items' => $cursor->items(),
        ];
    }
}
