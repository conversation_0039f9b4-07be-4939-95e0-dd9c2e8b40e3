import { useState } from "react";

import NavLink from "@/Components/NavLink";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MdExpandMore,
    MdOutlineErrorOutline,
    Md<PERSON>ie<PERSON>hart<PERSON>ut<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>d<PERSON><PERSON>,
    MdOutlineAdminPanelSettings,
} from "react-icons/md";
export default function JobNav({ postRouteName }) {
    const routes = {
        queue: route().current("job"),
        failed: route().current("job.failed"),
    };
    const [show, setShow] = useState(Object.values(routes).includes(true));
    const visible = () => {
        return !show ? " hidden" : "";
    };
    return (
        <>  
            <button
                onClick={() => setShow(!show)}
                className="flex items-center justify-between hover:text-gray-900 hover:shadow-sm pl-8 py-1 cursor-pointer"
            >
                <span className=" text-inherit ">Jobs</span>
                {show ? (
                    <MdExpandLess className=" text-3xl pr-2" />
                ) : (
                    <MdExpandMore className=" text-3xl pr-2" />
                )}
            </button>

            <NavLink
                href={route("job")}
                active={routes.queue}
                Icon={<MdList className="text-2xl " />}
                className={visible()}
            >
                <span className=" text-inherit">Queue</span>
            </NavLink>
            <NavLink
                href={route("job.failed")}
                active={routes.failed}
                Icon={<MdOutlineErrorOutline className="text-2xl " />}
                className={visible()}
            >
                <span className=" text-inherit">Failed Job</span>
            </NavLink>
        </>
    );
}
