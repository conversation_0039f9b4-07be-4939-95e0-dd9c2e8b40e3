<?php

namespace App\Modules\Domain\Services;

use App\Modules\Domain\Jobs\DomainEppClientHoldJob;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;
use stdClass;
use App\Modules\Domain\Services\DomainEppClientHoldJobService;
use App\Util\Helper\DomainParser;
use Illuminate\Support\Collection;
use App\Modules\Client\Constants\DomainStatus;
use Illuminate\Support\Facades\Mail;
use App\Mail\ClientHoldMail;
use App\Events\AdminActionEvent;
use App\Modules\AdminHistory\Constants\HistoryType;
use Exception;

class DomainClientHoldServices
{
    private static ?self $instance = null;
    

    public function __construct()
    {
        if (self::$instance === null) {
            self::$instance = $this;
        }
    }

    public static function instance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function updateClientHoldStatus(array|int $domainIds): array
    {
        return $this->updateStatus($domainIds, 'client_hold');
    }

    public function removeClientHoldStatus(array|int $domainIds): array
    {
        return $this->updateStatus($domainIds, 'client_unhold');
    }

    private function updateStatus(array|int $domainIds, string $action): array
    {
        $domains = is_int($domainIds) ? [$domainIds] : $domainIds;
        $domainsData = $this->fetchDomains($domains);

        if ($domainsData->isEmpty()) {
            app(AuthLogger::class)->error("No domains found to process");
            return ['status' => 'error'];
        }

        foreach ($domainsData as $domain) {
            if ($this->shouldProcessDomain($domain, $action)) {
                try {
                    $this->updateDomainStatus($domain, DomainStatus::IN_PROCESS);
                    // $this->sendClientHoldMail($domain, $this->getUserEmail($domain->id), $action);
                    $this->logAdminAction($domains, $action);
                    $this->dispatchEppUpdateJob($domain, $action);
                 } catch (Exception $e) {
                    $this->updateDomainStatus($domain, DomainStatus::ACTIVE);
                    app(AuthLogger::class)->error("Failed to queue client hold status update for domain {$domain->name}: " . $e->getMessage());
                }
            }
        }

        return ['status' => 'success'];

    }

    private function fetchDomains(array $domainIds): Collection
    {
        return DB::client()->table('domains')
            ->whereIn('id', $domainIds)
            ->get();
    }

    private function shouldProcessDomain(stdClass $domain, string $action): bool
    {
        $currentStatus = $this->parseCurrentStatus($domain);
        $hasHoldStatus = $this->hasClientHoldStatus($currentStatus);
        return !($action === 'client_hold' && $hasHoldStatus) && !($action === 'client_unhold' && !$hasHoldStatus);
    }

    private function dispatchEppUpdateJob(stdClass $domain, string $action): void
    {
        $registry = strtoupper(DomainParser::getRegistryName($domain->name));
        
        DomainEppClientHoldJobService::instance()->dispatch([
            'domainId' => $domain->id,
            'domainName' => $domain->name,
            'action' => $action,
            'queue' => "{$registry}-CLIENT-HOLD"
        ]);

        app(AuthLogger::class)->info("Client hold status update queued for domain {$domain->name}. Action: {$action}");
    }

    private function parseCurrentStatus(stdClass $domain): array
    {
        if (empty($domain->client_status)) {
            return [];
        }

        $currentStatus = is_string($domain->client_status)
            ? json_decode($domain->client_status, true)
            : $domain->client_status;

        return array_values(array_filter($currentStatus));
    }

    private function hasClientHoldStatus(array $currentStatus): bool
    {
        return in_array(EppDomainStatus::CLIENT_HOLD, $currentStatus);
    }

    public function updateDomainStatus(stdClass $domain, $domain_status): void
    {
        DB::client()->table('domains')
            ->where('id', $domain->id)
            ->update([
                'status' => $domain_status,
                'updated_at' => now()
            ]);
    }

    private function sendClientHoldMail(stdClass $domain, $email, string $action): void
    {
        $actionCase = strtoupper(str_replace('_', ' ', $action));

        $payload = [
            'subject' => 'Domain Client Hold Status Updated',
            'greeting' => 'Greetings!',
            'body' => "The client hold status for the domain {$domain->name} has been updated to ",
            'body2' => "If you have any questions or concerns, please contact our support team.",
            'text' => "Domain: {$domain->name}",
            'action' => $actionCase,
            'sender' => config('mail.from.sd_name'),
        ];

        Mail::to($email)->send(new ClientHoldMail($payload));
        self::emailTrack($email, $payload);
    }

    private static function emailTrack($email, array $invite){
        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => null,
                'name' => $email,
                'recipient_email' => $email,
                'subject' => 'Domain Client Hold Status Updated',
                'email_type' => 'Domain Client Hold',
                'email_body' => json_encode($invite),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
        return $emailSent;
    }

    public function logAdminAction(array $domainIds, string $action): void
    {
        $domainNames = $this->getDomainNames($domainIds);
        $formattedAction = $this->formatActionName($action);
        event(new AdminActionEvent( auth()->user()->id, HistoryType::DOMAIN_UPDATE, "Domains have been set to {$formattedAction}: " . implode(', ', $domainNames) . ' by ' . auth()->user()->email ));
    }

    private function formatActionName(string $action): string
    {
        return match($action) {
            'client_hold' => 'Client Hold',
            'client_unhold' => 'Client Unhold',
            default => ucwords(str_replace('_', ' ', $action))
        };
    }

    private function getDomainNames(array $domainIds): array
    {
        return DB::client()->table('domains')
            ->whereIn('id', $domainIds)
            ->pluck('name')
            ->toArray();
    }

    private function getUserEmail($domainId)
    {
        $user = $this->getUser($domainId);
        return $user->email;
    }

    private function getUser($domainId)
    {
        return DB::client()->table('registered_domains')
            ->select('users.email')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

}